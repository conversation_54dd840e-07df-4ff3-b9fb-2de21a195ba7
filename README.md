## APP后端，APP管理后端，以及OMS前端

#### 架构

 - `controller`使用struts,配置文件位置`src/main/resources/com/petkit/api/conf/struts/struts.xml`
 - 使用`Spring`做为依赖注入管理
 - 数据库层可以使用`mybatis`或者其定义的`JdbcTemplate`
    - `mybatis`的xml文件需放在如下目录`src/main/resources/config/mapper`
    - 使用自定义`JdbcTemplate`参考`FeederRepositoryImp`

### 如何使用异步处理

 - 异步处理建议使用`TaskManager`,使用方法参考已有代码`FeederServiceImp`

### 如何使用事务

   - 普通事务，可以使用`org.springframework.transaction.annotation.Transactional`注解
   - 跨库事务，建议使用`TransactionService::doTransactionWork`,参考已有代码`FeederServiceImp`
 
### 如何使用缓存

 - 推荐使用
   - redis配置文件在数据库`api_config`表中,`id=mcache.kv`
   - 创建`src/main/resources/com/petkit/api/conf/cache/demo.ini`,描述版本和key的效期,参考`FeederServiceImp`
   - 使用`KvCacheFactory.create`创建一个`KvCacheWrap`,参考`FeederServiceImp`
   - `create`方法的第一个参数`namespace`对应`src/main/resources/com/petkit/api/conf/cache/demo.ini`其文件名
 - 或者使用简化版`Jedis jedis = KvCacheFactory.getRedis();`从jedisPool中获取一个实例,记得用完close

### 如何发送IM消息

 - 发送消息给设备
   参考`CozyMessageSender`的实现
    
 - 发送消息给APP
   1. 创建一个类继承`SystemNotification`
   2. 重写`getSnapshot`，该方法返回的内容一般用于APP在通知栏显示
   3. 重写`getType`方法，该方法是该消息类型的唯一标识,APP根据Type不同做不同的处理
   4. 可以重写`getFrom`方法，实现作为一个普通APP用户发送给另一个用户,比如用户私聊，默认是系统发送
   4. 消息内容，通过在`playload` PUT 数据实现
   5. 使用`NotificationService::sendSystemMessage(userId, notification)`实现发送给相应用户消息
   6. 一般使用异步方式发送消息
   7. 参考`CozyFaultNotification`

### 如何使用定时任务

 - 在`src/task/java/com/petkit/task/`创建类继承`BlockRepeatTask`
 - 根据需求实现`repeatWork`,`beforeWork`,`afterWork`方法
 - 定时任务执行周期配置, `src/task/resources/com/petkit/task/conf/tasks.ini`,第一个参数为完整类名，第二个参数可以为固定时间点，或者固定间隔


### 升级所需文件

 - 数据库表结构变更，创建/删除表，在`src/update/resources/com/petkit/api/updater/resources/`下创建sql文件，执行文件参考`Update20180203DeviceChipId.java`
 - 系统配置文件变更，参考`Update20180228ApiConfigs.java`
 - 字串变更,参考`Update20171221FeederShareLocaleBundle.java`
 
### oms前端页面修改

  - 修改文件夹`oms/src`相应文件即可
  
### 权限
  
   - `@SessionRequired`标识允许用户，管理员，设备才能访问这个API,方法注解优先级高于类注解
   - `@AdminPermission`为该API接口的权限描述,用于oms配置权限
   - `@AdminPermissionGroup`中`key`对应`resources/com/petkit/api/conf/permission/zh_CN.ini`,为该权限组的名称
   - `@DeviceSessionRequired`标识只用该类型的设备(Feeder,Cozy)才能访问这个API

### 其他

   - `MiscInterceptor.java`为系统的全局处理类,所有API的入口和出口
   - 表`api_config`中所有的配置项，可以通过`G.java`获得,例如`G.getConfig().getString("mail")`
   - 模拟APP访问接口,请求头必须包含`x-session`，值对应表`user_session`
   - 模拟设备访问接口,请求头必须包含`X-Device`,值为`id=100001&nonce=xIAdXi9tkGy1&timestamp=946684806&type=feeder&sign=951d3fc85b1b450485f9536d8e3aa898`
   `once`为随机值,`type`为设备类型,`sign`的值参考`SignUtil`的加密算法,为`id100001noncexIAdXi9tkGy1timestamp946684806typefeeder8b0e4c8be2d652d7`的md5值
   - 国内设备ID建议从100000开始自增，国外设备ID建议从100000000开始自增，以避免设备迁移导致的ID冲突问题
  
### IoT 框架

#### 说明

IoT 框架解决将 http 接口转换为 mq 接口的问题，不用改 Service 层，简化了开发。
MVC 的 c 层由 http 转换为 MQ。

整体思路是，
1. 启动 IoTConsumer 接收消息，将接收的消息丢到通过 IoTContainer 放到同步队列中中；
1. IoTTaskExecutor 从阻塞队列中读取消息，将消息转换为任务（设置当前任务的Session、转换 type）
1. 再放入线程池中执行任务

#### 性能

经过测试，一秒可以读取约 1W 条消息。

#### 包结构

com.petkit.api.base.iot

- common 公共
- consumer 从 iot 接受的消息
- core 核心
- model 对象
- repository 
- util 工具

IoTConsumer 是消息接收者
FeederMiniIoTTask
IoTInterceptor 任务执行等同 MVCInterceptor
IoTSession 添加 Session，为 Service 服务
IoTTask IoT 任务
IoTTaskContainer IoT 任务容器，容器具体是阻塞队列
IoTTaskExecutor IoT 任务执行器，利用线程池执行

### 离线通知

#### 说明

喂食器、猫窝、喂食器 Mini 离线通知，每隔 ${minutes} 查询在 ${start} 到 ${end} 之间离线的，之后没有上下线记录的设备。
现在的版本为重构之后的，将三个离线通知整合在一起了。

#### 包结构

com.petkit.task.offline  

com.petkit.task.offline.AbstractOfflineEventPushTask 离线通知抽象类，定义了如何查询离线通知

#### 算法

需要通知的人 = 在某段时间内有上下线记录的 - 之后没有上下线记录的 - 之后在线的

因为 pimserver 插入数据不准确，之后没有上下线记录的、之后在线的可以有 1 s 误差

#### 注意

task 增加了 Redis 进行存储，注意及时关闭 Redis 连接。

### 其他的一些文档（升级、服务器访问脚本）

https://code.petkit.com/server-app/petkit-api-doc

### 其他的一些文档（接口）

https://code.petkit.com/server-app/petkit-doc

### 升级失败

可能是磁盘空间满
df -m 查看硬盘剩余空间
du -sh 查看空间