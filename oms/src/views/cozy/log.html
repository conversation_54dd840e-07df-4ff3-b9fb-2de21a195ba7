<script>
    // function getLocationParams() {
    //     let hash = location.hash.split('?');
    //     let params = hash[hash.length - 1];
    //     params = params.split('&');

    //     let result = {}
    //     params.map(v => {
    //         let _param = v.split('=');

    //         if (result[_param[0]]) {
    //             if (Object.prototype.toString.call(result[_param[0]]) === '[object Array]') {
    //                 result[_param[0]] = result[_param[0]].push(_param[1]);
    //             } else {
    //                 result[_param[0]] = [result[_param[0]], _param[1]];
    //             }
    //         } else {
    //             result[_param[0]] = _param[1];
    //         }

    //     })
    //     return result;
    // }
    // App.ready(function () {
    //     var ifr = document.getElementById('iframe');
    //     ifr.src = '/device/z1/log?X-Admin-Session=' + $.cookie('sess') + '&search=' + (getLocationParams()['search'] || '');
    // });
    App.ready(function () {
        var hashSearch = App.router.getHashSearch();
        var token = $.cookie('sess');
        localStorage.setItem('sessionToken', token);
        var ifrSrc = '/oms/device/cozy/logs';
        var ifrUrl = hashSearch ? (ifrSrc + '?' + hashSearch) : ifrSrc;
        var ifr = document.getElementById('iframe');
        ifr.src = ifrUrl;
    });
</script>

<div style="height: calc(100vh - 60px);">
    <iframe id="iframe" frameborder="0" width="100%" height="100%"></iframe>
</div>