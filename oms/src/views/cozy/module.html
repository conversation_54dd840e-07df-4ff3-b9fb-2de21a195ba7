<script>
    App.ready(function () {
        // var ifr = document.getElementById('iframe');
        // ifr.src = '/device/z1/module?X-Admin-Session=' + $.cookie('sess');
        var hashSearch = App.router.getHashSearch();
        var token = $.cookie('sess');
        localStorage.setItem('sessionToken', token);
        var ifrSrc = '/oms/device/cozy/modules';
        var ifrUrl = hashSearch ? (ifrSrc + '?' + hashSearch) : ifrSrc;
        var ifr = document.getElementById('iframe');
        ifr.src = ifrUrl;
    });
</script>

<div style="height: calc(100vh - 60px);">
    <iframe id="iframe" frameborder="0" width="100%" height="100%"></iframe>
</div>