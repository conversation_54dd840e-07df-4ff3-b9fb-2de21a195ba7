<script>
	App.ready(function() {

		App.generator.generatePage('/oms/schedule/type/list', '/schedule/types');

		// window.disable = function (id,isCustom) {
		// 	if(isCustom == 1){
		// 		alert("自定义类型不允许禁用！")
		// 	}
		// 	var data = {};
		// 	data['enable'] = 0;
		// 	App.api('/schedule/type_enable', {
		// 		'id': id,
		// 		'doc': JSON.stringify(data)
		// 	}).then(function () {
		// 		request();
		// 	});
		// };

		// window.enable = function (id) {
		// 	var data = {};
		// 	data['enable'] = 1;
		// 	App.api('/schedule/type_enable', {
		// 		'id': id,
		// 		'doc': JSON.stringify(data)
		// 	}).then(function () {
		// 		request();
		// 	});
		// };


		// var request = function() {
		// 	App.scrollToTop();
		// 	App.api('/schedule/types').then(function(list) {
		// 		var table = $('#the-table');
		// 		var template = table.attr('data-template');
		// 		if (!template) {
		// 			template = '<tr>' + table.find('tr.template').html() + '</tr>';
		// 			table.attr('data-template', template);
		// 		}
		// 		table.find('tr:not(.header)').remove();
		// 		for (var i = 0; i < list.length; i++) {
		// 			var context = list[i];
		// 			if (context.img) {
		// 				context._img = U.template('<a href="{{img}}" target="_blank"><img src="{{img}}"/></a>', context);
		// 			}
		// 			context._withPet = App.text(context.withPet > 0 ? 'yes' : 'no');
		// 			context._withDeviceType = App.text(context.withDeviceType > '-1' ? context.withDeviceType : '否');
		// 			context._withDeviceType = App.text(context.withDeviceType != '0' ? context._withDeviceType : '所有类型');
		// 			context._withDeviceType = App.text(context.withDeviceType != '0' ? context._withDeviceType : '所有类型');
		// 			context._scheduleAppoint = App.text(context.scheduleAppoint == null ? '无' : context.scheduleAppoint);
		// 			context._enable = context.enable === 1 ? "已启用" : "未启用";
		// 			var row = U.template(template, context);
		// 			table.append(row);
		// 		}
		// 	});
		// };
		// var init = function() {
        //     $('#export-btn').attr('href', App.apiPath('/schedule/type_string_out', App.specialApiParams()));
        //     $('#export-btn').attr('target', '_blank');
    	// 	request();
		// };
		// init();
	});
</script>
<div style="height: calc(100vh - 60px);">
	<iframe id="iframe" frameborder="0" width="100%" height="100%"></iframe>
</div>
<!-- <div class="panel panel-default x-panel">
	<div class="panel-heading">
		<a href="#/schedule/type" class="btn btn-success" data-l10n-id="create"></a>
        <a href="javascript:;" id="export-btn" class="btn btn-info">导出字串表</a>
        <a href="#/schedule/type_string_in" id="import-btn" class="btn btn-warning">导入字串表</a>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 20%">名称</th>
				<th style="width: 10%">图片</th>
				<th style="width: 10%">关联设备类型</th>
				<th style="width: 10%">关联宠物</th>
				<th style="width: 10%">约定提醒</th>
				<th style="width: 10%">优先级</th>
				<th style="width: 8%">是否启用</th>
				<th style="width: 10%">操作</th>
			</tr>
			<tr class="template">
				<td>{{name}}</td>
				<td>{{_img}}</td>
				<td>{{_withDeviceType}}</td>
				<td>{{_withPet}}</td>
				<td>{{_scheduleAppoint}}</td>
				<td>{{priority}}</td>
				<td>{{_enable}}</td>
				<td class="operations">

					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li>
								<a href="#/schedule/type?id={{id}}">编辑</a>
							</li>
							<li data-op-display="enable{{enable}}">
								<a href="javascript:enable('{{id}}');">启用</a>
							</li>
							<li data-op-display="disable{{enable}}">
								<a href="javascript:disable('{{id}}','{{isCustom}}');">禁用</a>
							</li>

						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div> -->