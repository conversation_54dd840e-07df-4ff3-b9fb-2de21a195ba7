<style>
    #img-preview img {
        width: 80px;
    }
</style>
<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script src="js/localesaver.js"></script>
<script>
App.ready(function () {
        App.menu.select('/schedule/types');
        var form = $('#save-form');
        var localeSaver = new LocaleSaver({'dialogUrl': '/schedule/type_localespec.html'});
        var previewImg = function (url) {
            if (url) {
                $('#img-preview').html('<img src="' + url + '"/>');
            } else {
                $('#img-preview').html('');
            }
            form.find('[name="img"]').val(url ? url : '');
        };
        var save = function () {
            var data = form.serializeObject();
            data._localizedProps = localeSaver.getDataAsModel();
            App.api('/schedule/type_save', {'type': JSON.stringify(data)}, {
                success: function () {
                    App.router.go('/schedule/types');
                }
            });
        };
        var loadModel = function () {
            var id = App.router.getParameter('id');
            if (!id) {
                return;
            }
            return App.api('/schedule/type', {'id': id}, {
                success: function (model) {
                    if ($.isEmptyObject(model)) {
                        alert('Bad request');
                        return false;
                    }
                    var context = {'model': model};
                    App.renderPage(context);
                    previewImg(model.img);
                    if (model._localizedProps) {
                    	localeSaver.setDataByModel(model._localizedProps, ['name','msg0','msg1','msg2','msg3'])
                    }
                }
            });
        };
        var init = function() {
            App.upload.init({
                namespace: 'schedule',
                type: 'image',
                id: 'upload-btn',
                fileUploaded: function (info) {
                    previewImg(info.url);
                },
                error: function (errTip) {
                    alert('文件上传出错' + errTip);
                }
            });
            $.when(localeSaver.init(), loadModel()).then(function () {
                $('#save-btn').click(save);
            });
        };
        init();
    });
</script>
<form id="save-form" role="form" onsubmit="return false;">
    <input type="hidden" name="id" ng-model="model.id"/>
    <div class="form-group">
        <label data-l10n-id="image"></label>

        <div class="controls">
            <input type="hidden" name="img" ng-model="model.img"/>
            <div>
                <a href="javascript:;" id="upload-btn">+选择上传文件</a>
            </div>
            <div id="img-preview"></div>
        </div>
    </div>
    <div class="form-group">
        <label>关联设备类型(-1:不绑定 0:所有类型 设备类型id逗号隔开)：</label>
        <input type="text" name="withDeviceType" ng-model="model.withDeviceType" class="form-control"/>
    </div>
    <div class="form-group">
        <label>关联宠物</label>
        <select name="withPet" ng-model="model.withPet" class="form-control">
            <option value="0" data-l10n-id="no"></option>
            <option value="1" data-l10n-id="yes"></option>
        </select>
    </div>
    <div class="form-group">
        <label>约定提醒</label>
        <input type="text" name="scheduleAppoint" class="form-control" ng-model="model.scheduleAppoint"/>
    </div>
    <div class="form-group">
        <label>优先级(0-99999，越高表示排在越前面)：</label>
        <input type="text" name="priority" class="form-control" ng-model="model.priority"/>
    </div>

    <div class="form-group">
        <label>是否是自定义</label>
        <select name="isCustom" ng-model="model.isCustom" class="form-control">
            <option value="0" data-l10n-id="no"></option>
            <option value="1" data-l10n-id="yes"></option>
        </select>
    </div>

    <div class="form-group">
        <label>是否启用：</label>
        <select name="enable" class="form-control" ng-model="model.enable">
            <option value="1">是</option>
            <option value="0">否</option>
        </select>
    </div>
    
    <div class="form-group">
        <div class="controls">
            <a href="javascript:;" id="local-add-btn">
                <span>+</span>
                <span data-l10n-id="localize"></span>
            </a>
            <div id="locale-spec-template">
                <div class="locale-spec">
                    <div class="locale-spec-item">
                        <label data-l10n-id="locale"></label>:
                        <span class="locale">{{locale.name}}</span>
                        <a href="javascript:;" class="edit" data-l10n-id="edit" data-op-type="update" data-op-key="{{locale.key}}"></a>
                        <a href="javascript:;" class="edit" data-l10n-id="delete" data-op-type="remove" data-op-key="{{locale.key}}"></a>
                    </div>
                    <div class="locale-spec-item">
                    	<label>名称</label>:<span>{{spec.name}}</span><br/>
	                    <label>提醒消息</label>:<span>{{spec.msg0}}</span><br/>
	                    <label>过期消息(第1天)</label>:<span>{{spec.msg1}}</span><br/>
	                    <label>过期消息(第2天)</label>:<span>{{spec.msg2}}</span><br/>
	                    <label>过期消息(第3天)</label>:<span>{{spec.msg3}}</span><br/>
                    </div>
                </div>
            </div>
            <div id="locale-specs"></div>
        </div>
    </div>
   
    <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
    <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
</form>
