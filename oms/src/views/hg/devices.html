<script>
    App.ready(function () {
        var hashSearch = App.router.getHashSearch();
        var token = $.cookie('sess');
        localStorage.setItem('sessionToken', token);
        var ifrSrc = '/oms/device/hg/bluetooth/list';
        var ifrUrl = hashSearch ? (ifrSrc + '?' + hashSearch) : ifrSrc;
        var ifr = document.getElementById('iframe');
        ifr.src = ifrUrl;
    });
</script>

<div style="height: calc(100vh - 60px);">
    <iframe id="iframe" frameborder="0" width="100%" height="100%"></iframe>
</div>
