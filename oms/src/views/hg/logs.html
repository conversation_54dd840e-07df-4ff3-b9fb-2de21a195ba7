<script>
	App.ready(function() {
		var requestParams;
		var request = function(offset) {
			App.scrollToTop();
			if (!U.isNull(offset)) {
				requestParams.offset = offset;
			}
			if (!requestParams.sn) {
				return;
			}
			App.api('/feeder/logs', requestParams).then(function(ps){
				var items = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for ( var i = 0; i < items.length; i++) {
					var context = items[i];
					context.createdAt = U.date.format(U.date.parse(context.createdAt), 'MM-dd HH:mm');
					context._src = (context.src === 1) ? 'App' : '设备';
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
			});
		};
		var submitIt = function() {
	        requestParams = $('#search-form').serializeObject();
	        request(0);
		};
		var init = function() {
			$('#search-btn').click(submitIt);
			$("#search-key").keydown(function(e){
			    　if(e.keyCode == 13) {
			    	submitIt();
			    　}
			});
			submitIt();
		};
		init();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
			<input type="hidden" name="limit" ng-model="REQUEST.limit"/>
			<input id="search-key" type="text" class="form-control" name="sn" ng-model="REQUEST.sn" placeholder="设备SN"/>
			<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 20%">上传日期</th>
				<th style="width: 20%">来自</th>
				<th style="width: 60%">内容</th>
			</tr>
			<tr class="template">
				<td>{{createdAt}}</td>
				<td>{{_src}}</td>
				<td>{{log}}</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>