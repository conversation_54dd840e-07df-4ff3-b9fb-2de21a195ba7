<script>
	App.ready(function () {
		App.menu.select('/push/smart_push_feedermini');
		const monthHour = 24 * 30;
		const weekHour = 24 * 7;
		const dayHour = 24;
		var remove = function (id) {
			if (!confirm(App.text('confirm.delete'))) {
				return;
			}
			App.api('/push/removeSpRule', {
				'id': id
			}).then(function () {
				window.location.reload();
			});
		};

		var push = function (id) {
			App.router.go('/community/push?type=text&resourceId=' + id);
		};

		var edit = function (id) {
			App.router.go('/push/smart_push_rule?ruleId=' + id);
		};

		var checkContent = function (id) {
			App.router.go('/push/smart_push?resourceId=' + id);
		};



		window.checkContent = checkContent;
		window.edit = edit;
		window.push = push;
		window.disable = function (id) {
			var data = {};
			data['status'] = 0;
			App.api('/push/updateRule', {
				'ruleId': id,
				'doc': JSON.stringify(data)
			}).then(function (result) {
				request();
			});
		};
		window.enable = function (id) {
			var data = {};
			data['status'] = 1;
			App.api('/push/updateRule', {
				'ruleId': id,
				'doc': JSON.stringify(data)
			}).then(function (result) {
				request();
			});
		};


		var showLocalizedString = function (map) {
			var s = '';
			if (!map) {
				return s;
			}
			var i = 0;
			for (var locale in map) {
				if (i > 0) {
					s += '<br/><br/>';
				}
				s += locale + ":";
				s += '<br/>';
				s += map[locale];
				i++;
			}
			return s;
		};

		var request = function (offset) {
			App.scrollToTop();
			var params = $.extend({
				'limit': 10
			}, App.router.getParameters());
			params.offset = (!U.isNull(offset)) ? offset : params.offset;
			params.type = 6;
			App.api('/push/smartPushRulesByType', params).then(function (ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					// context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');

					var time = context.duration;
					var timeStr = '';
					if (time >= monthHour && time % monthHour == 0) {
						timeStr = time / monthHour + "个月";
					} else if (time >= weekHour && time % weekHour == 0) {
						timeStr = time / weekHour + "星期";
					} else if (time >= dayHour && time % dayHour == 0) {
						timeStr = time / dayHour + "天";;
					} else {
						timeStr = time + "小时";;
					}
					context._rule = "事件：" + context.eventName + "&nbsp;&nbsp;<br/>" + "时间：" + timeStr + "&nbsp;&nbsp;<br/>" + "过滤条件：" + (!context.filterName ? "无" : context.filterName) + "&nbsp;&nbsp;<br/>";
					context._title = "<a href=\"javascript:checkContent('" + context.contentId + "');\">" + context.title + "</a>";
					context._status = context.status == 0 ? "未启用" : "启用";
					context.id = context.ruleId;
				
					table.attr('data-template', template);
					var row = U.template(template, context);
					table.append(row);
				}
				var hideKeys = ['enable1', 'disable0'];
				$.each(hideKeys, function (index, item) {
					$('[data-op-display=' + item + ']').addClass('none');
				});
				App.pagination(ps, request);
			});
		};
		var init = function () {
			request();
			//init buttons
			window.remove = remove;
		};

		init();
	});
</script>
<style>
	#the-table img.plink-img {
		width: 100px;
		height: auto;
	}
</style>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" onsubmit="return false;">
			<a href="#/push/smart_pushes?deviceType=6" class="btn btn-primary">内容</a>
			<a href="#/push/smart_push_rule?deviceType=6" id="ptext" class="btn btn-success">创建规则</a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 40%">规则</th>
				<th style="width: 40%">内容</th>
				<th style="width: 10%">状态</th>
				<th style="width: 10%">操作</th>
			</tr>
			<tr class="template">
				<td>{{_rule}}</td>
				<td>{{_title}}</td>
				<td>{{_status}}</td>
				<td class="operations">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li data-op-display="enable{{status}}">
								<a href="javascript:enable('{{id}}');">启用</a>
							</li>
							<li data-op-display="disable{{status}}">
								<a href="javascript:disable('{{id}}');">禁用</a>
							</li>
							<li>
								<a href="javascript:edit('{{id}}');">编辑</a>
							</li>
							<li>
								<a href="javascript:remove('{{id}}');" data-l10n-id="delete"></a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>

</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>