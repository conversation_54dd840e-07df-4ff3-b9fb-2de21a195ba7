<script src="js/localespec.js"></script>
<script src="js/qiniu.min.js"></script>
<script src="js/smart-device-select.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>

<script>
    App.ready(function () {
        App.menu.select('/push/smart_pushes');

        var form = $('#save-form');
        var model;
        var previewImage = function (url) {
            if (url) {
                $('#img-preview').html('<img src="' + url + '"/>');
            } else {
                $('#img-preview').html('');
            }
            form.find('[name="imgUrl"]').val(url);
        };

        App.upload.init({
            namespace: 'smartpush',
            type: 'image',
            id: 'upload-btn',
            fileUploaded: function (info) {
                if (typeof info === 'string') {
                    info = JSON.parse(info);
                }
                previewImage(info.url);
            },
            error: function (errTip) {
                alert('文件上传出错' + errTip);
            }
        });

        window.addLocaleSpec = function (locale) {
            var spec = LocaleSpec.get(locale);
            App.openDialog({
                url: App.router.getRealPath('/community/plinklocalespec.html'),
                context: {
                    spec: spec
                },
                noHeader: true,
                primaryClick: LocaleSpec.save,
                beforeRender: function (body) {
                    var select = body.find('[name="locale"]');
                    $.each(LocaleSpec.getLocaleList(), function (index, item) {
                        select.append('<option value="' + item.key + '">'
                            + item.name + '</option>');
                    });
                }
            });
        };

        var save = function () {
            var data = form.serializeObject();
            var specs = LocaleSpec.getAllByKeys();
            for (var key in specs) {
                var spec = specs[key];
                data[key] = spec;
            }

            if (model != null) {
                data['contentId'] = model['contentId'];
            }
            if (data['title'] === "") {
                alert("请填写标题");
                return false;
            }

            if (data['url'] !== "" && data['imgUrl'] === "") {
                alert("URL必须与配图共同使用");
                return false;
            }
            var deviceTypes = [];
            for (var key in devices) {
                var device = devices[key];
                var v = device.getValue();
                if (!v) {
                    alert(App.text('p.community.push.please.select.device'));
                    return false;
                }
                deviceTypes.push(v);
            }
            data["deviceTypes"] = deviceTypes;
            App.api('/push/saveSpContent', {
                'model': JSON.stringify(data)
            }, {
                success: function () {
                    App.router.go('/push/smart_pushes');
                }
            });
        };

        var renderModel = function (model) {
            App.renderPage({
                'model': model
            });
            previewImage(model.imgUrl);
            var localizedKeys = ['body'];
            var localizedProps = {};
            for (var i = 0; i < localizedKeys.length; i++) {
                var key = localizedKeys[i];
                localizedProps[key] = model[key];
            }
            LocaleSpec.setAllByKeys(localizedProps);
        };
        var deviceOffset = 0;
        var devices = {};
        var addDevice = function (deviceType) {

            deviceOffset++;
            var key = '' + deviceOffset;
            var template = $('#recipient-device-template').html();
            var prefix = 'devicekey-' + deviceOffset;
            var selectId = "#" + prefix + '-s1';
            var html = U.string
                .replaceAll(template, 'devicekey-', prefix + '-');
            var dom = $(html.trim());
            $('#recipient-device-container').append(dom);
            dom.on('click', '.remove-btn', key, function (e) {
                if (deviceType !== 0) {
                    alert("已关联的设备类型无法删除");
                    return;
                }
                var key = e.data;
                delete devices[key];
                $('#devicekey-' + e.data + '-row').remove();
            });
            var select = new DeviceSelect(prefix);
            select.init().done(function () {
                if (deviceType !== 0) {
                    select.setValue(deviceType);
                    $(selectId).prop("disabled", true);
                }
            });
            if (deviceType !== 0) {
                select.setValue(deviceType);
            } else {
                devices[key] = select;
            }

        };

        var initContent = function () {
            var resourceId = App.router.getParameter('resourceId');
            return App.api('/push/spcontent', {
                'id': resourceId
            }).then(function (result) {
                model = result;
                renderModel(model);
                if (model.deviceTypes) {
                    for (var i = 0; i < model.deviceTypes.length; i++) {
                        var deviceType = model.deviceTypes[i];
                        addDevice(deviceType)
                    }
                }
            });
        };
        //init
        LocaleSpec.loadLocales().then(function () {
            if (App.router.getParameter('resourceId')) {
                initContent();
            }
            $('#save-btn').click(save);
            $('#add-locale-btn').click(addLocaleSpec);
            $('#recipient-device-add-btn').click(function () {
                addDevice(0);
            });

        });
    });
</script>
<style type="text/css">
    #img-preview img {
        max-width: 200px;
    }
</style>

<form id="save-form" role="form" onsubmit="return false;">
    <input type="hidden" name="imgUrl" ng-model="model.imgUrl"/>
    <div class="form-group">
        <label>标题(必填)</label> <input type="text" name="title" class="form-control" ng-model="model.title"/>
    </div>
    <div class="form-group">
        <label>URL(可选)</label> <input type="text" name="url" class="form-control" ng-model="model.url"/>
    </div>
    <div class="form-group">
        <label>配图(可选)</label>
        <div class="controls">
            <div>
                <a href="javascript:;" id="upload-btn">+选择上传文件</a>
            </div>
            <div id="img-preview"></div>
        </div>
    </div>
    <div class="form-group">
        <div class="controls">
            <a href="javascript:;" id="add-locale-btn"><span>+</span><span data-l10n-id="localize"></span>(必填)</a>
            <div id="locale-spec-template">
                <div class="locale-spec" data-locale="{{locale.key}}">
                    <div class="locale-spec-item">
                        <label data-l10n-id="locale"></label>:<span class="locale">{{locale.name}}</span> <a
                            href="javascript:addLocaleSpec('{{locale.key}}');" class="edit" data-l10n-id="edit"></a> <a
                            href="javascript:LocaleSpec.remove('{{locale.key}}');" class="edit"
                            data-l10n-id="delete"></a>
                    </div>
                    <div class="locale-spec-item">
                        <span>{{spec.body}}</span>
                    </div>
                </div>
            </div>
            <div id="locale-specs"></div>
        </div>
    </div>
    <div class="form-group">
        <a href="javascript:;" id="recipient-device-add-btn" class="btn btn-success"
           data-l10n-id="p.community.add.device"></a>
        <div class="controls" id="recipient-device-container"></div>
    </div>
    <div id="recipient-device-template" class="none">
        <div class="region-select-container" id="devicekey-row">
            <select class="form-control" id="devicekey-s1" data-device-1="1"></select>
            <a href="javascript:;" class="remove-btn" data-l10n-id="delete"></a>
        </div>
    </div>

    <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
    <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
</form>