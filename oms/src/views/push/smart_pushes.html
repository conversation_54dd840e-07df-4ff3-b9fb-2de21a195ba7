<script>
    App.ready(function () {
        var remove = function (id) {
            if (!confirm(App.text('confirm.delete'))) {
                return;
            }
            App.api('/push/removeSpContent', {
                'id': id
            }).then(function () {
                window.location.reload();
            });
        };
        App.menu.select('/push/smart_pushes');

        window.usePush = function (id, deviceType) {
            if (deviceType == null || deviceType === '') {
                alert("请先将该内容关联设备");
                return;
            }
            App.router.go('/push/smart_push_rule?contentId=' + id + "&deviceType=" + deviceType);
        };
        window.edit = function (id) {
            App.router.go('/push/smart_push?resourceId=' + id);
        };

        window.push = function (id) {
            App.router.go('/community/push?type=text&resourceId=' + id);
        };
        var deviceType;
        var showLocalizedString = function (map) {
            var s = '';
            if (!map) {
                return s;
            }
            var i = 0;
            for (var locale in map) {
                if (i > 0) {
                    s += '<br/><br/>';
                }
                s += locale + ":";
                s += '<br/>';
                s += map[locale];
                i++;
            }
            return s;
        };
        var request = function (offset, type) {
            if (!U.isNull(type)) {
                deviceType = type;
            }
            App.scrollToTop();
            var params = $.extend({
                'limit': 10
            }, App.router.getParameters());
            params.offset = (!U.isNull(offset)) ? offset : params.offset;
            params.deviceType = deviceType;
            App.api('/push/spcontents', params).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>' + table.find('tr.template').html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
                    context._body = showLocalizedString(context.body);
                    context._title = context.title;
                    context.id = context.contentId;
                    if (context.deviceTypes != null && context.deviceTypes.length > 0) {
                        context.deviceType = context.deviceTypes[0];
                    }
                    context._deviceNames = context.deviceNames;
                    if (context.url !== undefined) {
                        context._url = '<a href="' + context.url + '" target="_blank">' + context.url + '</a>';
                    }
                    var icon = App.previewImgUrl(context.imgUrl, 120, 120);
                    if (!icon) {
                        context._img = null;
                    } else {
                        context._img = '<img class="plink-img" src="' + icon + '">';
                    }
                    var row = U.template(template, context);
                    table.append(row);
                }
                App.pagination(ps, request);
                $('.btn-primary').removeClass("active");
                switch (deviceType) {
                    case 4:
                        $('#feeder').addClass("active");
                        break;
                    case 5:
                        $('#cozy').addClass("active");
                        break;
                    case 6:
                        $('#feedermini').addClass("active");
                        break;
                    default:
                        $('#all_device').addClass("active");
                }

            });
        };
        window.request = request;
        var init = function () {
            deviceType = App.router.getParameter('deviceType');
            switch (deviceType) {
                case '4':
                    $('#feeder')[0].click();
                    break;
                case '6':
                    $('#feedermini')[0].click();
                    break;
                case '5':
                    $('#cozy')[0].click();
                    break;
                default:
                    $('#all_device')[0].click();
            }
            window.remove = remove;
        };

        init();
    });
</script>
<style>
    #the-table img {
        width: 100px;
        height: auto;
    }
</style>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form class="form-inline" action="#" onsubmit="return false;">
            <a href="javascript:request(null,'');" id="all_device" class="btn btn-primary">全部设备</a>
            <a href="javascript:request(null,4);" id="feeder" class="btn btn-primary">喂食器</a>
            <a href="javascript:request(null,6);" id="feedermini" class="btn btn-primary">喂食器Mini</a>
            <a href="javascript:request(null,5);" id="cozy" class="btn btn-primary">宠物窝</a>
            <a href="#/push/smart_push" class="btn btn-success">创建</a>

        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 15%">更新时间</th>
                <th style="width: 15%">标题</th>
                <th style="width: 15%">配图</th>
                <th style="width: 15%">链接</th>
                <th style="width: 20%">内容</th>
                <th style="width: 10%">关联设备</th>
                <th style="width: 10%">操作</th>
            </tr>
            <tr class="template">
                <td>{{_createdAt}}</td>
                <td>{{_title}}</td>
                <td>{{_img}}</td>
                <td>{{_url}}</td>
                <td>{{_body}}</td>
                <td>{{_deviceNames}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:edit('{{id}}');">编辑</a>
                            </li>
                            <li>
                                <a href="javascript:usePush('{{id}}','{{deviceType}}');">使用</a>
                            </li>
                            <li>
                                <a href="javascript:remove('{{id}}');" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>