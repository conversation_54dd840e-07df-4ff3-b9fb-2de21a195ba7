<script src="js/localespec.js"></script>
<script src="js/qiniu.min.js"></script>
<script src="js/value-select.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>

<script>
    App.ready(function () {
        var form = $('#save-form');
        const monthHour = 24 * 30;
        const weekHour = 24 * 7;
        const dayHour = 24;
        var save = function () {
            var data = {};
            data['ruleId'] = ruleId;
            data['eventId'] = eventId;
            data['contentId'] = contentId;
            data['deviceType'] = deviceType;
            var time;
            if (timeUnitId === 1) {
                time = timeValueId;
            } else if (timeUnitId === 2) {
                time = timeValueId * dayHour
            }
            else if (timeUnitId === 3) {
                time = timeValueId * weekHour
            }
            else {
                time = timeValueId * monthHour;
            }
            data['duration'] = time;

            data['filterId'] = filterId;

            App.api('/push/saveSpRule', {
                'model': JSON.stringify(data)
            }, {
                success: function () {
                    if (deviceType === '6')
                        App.router.go('/push/smart_push_feedermini');
                    else if(deviceType === '4')
                        App.router.go('/push/smart_push_feeder');
                    else if(deviceType === '5')
                        App.router.go('/push/smart_push_cozy');
                }
            });
        };
        var deviceType = App.router.getParameter('deviceType');
        if (deviceType === '6')
            App.menu.select('/push/smart_push_feedermini');
        else if(deviceType === '4')
            App.menu.select('/push/smart_push_feeder');
        else if(deviceType === '5')
            App.menu.select('/push/smart_push_cozy');

        var ruleId = App.router.getParameter('ruleId');

        var eventId = -1, filterId = -1, contentId = -1, timeUnitId = 1, timeValueId = 1;

        if (App.router.getParameter('contentId') != null) {
            contentId = App.router.getParameter('contentId');
        }
        var initRule = function () {
            return App.api('/push/findRule', {
                'ruleId': ruleId
            }).then(function (result) {
                deviceType = result.deviceType;
                eventId = result.eventId;
                filterId = result.filterId;
                contentId = result.contentId;
                var time = result.duration;
                if (time >= monthHour && time % monthHour === 0) {
                    timeUnitId = 4;
                    timeValueId = time / monthHour;
                } else if (time >= weekHour && time % weekHour === 0) {
                    timeUnitId = 3;
                    timeValueId = time / weekHour;
                } else if (time >= dayHour && time % dayHour === 0) {
                    timeUnitId = 2;
                    timeValueId = time / dayHour;
                } else {
                    timeUnitId = 1;
                    timeValueId = time;
                }

                timeUnitSelect.setValue(timeUnitId);
                timeValueSelect.setValue(timeValueId);
            });

        };

        var deviceSelect = new ValueSelect("device-select", []);
        var eventSelect = new ValueSelect("event-select", []);
        var filterSelect = new ValueSelect("filter-select", []);
        var contentSelect = new ValueSelect("content-select", []);
        var timeUnitSelect = new ValueSelect("timeUnit-select", [{key: 1, value: "小时"}, {key: 2, value: "天"}, {
            key: 3,
            value: "星期"
        }, {key: 4, value: "个月"}]);
        var timeValueSelect = new ValueSelect("timeValue-select", [{key: 1, value: '01'}, {
            key: 2,
            value: '02'
        }, {key: 3, value: '03'}, {key: 4, value: '04'}, {key: 5, value: '05'}, {key: 6, value: '06'}, {
            key: 7,
            value: '07'
        }, {key: 8, value: '08'}, {key: 9, value: '09'}, {key: 10, value: '10'}]);


        deviceSelect.init();
        eventSelect.init();
        filterSelect.init();
        contentSelect.init();
        timeUnitSelect.init();
        timeValueSelect.init();

        timeUnitSelect.setValue(timeUnitId);
        timeValueSelect.setValue(timeValueId);

        timeUnitSelect.addChangeListener(function (select, timeUnitCode) {
            timeUnitId = Number(timeUnitCode);
        });

        timeValueSelect.addChangeListener(function (select, timeValueCode) {
            timeValueId = Number(timeValueCode);
        });


        deviceSelect.addChangeListener(function (select, deviceCode) {
            deviceType = deviceCode;
            initEvent();
            initContent();
        });

        filterSelect.addChangeListener(function (select, filterCode) {
            filterId = filterCode;
        });
        contentSelect.addChangeListener(function (select, contentCode) {
            contentId = contentCode;
        });
        eventSelect.addChangeListener(function (select, eventCode) {
            return App.api('/push/findFilter', {
                'deviceType': deviceType,
                'eventId': eventCode
            }).then(function (result) {
                var item = [];
                eventId = eventCode;
                item.push({key: -1, value: "无"});
                for (var i = 0; i < result.length; i++) {
                    item.push({key: result[i].filterId, value: result[i].filterName});
                }
                if (item.length > 0 && filterId === -1) {
                    filterId = item[0].key;
                }

                filterSelect.setItem(item);
                filterSelect.setValue(filterId);
            });
        });


        var initEvent = function () {
            return App.api('/push/findEvent', {
                'deviceType': deviceType
            }).then(function (result) {
                var item = [];
                for (var i = 0; i < result.length; i++) {
                    item.push({key: result[i].eventId, value: result[i].eventName});
                }
                if (item.length > 0 && eventId === -1) {
                    eventId = item[0].key;
                }

                eventSelect.setItem(item);
                eventSelect.setValue(eventId);
            });
        };

        var initContent = function () {
            return App.api('/push/findSpContentByType', {
                'deviceType': deviceType
            }).then(function (result) {
                var item = [];
                for (var i = 0; i < result.length; i++) {
                    item.push({key: result[i].contentId, value: result[i].title});
                }
                if (contentId === -1 && item.length > 0) {
                    contentId = item[0].key;
                }
                contentSelect.setItem(item);
                contentSelect.setValue(contentId);
            });
        };

        var initDevice = function () {
            return App.api('/push/devices').then(function (result) {
                var item = [];
                for (var i = 0; i < result.length; i++) {
                    item.push({key: result[i].deviceType, value: result[i].deviceName});
                }
                if (!deviceType) {
                    deviceType = item[0].key;
                }
                deviceSelect.setItem(item);
                deviceSelect.setValue(deviceType);
            });
        };

        if (ruleId) {
            LocaleSpec.loadLocales().then(initRule).then(initDevice).then(initEvent).then(initContent).then(function () {
                $('#save-btn').click(save);
            });
        } else {
            LocaleSpec.loadLocales().then(initDevice).then(initEvent).then(initContent).then(function () {
                $('#save-btn').click(save);
            });
        }
    });
</script>
<style type="text/css">
    #img-preview img {
        max-width: 200px;
    }
</style>

<form id="save-form" role="form" onsubmit="return false;">

    <div>
        <label style="font-size: 18px">分类</label>
        <div class="region-select-container">
            <div class="form-group">
                <label style="color:darkgrey">设备 &nbsp;</label> <label for="device-select"></label><select
                    class="form-control"
                    id="device-select"></select>
            </div>
        </div>
    </div>

    <div>
        <label style="margin-top: 5px;font-size: 18px">添加规则</label>
        <div class="region-select-container" style="margin-top: 5px;">
            <div class="form-group">
                <label style="color:darkgrey">事件 &nbsp;</label> <label for="event-select"></label><select
                    class="form-control"
                    id="event-select"></select>
            </div>
        </div>
        <div class="region-select-container">
            <div class="form-group">
                <label style="color:darkgrey">时间 &nbsp; </label>
                <label for="timeValue-select"></label><select class="form-control" id="timeValue-select"></select>
                <label for="timeUnit-select"></label><select class="form-control" id="timeUnit-select"></select>

            </div>
        </div>

        <div class="region-select-container">
            <label style="color:darkgrey">过滤 &nbsp;</label> <label for="filter-select"></label><select
                class="form-control"
                id="filter-select"></select>
        </div>
    </div>


    <div>
        <label style="margin-top: 20px;font-size: 18px">推送内容</label>
        <div class="region-select-container">
            <label style="color:darkgrey">标题 &nbsp;</label> <label for="content-select"></label><select
                class="form-control"
                id="content-select"></select>
        </div>
    </div>


    <a id="save-btn" class="btn btn-primary" style="margin-top: 20px" data-l10n-id="save"></a>
    <a href="javascript:window.history.back();" style="margin-top: 20px" class="btn btn-info"
       data-l10n-id="cancel"></a>
</form>