<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>

<script src="js/localespec.js"></script>
<script>
    App.ready(function () {
        App.menu.select('/pet/foodbrands');
        var form = $('#save-form');
        var previewImage = function (url) {
            if (url) {
                $('#icon-preview').html('<img src="' + url + '"/>');
            } else {
                $('#icon-preview').html('');
            }
            form.find('[name="icon"]').val(url);
        };
        App.upload.init({
            namespace: 'foodbrand',
            type: 'image',
            id: 'upload-btn',
            fileUploaded: function (info) {
                if (typeof info == 'string') {
                    info = JSON.parse(info);
                }
                previewImage(info.url);
            },
            error: function (errTip) {
                alert('文件上传出错' + errTip);
            }
        });
        window.addLocaleSpec = function (locale) {
            var spec = LocaleSpec.get(locale);
            App.openDialog({
                url: App.router.getRealPath('/pet/foodbrandlocalespec.html'),
                context: {spec: spec},
                noHeader: true,
                primaryClick: LocaleSpec.save,
                beforeRender: function (body) {
                    var select = body.find('[name="locale"]');
                    $.each(LocaleSpec.getLocaleList(), function (index, item) {
                        select.append('<option value="' + item.key + '">' + item.name + '</option>');
                    });
                }
            });
        };
        var save = function () {
            var data = form.serializeObject();
            data._localizedProps = LocaleSpec.getAllByKeys();
            App.api('/pet/savefoodbrand', {'model': JSON.stringify(data), 'region': data.region}, {
                success: function () {
                    App.router.go('/pet/foodbrands', {'region': data.region});
                }
            });
        };
        var initButtons = function () {
            $('#save-btn').click(save);
            $('#add-locale-btn').click(addLocaleSpec);
        };
        var renderModel = function (model) {
            App.renderPage({'model': model});
            LocaleSpec.setAllByKeys(model._localizedProps);
            previewImage(model.icon);
        };
        var loadModel = function () {
            var id = App.router.getParameter('id');
            if (!id) {
                renderModel(App.router.getParameters());
                return;
            }
            return App.api('/pet/foodbrandnocache', {'id': id}, {
                success: function (data) {
                    model = data;
                    if ($.isEmptyObject(model)) {
                        return false;
                    }
                    renderModel(model);
                }
            });
        };
        LocaleSpec.loadLocales().then(loadModel).then(initButtons);
    });
</script>

<form id="save-form" role="form" onsubmit="return false;">
    <input type="hidden" name="id" ng-model="model.id"/>
    <input type="hidden" name="icon" ng-model="model.icon"/>
    <div class="form-group">
        <label>区域</label>
        <div class="controls">
            <input type="text" name="region" class="form-control" ng-model="model.region" readonly="readonly"/>
        </div>
    </div>
    <div class="form-group">
        <label>Icon</label>
        <div class="controls">
            <div>
                <a href="javascript:;" id="upload-btn">+选择上传文件</a>
            </div>
            <div id="icon-preview"></div>
        </div>
    </div>
    <div class="form-group">
        <div class="controls">
            <a href="javascript:;" id="add-locale-btn">
                <span>+</span>
                <span data-l10n-id="localize"></span>
            </a>
            <div id="locale-spec-template">
                <div class="locale-spec" data-locale="{{locale.key}}">
                    <div class="locale-spec-item">
                        <label data-l10n-id="locale"></label>:
                        <span class="locale">{{locale.name}}</span>
                        <a href="javascript:addLocaleSpec('{{locale.key}}');" class="edit" data-l10n-id="edit"></a>
                        <a href="javascript:LocaleSpec.remove('{{locale.key}}');" class="edit"
                           data-l10n-id="delete"></a>
                    </div>
                    <div class="locale-spec-item"><label data-l10n-id="name"></label>:
                        <span>{{spec.name}}</span>
                    </div>
                    <div class="locale-spec-item"><label>索引</label>:
                        <span>{{spec.index}}</span>
                    </div>
                </div>
            </div>
            <div id="locale-specs"></div>
        </div>
    </div>
    <!--<a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>-->
    <a href="#/pet/foodbrands" class="btn btn-info" data-l10n-id="cancel"></a>
</form>