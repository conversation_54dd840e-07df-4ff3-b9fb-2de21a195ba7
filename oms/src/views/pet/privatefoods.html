
<script>
    function audit(id, region) {
        App.router.go('/pet/privatefood', {id: id, region: region});
    }
    App.ready(function () {
        var request = function (offset) {
            App.scrollToTop();
            var params = App.router.getParameters();
            params = $.extend({'limit': 10}, params);
            params.offset = (!U.isNull(offset)) ? offset : params.offset;
            return App.api('/pet/privatefoods', params).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                var now = new Date();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._addition = JSON.stringify(context.addition);
                    var status = context.status;
                    if (status == 1) {
                        context._status = '待审核';
                        context._audit
                                = '<li><a href="javascript:audit(' + context.id + ',\'' + (context.region ? context.region : '') + '\')">审核</a></li>';
                    }
                    if (context.icons) {
                        var iconArr = context.icons;
                        var imgs = '';
                        for (var o in iconArr) {
                            imgs += '<a href="' + iconArr[o] + '" target="_blank"><img src="' + App.previewImgUrl(iconArr[o], 120, 120) + '" style=margin-right:10px;"></a>';
                        }
                        context._icons = imgs;
                    }
                    var row = U.template(template, context);
                    table.append(row);
                }
                App.pagination(ps, request);
            });
        };
        var init = function () {
            request();
        };
        init();
    });
</script>

<div class="panel panel-default x-panel">
    <div class="panel-heading">
      <!--   <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
            <input type="text" id="search-key" class="form-control" name="id" ng-model="REQUEST.id"/>
            <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
        </form> -->
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th width="20%">宠粮名</th>
                <th width="20%">审核图片</th>
                <th width="10%">蛋白质</th>
                <th width="10%">脂肪</th>
                <th width="10%">纤维</th>
                <th width="10%">地区</th>
                <th width="10%">审核状态</th>
                <th width="10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{name}}</td>
                <td>{{_icons}}</td>
                <td>{{protein}}</td>
                <td>{{fat}}</td>
                <td>{{fiber}}</td>
                <td>{{region}}</td>
                <td>{{_status}}</td>
                <td class="ops">
                    <div class="dropdown" style="display: inline-block;">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            {{__audit}}
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>