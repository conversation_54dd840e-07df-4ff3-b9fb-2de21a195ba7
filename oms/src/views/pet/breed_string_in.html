<script src="js/miscuploader.js"></script>
<script>
App.ready(function(){
	App.menu.select('/pet/breeds');
	var submit = function() {
		App.uploadFile({
			fileElement: $('#file')[0],
			url: '/pet/breed_string_in',
			success: function (result){
				alert('导入成功');
				App.router.go('/pet/breeds');
			}
		});
	};
	$('#submit-btn').click(submit);
});
</script>

<form id="form1" role="form" onsubmit="return false;">
	<div class="form-group">
		<label>选择文件：</label> 
		<input type="file" id="file" name="file" class="form-control" />
	</div>
	<a id="submit-btn" class="btn btn-primary">导入</a>
	<a href="javascript:window.history.back()" class="btn btn-info btn-back">返回</a>
</form>