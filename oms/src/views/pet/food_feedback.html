<script>
	App.ready(function() {
		var request = function(offset) {
			App.scrollToTop();
			var params = App.router.getParameters();
			params = $.extend({'limit' : 20}, params);
			params.offset = (!U.isNull(offset)) ? offset : params.offset;
			App.api('/pet/feedbackpetfoods', params).then(function(result) {
				var table = $('.table-main');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				var list = result.items || [];
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
					var row = U.template(template,context);
					table.append(row);
				}
				App.pagination(result, request);
			});
		};
		$('.table-main').on('click','.btn-delete',function(){
			if (!confirm('确认删除？')) {
				return;
			}
			var id = $(this).attr('id');
			App.api('/pet/deletefeedbackfood',{'id': id}).then(function(){
				alert('删除成功');
				window.location.reload();
			});
		});
		request();
	});
</script>
<style>
</style>
<div class="panel panel-default">
	<!-- <div class="panel-heading">
		<form class="form-inline">
			<input type="text" class="form-control" placeholder="宠粮名称">
			<a href="#" class="btn btn-primary" role="button">查询</a>
		</form>
	</div> -->
	<div class="panel-body">
		<table class="table x-table table-main">
			<tr class="header">
				<th style="width: 20%">用户id</th>
				<th style="width: 20%">建议时间</th>
				<th style="width: 40%">建议宠粮名称</th>
				<th style="width: 20%">操作</th>
			</tr>
			<tr class="template">
				<td>{{userId}}</td>
				<td>{{_createdAt}}</td>
				<td>{{name}}</td>
				<td>
					<a href="javascript:void(0);" class="btn-delete" id="{{id}}">删除</a>
				</td>
			</tr>
		</table>
	</div>
</div>

<div class="clear h10"></div>
<div id="pagination-wrap"></div>