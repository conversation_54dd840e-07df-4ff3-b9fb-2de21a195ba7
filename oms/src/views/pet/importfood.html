<script src="js/miscuploader.js"></script>
<script>
App.ready(function(){
	var submit = function() {
		var region = App.router.getParameter('region');
		App.uploadFile({
			fileElement: $('#file')[0],
			url: '/pet/importfood',
			data: {'region': region},
			success: function (result){
				alert('导入成功');
			    App.router.go('/pet/foodbrands', {'region': region});
			}
		});
	};
	$('#submit-btn').click(submit);
});
</script>

<form id="form1" role="form" onsubmit="return false;">
	<div class="form-group">
		<label>选择文件：</label> 
		<input type="file" id="file" name="file" class="form-control" />
	</div>
	<a id="submit-btn" class="btn btn-primary">导入</a>
</form>