<style type="text/css">
    .size-template {
        display: none;
    }

    .size, .size1 {
        clear: both;
        padding: 10px;
        margin: 20px 0 0 0;
        color: #333;
        border: 1px dashed #AAA;
    }

    .size .form-control {
        width: 60px;
        display: inline-block;
    }

    .size-detail {
        width: 100%;
        padding: 0;
    }

    .size-detail td {
        padding: 0;
        vertical-align: top;
    }

    .size-detail select.form-control {
        width: 150px;
    }

    .size-detail textarea.form-control {
        width: 100%;
        line-height: 1.1rem;
        font-size: 1rem;
    }

    .size-add-btn {
        color: #990000;
    }
    .size-remove-btn {
        color: #F00;
    }
</style>
<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script src="js/localesaver.js"></script>
<script>
App.ready(function () {
        //menu
        App.menu.select('/pet/breeds');
        var form = $('#save-form');
        var localeSaver = new LocaleSaver({'dialogUrl': '/pet/breed_spec.html'});
        var previewImage = function (url) {
            if (url) {
                $('#avatar-preview').html('<img src="' + url + '"/>');
            } else {
                $('#avatar-preview').html('');
            }
            form.find('[name="avatar"]').val(url);
        };
        var parseWeightArray = function (o, key) {
            var size = 2;
            var array = [];
            for (var i = 0; i < size; i++) {
                var k = key + '[' + i + ']';
                var v = o[k];
                delete o[k];
                v = parseFloat(v);
                if (U.isNull(v) || isNaN(v)) {
                    return null;
                }
                array.push(v);
            }
            return array;
        };
        var save = function () {
            var data = form.serializeObject();
            data._localizedProps = localeSaver.getDataAsModel();
            var sizes = [];
            var tmpForm = $('<form/>');
            $('.size').not('.size-template').each(function (index, item) {
                item = $(item);
                var cloneItem = item.clone();
                tmpForm.empty();
                tmpForm.append(cloneItem);
                var id = item.find('[name="sizes.id"]').val();
                //console.log(item.find('[name="sizes.id"]').val() + ", " + cloneItem.find('[name="sizes.id"]').val());
                var size = tmpForm.serializeObject();
                size = size['sizes'];
                size['id'] = id; // select value could not be cloned
                size['maleWeight'] = parseWeightArray(size, 'maleWeight');
                size['femaleWeight'] = parseWeightArray(size, 'femaleWeight');
                var locales = {};
                item.find('.size-detail').each(function (index, sd) {
                    sd = $(sd);
                    var locale = sd.find('[name="sizes.localespec.locale"]').val();
                    var detail = sd.find('[name="sizes.localespec.detail"]').val();
                    locales[locale] = detail;
                });
                var localeProps = {'detail': locales};
                delete size['localespec'];
                size['_localizedProps'] = localeProps;
                sizes.push(size);
            });
            data['sizes'] = sizes;
            App.api('/pet/breed_save', {'model': JSON.stringify(data)}, {
                success: function () {
                    App.router.go('/pet/breeds?petType=' + data['type']);
                }
            });
        };
        var renderModel = function (model) {
            App.renderPage({'category': model});
            if (model._localizedProps) {
            	localeSaver.setDataByModel(model._localizedProps, ['name','index'])
            }
            previewImage(model.avatar);
            var sizes = model.sizes || [];
            for (var i = 0; i < sizes.length; i++) {
                var size = sizes[i];
                appendSize(size);
            }
            $('.size-add-btn').click(function () {
                appendSize({});
            });
            $('.size-up-btn').click(function () {
                var p = $(this).parents('.size');
                var pp = p.parent();
                var theIndex = -1;
                var list = pp.children('.size').not('.size-template');
                list.each(function (index, item) {
                    if (item == p[0]) {
                        theIndex = index;
                        return false;
                    }
                });
                if (theIndex <= 0) {
                    return;
                }
                p.insertBefore(list[theIndex - 1]);
            });
            $('.size-remove-btn').click(function () {
                var p = $(this).parents('.size');
                p.remove();
            });
        };
        var sizes = [];
        var sizeTemplate = $('.size-template');
        var loadSizes = function () {
            return App.api('/pet/sizes', null, {
                success: function (data) {
                    sizes = data;
                }
            });
        };
        var appendSizeDetail = function (parent, data) {
            if (!data) {
                data = {};
            }
            var c = sizeTemplate.find('.size-detail').clone();
            var key = 'sizes.localespec';
            var localeSelect = c.find('[name="' + key + '.locale"]');
            localeSelect.append('<option value="">------</option>');
            $.each(localeSaver.getLocales(), function (index, item) {
                localeSelect.append('<option value="' + item.key + '">' + item.name + '</option>');
            });
            localeSelect.val(data.locale);
            c.find('[name="' + key + '.detail"]').val(data.detail);
            c.find('.size-detail-remove-btn').click(function () {
                var dom = $(this).parents('.size-detail');
                var detail = dom.find('[name="sizes.localespec.detail"]').val();
                if (detail && !confirm('确认删除介绍内容吗？')) {
                    return;
                }
                dom.remove();
            });
            parent.append(c);
        };
        var appendSize = function (size) {
            var container = sizeTemplate.clone().removeClass('size-template');
            var key = 'sizes';
            var sizesContainer = container.find('[name="sizes.id"]');
            $.each(sizes, function (index, item) {
                sizesContainer.append('<option value="' + item.id + '">' + item.name + '</option>');
            });
            container.renderModel({'sizes': size});
            if (size.maleWeight) {
                container.find('[name="' + key + '.maleWeight[0]"]').val(size.maleWeight[0]);
                container.find('[name="' + key + '.maleWeight[1]"]').val(size.maleWeight[1]);
            }
            if (size.femaleWeight) {
                container.find('[name="' + key + '.femaleWeight[0]"]').val(size.femaleWeight[0]);
                container.find('[name="' + key + '.femaleWeight[1]"]').val(size.femaleWeight[1]);
            }
            var detailsContainer = container.find('.size-details').empty();
            var details = size._localizedProps ? size._localizedProps.detail : {};
            for (var locale in details) {
                appendSizeDetail(detailsContainer, {'locale': locale, 'detail': details[locale]});
            }
            container.find('.size-detail-add-btn').click(function () {
                var p = $(this).parents('.size').find('.size-details');
                appendSizeDetail(p, null);
            });
            $('#sizes').append(container);
        };
        var loadModel = function () {
            var id = App.router.getParameter('id');
            if (!id) {
                model = {'type': App.router.getParameter('petType')};
                renderModel(model);
                return;
            }
            return App.api('/pet/breed', {'id': id}, {
                success: function (data) {
                    model = data;
                    if ($.isEmptyObject(model)) {
                        return;
                    }
                    renderModel(model);
                }
            });
        };
        var init = function() {
            App.upload.init({
                namespace: 'dogavatar',
                type: 'image',
                id: 'upload-btn',
                fileUploaded: function (info) {
                    if (typeof info == 'string') {
                        info = JSON.parse(info);
                    }
                    previewImage(info.url);
                },
                error: function (errTip) {
                    alert('文件上传出错' + errTip);
                }
            });
            $.when(localeSaver.init(), loadSizes()).then(loadModel).then(function () {
                $('#save-btn').click(save);
            });
        };
        init();
});
</script>


<form id="save-form" role="form" onsubmit="return false;">
    <input type="hidden" name="id" ng-model="category.id"/>
    <input type="hidden" name="avatar" ng-model="category.avatar"/>
    <input type="hidden" name="type" ng-model="category.type"/>

    <div class="form-group">
        <label>大小</label>
        <select class="form-control" name="size" ng-model="category.size">
            <option value="1">小</option>
            <option value="2">中</option>
            <option value="3">大</option>
        </select>
    </div>

    <div class="form-group">
        <label>头像</label>
        <div class="controls">
            <div>
                <a href="javascript:;" id="upload-btn">+选择上传文件</a>
            </div>
            <div id="avatar-preview"></div>
        </div>
    </div>

    <div class="form-group">
        <label>
            <a href="javascript:;" class="size-add-btn">+体型</a>
        </label>

        <div class="size size-template">
            <label>
            	<span>体型</span>
                <a href="javascript:;" class="size-up-btn">↑上移</a>
                <a href="javascript:;" class="size-remove-btn">-删除</a>
            </label>
            <select name="sizes.id" class="form-control" ng-model="sizes.id"></select>
            <label>成年：</label>
            <input type="text" name="sizes.adult" class="form-control" ng-model="sizes.adult"/>
            月
            <label>老年：</label>
            <input type="text" name="sizes.old" class="form-control" ng-model="sizes.old"/>
            月
            <label>雄体重：</label>
            <input type="text" name="sizes.maleWeight[0]" class="form-control"/>
            KG
            到
            <input type="text" name="sizes.maleWeight[1]" class="form-control"/>
            KG
            <label>雌体重：</label>
            <input type="text" name="sizes.femaleWeight[0]" class="form-control"/>
            到
            <input type="text" name="sizes.femaleWeight[1]" class="form-control"/>
            <br/>
            <label>
                <a href="javascript:;" class="size-detail-add-btn">+详细</a>
            </label>

            <div class="size-details">
                <table class="size-detail" cellspacing="0">
                    <tr>
                        <td width="25%">
                            <a href="javascript:;" class="size-detail-remove-btn">-语言</a>
                            ：
                            <select class="form-control" name="sizes.localespec.locale"></select>
                        </td>
                        <td width="75%">
                            <textarea type="text" class="form-control" rows="3"
                                      name="sizes.localespec.detail"></textarea>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div id="sizes"></div>
    </div>


    <div class="form-group">
        <div class="controls">
            <a href="javascript:;" id="local-add-btn">
                <span>+</span>
                <span data-l10n-id="localize"></span>
            </a>
            <div id="locale-spec-template">
                <div class="locale-spec">
                    <div class="locale-spec-item">
                        <label data-l10n-id="locale"></label>:
                        <span class="locale">{{locale.name}}</span>
                        <a href="javascript:;" class="edit" data-l10n-id="edit" data-op-type="update" data-op-key="{{locale.key}}"></a>
                        <a href="javascript:;" class="edit" data-l10n-id="delete" data-op-type="remove" data-op-key="{{locale.key}}"></a>
                    </div>
                    <div class="locale-spec-item">
                    	<label>名称</label>:<span>{{spec.name}}</span><br/>
	                    <label>索引</label>:<span>{{spec.index}}</span>
                    </div>
                </div>
            </div>
            <div id="locale-specs"></div>
        </div>
    </div>

    <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
    <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
</form>