<link rel="stylesheet" type="text/css" href="vendor/select2/select2.css" />
<script src="vendor/select2/select2.min.js"></script>
<script src="js/region-select.js"></script>
<script>
    App.ready(function () {
        var code = App.router.getParameter('region');
        if (!code) {
            code = "CN";
        }
        var request = function () {
            App.scrollToTop();
            var params = App.router.getParameters();
            return App.api('/pet/foodbrandsnocache', params).then(function (list) {
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>' + table.find('tr.template').html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    if (context.icon) {
                    	context._icon = '<a href="' + context.icon + '" target="_blank"><img src="'+App.previewImgUrl(context.icon,120,120)+'"/></a>';
                    }
                    var row = U.template(template, context);
                    table.append(row);
                }
            });
        };

        var init = function () {
          	$('#import-btn').click(function(){
        		App.router.go('pet/importfood', {'region': code});
        	});
            var select = new RegionSelect('region-select');
            select.init({'value': code}).done(function(){
            	select.addChangeListener(function(select, value){
                	code = value;
                	App.router.go('/pet/foodbrands', {'region': code});
                });
            });
            $('#add-brand-btn').click(function () {
                App.router.go('/pet/foodbrand', {'region': code});
            });
            request();
        };
        init();
    });
</script>

<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <!--<a href="javascript:;" id="add-brand-btn" class="btn btn-primary">添加品牌</a>-->
        <!--<a href="javascript:;" id="import-btn" class="btn btn-warning">导入宠粮</a>-->
        <select class="form-control" id="region-select-s1" style="width:15rem;display:inline-block;"></select>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 40%" data-l10n-id="name"></th>
                <th style="width: 15%">索引</th>
                <th style="width: 25%">Icon</th>
                <th style="width: 20%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{name}}</td>
                <td>{{index}}</td>
                <td>{{_icon}}</td>
                <td>
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="#/pet/foodbrand?id={{id}}" data-l10n-id="edit"></a>
                            </li>
                            <li>
                                <a href="#/pet/foodproducts?brandId={{id}}">宠粮列表</a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>