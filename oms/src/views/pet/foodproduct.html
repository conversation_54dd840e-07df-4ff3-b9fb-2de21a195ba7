<script src="js/localespec.js"></script>
<script>
App.ready(function() {
	App.menu.select('/pet/foodbrands');
	
  var form = $('#save-form');
  var readyOnly = App.router.getParameter('readOnly');

	window.addLocaleSpec = function(locale) {
	    var spec = LocaleSpec.get(locale);
		App.openDialog({
			url : App.router.getRealPath('/pet/foodproductlocalespec.html'),
			context: {spec: spec},
			noHeader : true,
			primaryClick : LocaleSpec.save,
			beforeRender: function(body) {
				var select = body.find('[name="locale"]');
				$.each(LocaleSpec.getLocaleList(), function(index, item) {
					select.append('<option value="'+item.key+'">'+item.name+'</option>');
				});
			}
		});
	};

  var save = function() {
	var data = form.serializeObject();
	data._localizedProps = LocaleSpec.getAllByKeys();
	App.api('/pet/savefoodproduct', {'model': JSON.stringify(data)} , {
		success: function() {
			App.router.go('/pet/foodproducts?brandId=' + data['brand']['id'] + '&petType=' + data['type']);
		}
	});
  };

  var initButtons = function() {
	if (readyOnly) {
		form.find('[name]').attr('disabled', 'disabled');
		return;
	}
    $('#save-btn').click(save);
    $('#add-locale-btn').click(addLocaleSpec);
  };
  
  var renderModel = function(model) {
		App.renderPage({'model': model});
		LocaleSpec.setAllByKeys(model._localizedProps);
  };
  
  var loadModel = function() {
	var id = App.router.getParameter('id');
	if (!id) {
		return App.promise(function(){
			var brandId = App.router.getParameter('brandId');
			if (!brandId) {
				alert('Bad request');
				return false;
			}
			model = {'brand': {'id': brandId}, 'type': App.router.getParameter('petType')};
			renderModel(model);
		});
	}
	return App.api('/pet/foodproductnocache', {'id': id}, {
		success: function(data) {
			model = data;
			if ($.isEmptyObject(model)) {
				alert('The product has been removed.');
				return false;
			}
			renderModel(model);
		}
	});
  };
  
  LocaleSpec.loadLocales().then(loadModel).then(initButtons);
});
</script>

<form id="save-form" role="form" onsubmit="return false;">
    <input type="hidden" name="id" ng-model="model.id" />
    <input type="hidden" name="brand.id" ng-model="model.brand.id" />
    <input type="hidden" name="type" ng-model="model.type" />
     <div class="form-group">
        <label>蛋白质含量(0-1)</label>
        <div class="controls">
            <input type="text" name="component.protein" ng-model="model.component.protein" class="form-control"/>
        </div>
     </div>
     <div class="form-group">
        <label>脂肪含量(0-1)</label>
        <div class="controls">
            <input type="text" name="component.fat" ng-model="model.component.fat" class="form-control"/>
        </div>
     </div>
     <div class="form-group">
        <label>水分含量(0-1)</label>
        <div class="controls">
            <input type="text" name="component.water" ng-model="model.component.water" class="form-control"/>
        </div>
     </div>
     <div class="form-group">
        <label>纤维含量(0-1)</label>
        <div class="controls">
            <input type="text" name="component.fibre" ng-model="model.component.fibre" class="form-control"/>
        </div>
     </div>
     <div class="form-group">
        <label>能量(如果以上成分信息不全，请填写)</label>
        <div class="controls">
            <input type="text" name="energy" ng-model="model.energy" class="form-control"/>
            <p class="help-block">1KG卡路里含量</p>
        </div>
     </div>
        <div class="form-group">
        <label>消化率(0-100)</label>
        <div class="controls">
            <input type="text" name="ratio" ng-model="model.ratio" class="form-control"/>
        </div>
     </div>
     <div class="form-group">
        <div class="controls">
           	<a href="javascript:;" id="add-locale-btn"><span>+</span><span data-l10n-id="localize"></span></a>
           	<div id="locale-spec-template">
           		<div class="locale-spec" data-locale="{{locale.key}}">
			  	 	<div class="locale-spec-item">
			  	 		<label data-l10n-id="locale"></label>:<span class="locale">{{locale.name}}</span>
			  	 		<a href="javascript:addLocaleSpec('{{locale.key}}');" class="edit" data-l10n-id="edit"></a>
			  	 		<a href="javascript:LocaleSpec.remove('{{locale.key}}');" class="edit" data-l10n-id="delete"></a>
			  	 	</div>
			  	 	<div class="locale-spec-item"><label data-l10n-id="name"></label>:<span>{{spec.name}}</span></div>
			  	 	<div class="locale-spec-item"><label>索引</label>:<span>{{spec.index}}</span></div>
			  	 	<div class="locale-spec-item"><label data-l10n-id="detail"></label>:<span>{{spec.detail}}</span></div>
			  	 </div>
           	</div>
           	<div id="locale-specs"></div>
        </div>
     </div>
     <!--<a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>-->
      <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
</form>