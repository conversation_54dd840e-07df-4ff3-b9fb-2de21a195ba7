<style>
  .avatar-show span {
    display: none;
  }

  .avatar-hide a {
    display: none;
  }
</style>
<script>
  App.ready(function () {
    var calcAge = function (birth, now) {
      var years = now.getFullYear() - birth.getFullYear();
      var days = U.date.getDayOfYear(now) - U.date.getDayOfYear(birth);
      var age = years + days / 365;
      return Math.round(age * 12);
    };
    var genderName = function (t) {
      if (t == 1) {
        return '公';
      }
      if (t == 2) {
        return '母';
      }
      return '';
    };
    var request = function (offset) {
      App.scrollToTop();
      var params = $.extend({ limit: 10 }, App.router.getParameters());
      if (mars.isEmpty(params.id) && mars.isEmpty(params.groupId)) {
        return;
      }
      params.offset = !U.isNull(offset) ? offset : params.offset;
      var requestUrl = '';
      if (params.groupId) {
        requestUrl = '/family/getPetListByGroupId';
      } else {
        requestUrl = '/pet/pets';
      }

      return App.api(requestUrl, params).then(function (ps) {
        var list = ps.items || [];
        var table = $('#the-table');
        var template = table.attr('data-template');
        if (!template) {
          template = '<tr>' + table.find('tr.template').html() + '</tr>';
          table.attr('data-template', template);
        }
        table.find('tr:not(.header)').remove();
        var now = new Date();
        for (var i = 0; i < list.length; i++) {
          var context = list[i];
          context._avatarCssClass = context.avatar
            ? 'avatar-show'
            : 'avatar-hide';
          context._gender = genderName(context.gender);
          context._createdAt = U.date.format(
            U.date.parse(context.createdAt),
            'yyyy-MM-dd HH:mm'
          );
          context._age = calcAge(U.date.parse(context.birth, 0), now);
          context.foodUrl = 'javascript:;';
          if (context.food && context.food.id) {
            context.foodUrl = '#/pet/foodproduct?id={{food.id}}&readOnly=1';
            context.foodName = context.food.name;
          } else if (context.privateFood && context.privateFood.id) {
            context.foodUrl = '#';
            context.foodName = context.privateFood.name + ' -(自定义主粮)';
          }
          var row = U.template(template, context);
          table.append(row);
        }
        App.pagination(ps, request);
      });
    };

    var submitIt = function () {
      App.router.go(
        App.router.getCurrentPath(),
        $('#search-form').serializeObject()
      );
    };

    var init = function () {
      $('#search-btn').click(submitIt);
      $('#search-key').keydown(function (e) {
        if (e.keyCode == 13) {
          submitIt();
        }
      });
      request();
    };

    init();
  });
</script>

<div class="panel panel-default x-panel">
  <div class="panel-heading">
    <form
      id="search-form"
      class="form-inline"
      action="#"
      method="get"
      onsubmit="return false;"
    >
      <input
        type="text"
        id="search-key"
        class="form-control"
        name="id"
        ng-model="REQUEST.id"
      />
      <a
        href="javascript:;"
        id="search-btn"
        class="btn btn-primary"
        data-l10n-id="search"
      ></a>
    </form>
  </div>
  <div class="panel-body">
    <table class="table table-hover x-table" id="the-table">
      <tr class="header">
        <th style="width: 10%" data-l10n-id="id"></th>
        <th style="width: 10%" data-l10n-id="name"></th>
        <th style="width: 5%">类别</th>
        <th style="width: 5%">性别</th>
        <th style="width: 10%">品种</th>
        <th style="width: 5%">体重</th>
        <th style="width: 5%">年龄</th>
        <th style="width: 10%">宠粮</th>
        <th style="width: 10%">主人</th>
        <th style="width: 10%">所在家庭</th>
        <th style="width: 10%">宠物绑定设备(台)</th>
        <th style="width: 8%">识别照片(张)</th>
        <!-- <th style="width: 12%" data-l10n-id="operations"></th> -->
      </tr>
      <tr class="template">
        <td title="{{_createdAt}}">{{id}}</td>
        <td class="{{_avatarCssClass}}">
          <a href="{{avatar}}" target="_blank">{{name}}</a>
          <span>{{name}}</span>
        </td>
        <td>{{type.name}}</td>
        <td>{{_gender}}</td>
        <td>{{category.name}}</td>
        <td>{{weight}}</td>
        <td>{{_age}}月</td>
        <td><a href="{{foodUrl}}">{{foodName}}</a></td>
        <td>
          <a href="#/user/users?username=%23{{owner.id}}">{{owner.id}}</a>
        </td>
        <td><a href="#/user/family?groupId={{familyId}}">{{familyId}}</a></td>
        <td>
          <a href="#/user/linked_devices?petId={{id}}">{{deviceCount}}</a>
        </td>
        <td>
          <a href="#/pet/photoIdentify?userId={{owner.id}}"
            >{{omsDiscernPic.count}}</a
          >
        </td>
        <!-- <td class="ops">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li>
								<a href="#/user/linked_devices?petId={{id}}">查看绑定设备</a>
							</li>
						</ul>
					</div>
				</td> -->
      </tr>
    </table>
  </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>
