<script>
	App.ready(function() {
		var params = App.router.getParameters();
		var brandId = params.brandId;

		var request = function() {
			App.scrollToTop();
			return App.api('/pet/foodproductsnocache', params).then(function(list) {
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					var row = U.template(template, context);
					table.append(row);
				}
			});
		};

		var changeType = function(type) {
			var params = App.router.getParameters();
			params['petType'] = type;
			App.router.go(App.router.getCurrentPath(), params);
		};

		var initTypes = function() {
			var selectedTypeId = App.router.getParameter('petType') || 1;
			var selectedType = null;
			var types = [ {
				'id' : 1,
				'name' : '狗'
			}, {
				'id' : 2,
				'name' : '猫'
			} ];
			for (var i = 0; i < types.length; i++) {
				var type = types[i];
				if (selectedTypeId == type.id) {
					selectedType = type;
				}
				var li = $('<li><a href="javascript:;">' + type.name + '</a></li>');
				li.find('a').bind('click', type.id, function(event) {
					changeType(event.data);
				});
				$('#type-selector').append(li);
			}
			if (!selectedType) {
				selectedType = types[0];
			}
			$('#type-indicator').html(selectedType.name);
		};
		
		var initBrandName = function() {
			App.api('/pet/foodbrandnocache', {'id': brandId}, {
				success: function(data) {
					if ($.isEmptyObject(data)) {
						return;
					}
					$('#name-btn').html('&lt;&lt;品牌：' + data.name);
					$('#name-btn').click(function(){
						App.router.go('/pet/foodbrands', {'region': data.region});
					});
				}
			});
		};

		var init = function() {
			//menu
			App.menu.select('/pet/foodbrands');
			if (!brandId) {
				alert('Bad request');
				return;
			}
			$('#add-product-btn').click(function() {
				App.router.go('/pet/foodproduct', App.router.getParameters());
			});
			$('#export-product-btn').attr('href', App.apiPath('pet/exportfoodbybrand', $.extend(App.specialApiParams(), {
				brandId : brandId
			})));
			initTypes();
			initBrandName();
			request();
		};

		init();
	});
</script>

<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<a href="javascript:;" id="name-btn" class="btn btn-warning"></a>
		<!--<a href="javascript:;" id="add-product-btn" class="btn btn-primary" data-l10n-id="add"></a>-->
		<!--<a href="javascript:;" id="export-product-btn" class="btn btn-info">导出</a>-->
		<div class="dropdown fr">
			<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
				<span id="type-indicator"></span>
				<b class="caret"></b>
			</a>
			<ul class="dropdown-menu" id="type-selector">
			</ul>
		</div>
	</div>
	<div class="panel-body">
		<div class="table-responsive">
			<table class="table table-hover x-table" id="the-table">
				<tr class="header">
					<th style="width: 10%" data-l10n-id="id"></th>
					<th style="width: 10%" data-l10n-id="name"></th>
					<th style="width: 10%">索引</th>
					<th style="width: 10%">能量</th>
					<th style="width: 5%">消化率</th>
					<th style="width: 45%" data-l10n-id="detail"></th>
					<th style="width: 10%" data-l10n-id="operations"></th>
				</tr>
				<tr class="template">
					<td>{{id}}</td>
					<td>{{name}}</td>
					<td>{{index}}</td>
					<td>{{energy}}</td>
					<td>{{ratio}}</td>
					<td>{{detail}}</td>
					<td>
						<div class="dropdown">
							<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
								<span data-l10n-id="operations"></span>
								<b class="caret"></b>
							</a>
							<ul class="dropdown-menu dropdown-menu-right">
								<li>
									<a href="#/pet/foodproduct?id={{id}}" data-l10n-id="edit"></a>
								</li>
							</ul>
						</div>
					</td>
				</tr>
			</table>
		</div>
	</div>
</div>