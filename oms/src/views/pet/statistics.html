<script>
	App.ready(function() {
		var form = $('#search-form');
		var container = $('#chart1');
		var renderData = function(data) {
			var cats = [];
			var total = 0;
			var weight = 0;
			for (var i = 0; i < data.length; i++) {
				var item = data[i];
				total += item.count;
				weight+=item.weight;
			}	
			$('#weightinfo').html('样本数:<h1><font color="red">' + total + '</font></h1>'+'平均体重:<h1><font color="red">' + weight + '</font></h1>');
		};

		var stat = function() {
			container.empty();
			App.api('/pet/statistics', form.serializeObject()).then(function(data) {
				if (!data) {
					return;
				}
				renderData(data);
			});
		};

		var initButtons = function() {
			$('#stat-btn').click(stat);
			form.find('[name="since"]').val(60);
			form.find('[name="until"]').val(120);
			form.find('[name="type"]').val('1');
			$("input[name='event'][value=callparticipants]").attr("checked", true);
		};

		var init = function() {
			initButtons();
			stat();
		};
		init();

		var cats1, cats2;
		App.api('/pet/categoriesnocache', {
			petType : 1
		}).then(function(result) {
			cats1 = result;
			for (var i = 0; i < cats1.length; i++) {
				$('.select-cat').append('<option value="'+cats1[i].id+'">' + cats1[i].name + '</option>');
			}
		});

		$('.select-type').change(function() {
			var cat = $(this).val();

			if (!cats2) {
				App.api('/pet/categoriesnocache', {
					petType : 2
				}).then(function(result) {
					cats2 = result;
					$('.select-cat').html('');
					for (var i = 0; i < cats2.length; i++) {
						$('.select-cat').append('<option value="'+cats2[i].id+'">' + cats2[i].name + '</option>');
					}
				});
			} else {
				if (cat == 1) {
					$('.select-cat').html('');
					for (var i = 0; i < cats1.length; i++) {
						$('.select-cat').append('<option value="'+cats1[i].id+'">' + cats1[i].name + '</option>');
					}
				} else {
					$('.select-cat').html('');
					for (var i = 0; i < cats2.length; i++) {
						$('.select-cat').append('<option value="'+cats2[i].id+'">' + cats2[i].name + '</option>');
					}
				}
			}
		});
	});
</script>
<style>
#advanced-options .option {
	margin: 10px 0 0 0;
}
</style>

<form id="search-form" class="form-inline">
	<a href="javascript:;" id="stat-btn" class="btn btn-success">查询</a>

	<div id="advanced-options">
		<div class="option">
			<span>宠物年龄（天）</span>
			<span>从</span>
			<input type="text" class="form-control" value=1 name="since" />
			<span>到</span>
			<input type="text" class="form-control" name="until" />
		</div>


		<div class="option">
			<span>宠物类型</span>
			<select class="form-control select-type" name="type"
				onchange="show_sub(this.options[this.options.selectedIndex].value)"
			>
				<option value="0">==请选择==</option>
				<option value="1">狗</option>
				<option value="2">猫</option>
			</select>
		</div>


		<div class="option">
			<span>宠物性别</span>
			<select class="form-control" name="gender" onchange="show_sub(this.options[this.options.selectedIndex].value)">
				<option value="1">公</option>
				<option value="2">母</option>
			</select>
		</div>

		<div class="option">
			<span>宠物种类</span>
			<select class="form-control select-cat" name="category"
				onchange="show_sub(this.options[this.options.selectedIndex].value)"
			>
			</select>
		</div>
	</div>
</form>
<div id="chart-wrap">
	<div id="weightinfo" style="padding: 20px 0;"></div>
</div>
<script>
	function show_sub(v) {
		if (v == 'call') {
			$("#call").css('display', 'block');
			$("#funnypet").css('display', 'none');
			$("#rotary").css('display', 'none');
		} else if (v == 'funnypet') {
			$("#call").css('display', 'none');
			$("#funnypet").css('display', 'block');
			$("#rotary").css('display', 'none');
		} else {
			$("#call").css('display', 'none');
			$("#funnypet").css('display', 'none');
			$("#rotary").css('display', 'block');
		}
	}
</script>
