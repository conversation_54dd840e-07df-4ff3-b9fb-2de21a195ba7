<script>
    App.ready(function () {
    	var petSizeName = function(t) {
    		if (t == 1) {
    			return '小型';
    		} else if (t == 2) {
    			return '中型';
    		} else if (t == 3) {
    			return '大型';
    		} else {
    			return '';
    		}
    	};
        var request = function () {
            App.scrollToTop();
            var params = App.router.getParameters();
            return App.api('/pet/breeds', params).then(function (list) {
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>' + table.find('tr.template').html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._size = petSizeName(context.size);
                    var avatar = App.previewImgUrl(context.avatar, 120, 120);
                    context._avatar
                            = U.template('<a href="{{avatar}}" target="_blank"><img src="' + avatar + '"/></a>', context);
                    var row = U.template(template, context);
                    table.append(row);
                }
            });
        };
        var changeType = function (type) {
            var params = App.router.getParameters();
            params['petType'] = type;
            App.router.go(App.router.getCurrentPath(), params);
        };
        var types = [
            {
                'id': 1,
                'name': '狗'
            }, {
                'id': 2,
                'name': '猫'
            }
        ];
        var initTypes = function () {
            var selectedTypeId = App.router.getParameter('petType') || 1;
            var selectedType = null;
            for (var i = 0; i < types.length; i++) {
                var type = types[i];
                if (selectedTypeId == type.id) {
                    selectedType = type;
                }
                var li = $('<li><a href="javascript:;">' + type.name + '</a></li>');
                li.find('a').bind('click', type.id, function (event) {
                    changeType(event.data);
                });
                $('#type-selector').append(li);
            }
            if (!selectedType) {
                selectedType = types[0];
            }
            $('#type-indicator').html(selectedType.name);
        };
        //init
        {
            $('#create-btn').click(function () {
                var params = App.router.getParameters();
                if (!params['petType']) {
                    params['petType'] = 1;
                }
                App.router.go('/pet/breed', params);
            });
            request();
            initTypes();
            $('#export-btn').attr('href', App.apiPath('/pet/breed_string_out', App.specialApiParams()));
            $('#export-btn').attr('target', '_blank');
        }
    });
</script>

<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <a href="javascript:;" id="create-btn" class="btn btn-primary" data-l10n-id="add"></a>
        <a href="javascript:;" id="export-btn" class="btn btn-info">导出字串表</a>
        <a href="#/pet/breed_string_in" id="import-btn" class="btn btn-warning">导入字串表</a>
        <div class="dropdown fr">
            <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                <span id="type-indicator"></span>
                <b class="caret"></b>
            </a>
            <ul class="dropdown-menu" id="type-selector">
            </ul>
        </div>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 20%" data-l10n-id="id"></th>
                <th style="width: 30%" data-l10n-id="name"></th>
                <th style="width: 10%">索引</th>
                <th style="width: 20%">头像</th>
                <th style="width: 10%">大小</th>
                <th style="width: 10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{id}}</td>
                <td>{{name}}</td>
                <td>{{index}}</td>
                <td>{{_avatar}}</td>
                <td>{{_size}}</td>
                <td>
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="#/pet/breed?id={{id}}" data-l10n-id="edit"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
