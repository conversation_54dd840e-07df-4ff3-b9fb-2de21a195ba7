<script src="js/localespec.js"></script>
<script>
  App.ready(function() {
    App.menu.select("/pet/privatefoods");
    App.api("/pet/privatefoods", { id: App.router.getParameter("id") }).then(
      function(ret) {
        $('input[name="privatefoodid"]').val(ret.id);
        $("#name").html(ret.name);

        if (ret.icons) {
          var iconArr = ret.icons;
          var imgs = "";
          for (var o in iconArr) {
            imgs +=
              '<a href="' +
              iconArr[o] +
              '" target="_blank"><img src="' +
              iconArr[o] +
              '" style=margin-right:10px;"></a>';
          }
          $("#icons").html(imgs);
        }

        $("#protein").html(ret.protein);
        $("#fat").html(ret.fat);
        $("#fiber").html(ret.fiber);
        $("#addition").html(JSON.stringify(ret.addition));
        $('input[name="component.protein"]').val(ret.protein / 100);
        $('input[name="component.fat"]').val(ret.fat / 100);
        $('input[name="component.fibre"]').val(ret.fiber / 100);
      }
    );
    App.api("/pet/foodbrandsnocache", {
      region: App.router.getParameter("region")
    }).then(function(list) {
      var table =
        '<table class="table table-hover x-table">' +
        '<tr class="header">' +
        "<th>ID</th>" +
        "<th>名称</th>" +
        "<th>封面</th>" +
        "<th>操作</th>" +
        "</tr>";
      for (var i = 0; i < list.length; i++) {
        var context = list[i];
        table += "<tr>";
        table += "<td>" + context.id + "</td>";
        table += "<td>" + context.name + "</td>";
        var tmp;
        if (context.icon) {
          if (context.icon.indexOf("img2.petkit.cn") >= 0) {
            tmp =
              '<a href="' +
              context.icon +
              '" target="_blank"><img src="' +
              context.icon +
              '@!120-120"/></a>';
          } else {
            tmp =
              '<a href="' +
              context.icon +
              '" target="_blank"><img src="' +
              context.icon +
              '?imageView2/2/w/60/h/120"/></a>';
          }
        }
        table += "<td>" + tmp + "</td>";
        table +=
          '<td><a href="javascript:;" class="btn btn-info btn-choose-brand-submit" value="' +
          context.id +
          '">确定</a></td>';
        table += "</tr>";
      }
      table += "</table>";
      $("#brand-box").append(table);
    });
    window.addLocaleSpec = function(locale) {
      var spec = LocaleSpec.get(locale);
      App.openDialog({
        url: App.router.getRealPath("/pet/foodproductlocalespec.html"),
        context: { spec: spec },
        noHeader: true,
        primaryClick: LocaleSpec.save,
        beforeRender: function(body) {
          var select = body.find('[name="locale"]');
          $.each(LocaleSpec.getLocaleList(), function(index, item) {
            select.append(
              '<option value="' + item.key + '">' + item.name + "</option>"
            );
          });
        }
      });
    };
    $("#add-locale-btn").click(addLocaleSpec);
    LocaleSpec.loadLocales();
    $(".brand-wrapper").on("click", ".btn-choose-brand-submit", function() {
      $("#brand-box").hide();
      $("#brand-info").html($(this).attr("value"));
      $('input[name="brand.id"]').val($(this).attr("value"));
      $("#brand-info").show();
    });
    $(".btn-choose-brand").click(function() {
      $("#brand-box").show();
    });
    $(".btn-packup-brand").click(function() {
      $("#brand-box").hide();
    });
    $(".btn-back").click(function() {
      App.router.go("/pet/privatefoods");
    });
    $(".btn-nopass").click(function() {
      App.api(
        "/pet/auditprivatefood",
        { id: $('input[name="privatefoodid"]').val(), status: 3 },
        {
          success: function() {
            App.router.go("/pet/privatefoods");
          }
        }
      );
    });
    $("#save-btn").click(function() {
      if (!$('input[name="brand.id"]').val()) {
        alert("请先选择宠粮品牌");
        return;
      }
      var data = $("#save-form").serializeObject();
      data._localizedProps = LocaleSpec.getAllByKeys();
      App.api(
        "/pet/savefoodproduct",
        { model: JSON.stringify(data) },
        {
          success: function() {
            App.api(
              "/pet/auditprivatefood",
              { id: $('input[name="privatefoodid"]').val(), status: 2 },
              {
                success: function() {
                  App.router.go("/pet/privatefoods");
                }
              }
            );
          }
        }
      );
    });
  });
</script>

<div class="panel panel-default x-panel">
  <div class="panel-body">
    <form>
      <input type="hidden" name="privatefoodid" />
      <div class="form-group">
        <label>名称:</label>
        <div id="name" class="form-control"></div>
      </div>
      <div class="form-group">
        <label>图片:</label>
        <div id="icons"></div>
      </div>
      <div class="form-group">
        <label>蛋白质含量(%):</label>
        <div id="protein" class="form-control"></div>
      </div>
      <div class="form-group">
        <label>脂肪含量(%):</label>
        <div id="fat" class="form-control"></div>
      </div>

      <div class="form-group">
        <label>纤维含量(%):</label>
        <div id="fiber" class="form-control"></div>
      </div>
      <div class="form-group">
        <label>附加信息:</label>
        <div id="addition" class="form-control"></div>
      </div>
    </form>
    <div style="border-bottom:1px dashed black;"></div>
    <div class="form-group brand-wrapper">
      <div>第一步，选择宠粮品牌（若无该品牌，请到宠粮品牌页面添加）:</div>
      <div>
        <a href="javascript:;" class="btn btn-primary btn-choose-brand"
          >展开宠粮品牌</a
        >
        <a href="javascript:;" class="btn btn-primary btn-packup-brand"
          >收起宠粮品牌</a
        >
      </div>
      <div id="brand-box" style="display:none;"></div>
      <div id="brand-info" style="display:none;"></div>
    </div>
    <div class="form-group">
      <div>第二步，填写宠粮信息（参考页面头部信息）:</div>
      <div>
        <form id="save-form" role="form" onsubmit="return false;">
          <input type="hidden" name="id" ng-model="model.id" />
          <input type="hidden" name="brand.id" ng-model="model.brand.id" />
          <input type="hidden" name="type" ng-model="model.type" />
          <div class="form-group">
            <label>蛋白质含量(0-1)</label>

            <div class="controls">
              <input
                type="text"
                name="component.protein"
                ng-model="model.component.protein"
                class="form-control"
              />
            </div>
          </div>
          <div class="form-group">
            <label>脂肪含量(0-1)</label>

            <div class="controls">
              <input
                type="text"
                name="component.fat"
                ng-model="model.component.fat"
                class="form-control"
              />
            </div>
          </div>
          <div class="form-group">
            <label>水分含量(0-1)</label>

            <div class="controls">
              <input
                type="text"
                name="component.water"
                ng-model="model.component.water"
                class="form-control"
              />
            </div>
          </div>
          <div class="form-group">
            <label>纤维含量(0-1)</label>

            <div class="controls">
              <input
                type="text"
                name="component.fibre"
                ng-model="model.component.fibre"
                class="form-control"
              />
            </div>
          </div>
          <div class="form-group">
            <label>能量(如果以上成分信息不全，请填写)</label>

            <div class="controls">
              <input
                type="text"
                name="energy"
                ng-model="model.energy"
                class="form-control"
              />
              <p class="help-block">1KG卡路里含量</p>
            </div>
          </div>
          <div class="form-group">
            <label>消化率(0-100)</label>

            <div class="controls">
              <input
                type="text"
                name="ratio"
                ng-model="model.ratio"
                class="form-control"
              />
            </div>
          </div>
          <div class="form-group">
            <div class="controls">
              <a href="javascript:;" id="add-locale-btn">
                <span>+</span>
                <span data-l10n-id="localize"></span>
              </a>
              <div id="locale-spec-template">
                <div class="locale-spec" data-locale="{{locale.key}}">
                  <div class="locale-spec-item">
                    <label data-l10n-id="locale"></label>:
                    <span class="locale">{{locale.name}}</span>
                    <a
                      href="javascript:addLocaleSpec('{{locale.key}}');"
                      class="edit"
                      data-l10n-id="edit"
                    ></a>
                    <a
                      href="javascript:LocaleSpec.remove('{{locale.key}}');"
                      class="edit"
                      data-l10n-id="delete"
                    ></a>
                  </div>
                  <div class="locale-spec-item">
                    <label data-l10n-id="name"></label>:
                    <span>{{spec.name}}</span>
                  </div>
                  <div class="locale-spec-item">
                    <label>索引</label>:
                    <span>{{spec.index}}</span>
                  </div>
                  <div class="locale-spec-item">
                    <label data-l10n-id="detail"></label>:
                    <span>{{spec.detail}}</span>
                  </div>
                </div>
              </div>
              <div id="locale-specs"></div>
            </div>
          </div>
          <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
          <a
            href="javascript:window.history.back();"
            class="btn btn-info"
            data-l10n-id="cancel"
          ></a>
          <a class="btn btn-danger btn-nopass">不通过</a>
        </form>
      </div>
    </div>
  </div>
</div>
