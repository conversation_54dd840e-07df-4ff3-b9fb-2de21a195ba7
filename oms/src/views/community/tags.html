<style type="text/css">
    .x-table img.postimg {
        padding-right: 10px;
    }
    .x-table img.emoji {
        width: 20px;
        height: 20px;
    }
</style>

<script>
    App.ready(function () {
        var form = $('#search-form');
        //init post search action
        var postParams;
        var itemMap;
        var request = function (offset) {
            App.scrollToTop();
            if (!U.isNull(offset)) {
            	postParams.offset = offset;
            }
            App.api('/community/tags', postParams, {
                success: function (ps) {
                    var items = ps.items || [];
                    var table = $('#the-table');
                    var template = table.attr('data-template');
                    if (!template) {
                        template = '<tr>' + table.find('tr.template').html() + '</tr>';
                        table.attr('data-template', template);
                    }
                    table.find('tr:not(.header)').remove();
                    itemMap = {};
                    for (var i = 0; i < items.length; i++) {
                        var context = items[i];
                        itemMap[context.id] = context;
                        table.append($(U.template(template, context)));
                    }
                    initOperationButtons();
                    App.pagination(ps, request);
                }
            });
        };
        var initOperationButtons = function() {
        	$('[data-op-type=remove]').click(function(){
        		remove($(this).attr('data-op-key'));
        	});
        	$('[data-op-type=update]').click(function(){
        		update($(this).attr('data-op-key'));
        	});
        };
		var search = function() {
		    postParams = form.serializeObject();
	        request(0);
		};
		var create = function() {
			var name = prompt('请输入标签名称', '');
			if (mars.isEmpty(name)) {
				return;
			}
			App.api('/community/tag_save', {'name': name}).then(function(){
				delete postParams['name'];
	        	request(0);
			});
		};
        var update = function (id) {
			var name = prompt('请输入标签名称', itemMap[id].name || '');
			if (mars.isEmpty(name)) {
				return;
			}
            App.api('/community/tag_save', {
                'id': id,
                'name': name
            }).then(doneAndRequest);
        };
        var remove = function (id) {
            if (!confirm(App.text('confirm.operation'))) {
            	return;
            }
            App.api('/community/tag_remove', {
                'id': id
            }).then(doneAndRequest);
        };
        var doneAndRequest = function() {
        	alert(App.text('done'));
            request();
        };
		var init = function() {
			 form.find('[name="name"]').keydown(function (e) {
	            if (e.keyCode == 13) {
	            	search();
	            }
		     });
			 $('#search-btn').click(search);
			 $('#create-btn').click(create);
			 search();
		};
		init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
    	 <form id="search-form" class="form-inline" action="#" method="post" onsubmit="return false;">
             <input type="hidden" name="limit" ng-model="REQUEST.limit"/>
             <input type="text" name="name" class="form-control" placeholder="标签" data-l10n-for="placeholder"/>
             <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
             <a href="javascript:;" id="create-btn" class="btn btn-warning" data-l10n-id="create"></a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 30%" data-l10n-id="id"></th>
                <th style="width: 50%" data-l10n-id="name"></th>
                <th style="width: 20%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{id}}</td>
                <td>{{name}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:;" data-op-type="update" data-op-key="{{id}}" data-l10n-id="edit"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="remove" data-op-key="{{id}}" data-l10n-id="delete"></a>
                            </li>
                            <li>
                                <a href="#/community/posts?tagId={{id}}&type=tag" data-l10n-id="p.community.post"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>