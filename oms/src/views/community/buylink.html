<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script>
    App.ready(function () {
    	App.menu.select('/community/buylinks');
        var form = $('#save-form');
        var id;
        var previewImage = function (url) {
            if (url) {
                $('#icon-preview').html('<img src="' + url + '"/>');
            } else {
                $('#icon-preview').html('');
            }
            $('input[name="imgUrl"]').val(url);
        };
        var init = function () {
            App.upload.init({
                namespace:'discovery',
                type:'image',
                id: 'upload-btn',
                fileUploaded: function (info) {
                    previewImage(info.url);
                },
                error: function (errTip) {
                    alert('文件上传出错' + errTip);
                }
            });
            var _id = App.router.getParameter('id');
            if (_id) {
                id = _id;
                App.api('/community/buylinks', {id: id}).then(function (result) {
                    form.renderModel({model: result});
                    previewImage(result.imgUrl);
                });
            }
            var save = function (e) {
                var model = form.serializeObject();
                model.id = id;
                App.api('/community/buylink_save', {
                    buylink: JSON.stringify(model)
                }).then(function (result) {
                    App.router.go('/community/buylinks');
                });
            };
            $('.btn-save').click(save);
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-body">
        <form id="save-form" class="form">
            <input type="hidden" name="imgUrl" ng-model="model.imgUrl"/>
            <div class="form-group">
                <label>图片</label>

                <div>
                    <a href="javascript:;" id="upload-btn">+选择上传文件</a>
                </div>
                <div id="icon-preview"></div>
            </div>
            <div class="form-group">
                <label>链接</label>
                <input class="form-control" type="text" name="link" ng-model="model.link" placeholder="链接url"/>
            </div>
            <div class="form-group">
                <label>排序</label>
                <input class="form-control" type="text" name="priority" ng-model="model.priority" placeholder="排序（整数）"/>
            </div>
            <a class="btn btn-primary btn-save" href="javascript:;">保存</a>
            <a class="btn btn-primary" href="javascript:window.history.back();">返回</a>
        </form>
    </div>
</div>