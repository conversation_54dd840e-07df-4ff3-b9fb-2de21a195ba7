<script>
    function remove(id) {
        if (!confirm(App.text('confirm.delete'))) {
            return;
        }
        App.api('/community/removebanner', {
            'id': id
        }).then(function () {
            window.location.reload();
        });
    }
    App.ready(function () {
        var region = App.router.getParameter('region');
        if (!region) {
        	region = "CN";
        }
        var request = function () {
            App.scrollToTop();
            var params = App.router.getParameters();
            App.api('/community/banners', params).then(function (list) {
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>' + table.find('tr.template').html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context.createdAt = U.date.format(U.date.parse(context.createdAt), 'MM-dd HH:mm');
                    context._img = context.img ? '<a href="'+context.img+'" target="_blank"><img src="' +
                			App.previewImgUrl(context.img, 120, 120) + '"/></a>':  null;
                    context._type = App.text('p.community.banner.type.' + context.type);
                    context.tagName = context.tagName || '全部用户';
                    var row = U.template(template, context);
                    table.append(row);
                }
            });
        };
        $('#create-btn').click(function(){
            App.router.go('/community/savebanner');
        });
        request();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <a href="javascript:;" id="create-btn" class="btn btn-success" data-l10n-id="p.community.banner.createlink"></a>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 30%" data-l10n-id="title"></th>
                <th style="width: 10%" data-l10n-id="image"></th>
                <th style="width: 10%" data-l10n-id="type"></th>
                <th style="width: 10%" data-l10n-id="priority"></th>
                <th style="width: 10%" data-l10n-id="userGroup"></th>
                <th style="width: 20%" data-l10n-id="createdat"></th>
                <th style="width: 10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{title}}</td>
                <td>{{_img}}</td>
                <td>{{_type}}</td>
                <td>{{priority}}</td>
                <td>{{tagName}}</td>
                <td>{{createdAt}}</td>
                <td class="operations">
                    <a href="#/community/savebanner?id={{id}}" data-l10n-id="edit"></a>
                    <span class="vertical-bar">|</span>
                    <a href="javascript:remove('{{id}}');" data-l10n-id="delete"></a>
                </td>
            </tr>
        </table>
    </div>
</div>