<script>
    App.ready(function () {
        var remove = function (id) {
            if (!confirm(App.text('confirm.delete'))) {
                return;
            }
            App.api('/community/removepush', {
                'id': id
            }).then(function () {
                window.location.reload();
            });
        };
        var typeMapping = {
            'sms': '短信',
            'web': '链接',
            'text': '文本',
            'post': '宠星文',
            'topic': '话题'
        };
        var deviceMapping = {
            '1': 'Fit',
            '2': 'Mate',
            '3': 'Go',
            '4': 'Feeder',
            '5': 'Cozy',
            '6': 'FeederMini',
            '7': 'T3',
            '8': 'K2',
            '9': 'D3',
            '10': 'Aq',
            '11': 'D4',
            '12': 'P3',
            '13': 'H2',
            '14': 'W5',
            '15': 'T4',
            '16': 'k3',
            '17': 'Aqr',
            '18': 'R2',
            '19': 'AqH1',
        };
        var getTypeString = function (t) {
            var s = typeMapping[t];
            return s ? s : t;
        };
        var showLocalizedString = function (map) {
            var s = '';
            if (!map) {
                return s;
            }
            var i = 0;
            for (var locale in map) {
                if (i > 0) {
                    s += '<br/><br/>';
                }
                s += locale + ":";
                s += '<br/>';
                s += map[locale];
                i++;
            }
            return s;
        };
        var showRecipient = function (map) {
            var s = '';
            if (!map) {
                return s;
            }
            var v;
            for (var key in map) {
                v = map[key];
                if (key == 'regions') {
                    s += '<b>地区:</b>';
                    for (var i = 0; i < v.length; i++) {
                        s += v[i];
                        s += '&nbsp;&nbsp;';
                    }
                } else if (key == 'appVersionMin') {
                    s += '<b>App最低版本号:</b>' + v;
                } else if (key == 'excludeUserIds') {
                    s += '<b>排除用户:</b>' + v;
                }else if (key == 'devices') {
                    s += '<b>限定用户设备:</b>';
                    for (var i = 0; i < v.length; i++) {
                        s += deviceMapping[v[i]];
                        s += '&nbsp;&nbsp;';
                    }
                } else {
                    s += key + ': ' + v;
                }
                s += '<br/>';
            }
            return s;
        };
        var request = function (offset) {
            App.scrollToTop();
            var params = $.extend({
                'limit': 10
            }, App.router.getParameters());
            params.offset = (!U.isNull(offset)) ? offset : params.offset;
            App.api('/community/pushes', params).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>' + table.find('tr.template').html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._sentCount = context.sentCount >= 0 ? (context.sentCount + '/' + context.matchCount) : '';
                    context._createdAt = U.date.format(U.date.parse(context.createdAt), 'MM-dd HH:mm');
                    context._sendAt = U.date.format(U.date.parse(context.sendAt), 'MM-dd HH:mm');
                    context._state = context.state > 0 ? '已发送' : '未发送';
                    context._type = getTypeString(context.type);
                    context._snapshot = showLocalizedString(context.snapshot);
                    context._payload = context.payload ? JSON.stringify(context.payload) : '';
                    context._recipient = '<div class="recipient">' + showRecipient(context.recipient) + "</div>";
                    var row = U.template(template, context);
                    table.append(row);
                }
                App.pagination(ps, request);
            });
        };
        var init = function () {
            request();
            window.remove = remove;
        };
        init();

        $('#plinks').click(function () {
            var params = App.router.getParameters();
            App.router.go('/community/plinks', params);
        });
        $('#psmses').click(function () {
            var params = App.router.getParameters();
            App.router.go('', params);
        });
        $('#ptexts').click(function () {
            var params = App.router.getParameters();
            App.router.go('', params);
        });
    });
</script>
<style>
    .recipient b {
        color: red;
        padding-right: 5px;
    }
</style>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" onsubmit="return false;">
            <a href="#/community/plinks" class="btn btn-primary">推送链接</a>
            <a href="#/community/psmses" class="btn btn-primary">推送短信</a>
            <a href="#/community/ptexts" class="btn btn-primary">推送文本</a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 10%">发送时间</th>
                <th style="width: 10%">发送/匹配数</th>
                <th style="width: 10%">状态</th>
                <th style="width: 10%">类型</th>
                <th style="width: 30%">内容</th>
                <th style="width: 20%">收件人</th>
                <th style="width: 10%">操作</th>
            </tr>
            <tr class="template">
                <td title="{{_createdAt}}">{{_sendAt}}</td>
                <td>{{_sentCount}}</td>
                <td>{{_state}}</td>
                <td>{{_type}}</td>
                <td>{{_snapshot}}</td>
                <td>{{_recipient}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:remove('{{id}}');" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>