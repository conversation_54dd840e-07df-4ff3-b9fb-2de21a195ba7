
<script>
	App.ready(function() {
		var remove = function(id) {
	        if (!confirm(App.text('confirm.delete'))) {
	            return;
	        }
			App.api('/community/removeplink', {
				'id' : id
			}).then(function() {
				window.location.reload();
			});
		};
        App.menu.select('/community/pushes');

        var push = function(id) {
			App.router.go('/community/push?type=web&resourceId=' + id);
		};
		window.push = push;
		var showLocalizedString = function(map) {
			var s = '';
			if (!map) {
				return s;
			}
			var i = 0;
			for ( var locale in map) {
				if (i > 0) {
					s += '<br/><br/>';
				}
				s += locale + ":";
				s += '<br/>';
				s += map[locale];
				i++;
			}
			return s;
		};
		var request = function(offset) {
			App.scrollToTop();
			var params = $.extend({
				'limit' : 10
			}, App.router.getParameters());
			params.offset = (!U.isNull(offset)) ? offset : params.offset;
			App.api('/community/plinks', params).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
					context._body = showLocalizedString(context.body);
					context._url = '<a href="' + context.url + '" target="_blank">' + context.url + '</a>';
					var icon = App.previewImgUrl(context.imgUrl, 120, 120);
					if (!icon) {
						context._img = null;
					} else {
						context._img = '<img class="plink-img" src="' + icon + '">';
					}
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
			});
		};
		var init = function() {
			request();
			//init buttons
			window.remove = remove;
		};
        $('#plink').click(function () {
            var params = App.router.getParameters();
            App.router.go('', params);
        });
		init();
	});
</script>
<style>
#the-table img.plink-img {
	width: 100px;
	height: auto;
}
</style>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" onsubmit="return false;">
			<a href="#/community/plink"  class="btn btn-primary">创建推送链接</a>
			<a href="javascript:window.history.back();" class="btn btn-warning" >返回</a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 15%">创建时间</th>
				<th style="width: 15%">配图</th>
				<th style="width: 20%">链接</th>
				<th style="width: 40%">内容</th>
				<th style="width: 10%">操作</th>
			</tr>
			<tr class="template">
				<td>{{_createdAt}}</td>
				<td>{{_img}}</td>
				<td>{{_url}}</td>
				<td>{{_body}}</td>
				<td class="operations">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li>
								<a href="javascript:push('{{id}}');">推送</a>
							</li>
							<li>
								<a href="javascript:remove('{{id}}');" data-l10n-id="delete"></a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>