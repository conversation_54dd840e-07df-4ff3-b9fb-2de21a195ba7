<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<link rel="stylesheet" type="text/css" href="vendor/select2/select2.min.css"/>
<script src="vendor/select2/select2.min.js"></script>

<style>
#img-preview img{
	width: 80px;
}
.post-img {
  float: left;
  padding-left: 10px;
}
.post-img img {
  display: block;
  width: 60px;
  height: 60px;
  border:1px solid #CCC;
  margin-bottom: 5px;
}
.post-img a {
  font-size: 90%;
}
</style>
<script>
App.ready(function() {
	App.menu.select('/community/banners');
	var form = $('#save-form');
	var _model = null;
	var bannerResult = null;
	var userGroupList = [];

	var setUserGroupEstimateNumber = function (ids) {
		const userGroups = userGroupList.filter(item => (ids || []).indexOf(item.id) > -1);
		$('#estimate-number').text(userGroups.reduce((prev, curr) => prev + curr.estimate, 0));
	};
	
	var previewImg = function(url){
		if (url) {
			$('#img-preview').html('<img src="'+url+'"/>');
		} else {
			$('#img-preview').html('');
		}
		form.find('[name="img"]').val(url);
	};

	var loadUserGroups = function () {
		return App.api('tag/user/list', {offset: 0, limit: 1000000}, {server: 'new-api'}).then(function (result) {
			var select = form.find('[name="tagId"]')
			select.append('<option style="display:none;" value="">请选择用户分群</option>');
			userGroupList = result.items;
			var tagIds = bannerResult && bannerResult.tagId ? bannerResult.tagId.split(',').map(item => +item) : [];
			setUserGroupEstimateNumber(tagIds);
			userGroupList.filter(item => item.estimate >= 0 && item.enable === 1).map(item => {
				if (bannerResult && tagIds.indexOf(item.id) > -1) {
					select.append('<option selected value="' + item.id + '">' + item.name + '</option>');
				} else {
					select.append('<option value="' + item.id + '">' + item.name + '</option>');
				}
			});
			select.select2({width: '500px'});
		});
	};

	App.upload.init({
		namespace:'banner',
		type:'image',
		id: 'upload-btn',
		fileUploaded: function (info) {
			if (typeof info == 'string') {
				info = JSON.parse(info);
			}
			previewImg(info.url);
		},
		error: function (errTip) {
			alert(App.text('network.error'));
		}
	});

	var loadModel = function() {
		return App.api('/community/banner', App.router.getParameters() , {
			success: function(result) {
				if ($.isEmptyObject(result) || $.isEmptyObject(result.banner)) {
					alert('Bad request');
					return false;
				}
				bannerResult = result.banner;
				var banner = result.banner;
				_model = banner;
				banner._type = App.text('p.community.banner.type.' + banner.type);
				form.renderModel({model: banner});
				previewImg(banner.img);
				
				var payload = result.payload || {};
				if (banner.type == 1) {
					var template = '<div class="post-img">'
		           	+	'<img src="{{img}}"/>'
		           	+	'<a href="javascript:previewImg(\'{{img}}\');">{{previewText}}</a>'
			        +'</div>';
					var previewText = App.text('p.community.set.as.cover');
					var imgsHTML = '';
					$.each(payload.imgs || [], function(index, item){
						imgsHTML+= U.template(template,{'img': item, 'previewText': previewText});
					});
					payload._imgs = imgsHTML;
				}
				var payloadContainer = $('[data-payload-type="'+banner.type+'"]');
				payloadContainer.renderTemplate({payload: payload, model: banner});
				payloadContainer.removeClass('none');

				// 需要默认选中用户分群相关radio
				if (banner.tagId) {
					$('#userGroup').prop('checked', true);
					$('#user-group-selector').css('display', 'block');
					$('#user-group-estimate-number').css('display', 'block');
				} else {
					$('#all').prop('checked', true);
					$('#user-group-estimate-number').css('display', 'none');
				}

				return banner;
			}
		});
	};
	
	var initButtons = function() {
		$('#save-btn').click(function(){
			var data = form.serializeObject();
			// 选择用户分群的时候，需要存在tagId，否则提示
            if (data.userGroup && !data.tagId) {
                alert("请选择用户分群");
                return ;
            }

            // 用户分群如果选择了全部，则需要手动删除一下tagId
            if (data.all) {
                delete data['tagId'];
            } else if (typeof data.tagId !== 'string') {
                data.tagId = data.tagId.join();
            }
			delete data['all'];
			delete data['userGroup'];
	 		App.api('/community/savebanner', data).then(function() {
	 			App.router.go('/community/banners');
	 		});
		});
	};
	var initRadioButtons = () => {
		$('input[type="radio"]').on('click', function(ev) {
			$(ev.target).siblings('input').removeProp('checked');
			const value = $(ev.target).val();
			// 只有两个值：all，userGroup
			if (value === 'userGroup') {
				$('#user-group-selector').css('display', 'block');
				$('#user-group-estimate-number').css('display', 'block');
			} else {
				$('#user-group-selector').css('display', 'none');
				$('#user-group-estimate-number').css('display', 'none');
			}
		});
	};
	var initUserGroupSelector = () => {
		$('[name="tagId"]').change(function() {
			const tagIds = $('[name="tagId"]').val().map(item => +item);
			setUserGroupEstimateNumber(tagIds);
		});
	};
	var init = function() {
		loadModel().done(function(){
			loadUserGroups();
			initRadioButtons();
			initButtons();
			initUserGroupSelector();
			window.previewImg = previewImg;
		});
	};
	init();
});
</script>

<form id="save-form" role="form" onsubmit="return false;">
	   <input type="hidden" name="id" ng-model="model.id"/>
	   <input type="hidden" name="type" ng-model="model.type"/>
	   <input type="hidden" name="img" ng-model="model.img"/>
	   <div class="form-group">
         	<label data-l10n-id="type"></label>
            <input type="text" class="form-control" ng-model="model._type" disabled="disabled"/>
       </div>
        <div class="form-group">
         	<label data-l10n-id="priority"></label>
            <input type="text" name="priority" class="form-control" ng-model="model.priority"/>
            <p class="help-block" data-l10n-id="priority.tip"></p>
       </div>
 	   <div class="form-group">
         	<label data-l10n-id="title"></label>
            <input type="text" name="title" class="form-control" ng-model="model.title"/>
       </div>
       <div class="form-group">
           <label data-l10n-id="p.community.cover"></label>
		   <div>
			   <a href="javascript:;" id="upload-btn" data-l10n-id="file.selector"></a>
		   </div>
           <div id="img-preview"></div>
       </div>
       
	   <div class="form-group none" data-payload-type="1">
        	<label data-l10n-id="p.community.post"></label>
            <input type="hidden" name="post" class="form-control" value="{{payload.id}}"/>
            <div id="post-details">
                <div class="clear" style="height:5px;"></div>
                <p>{{payload.detail}}</p>
                <div class="clear" style="height:5px;"></div>
                {{payload._imgs}}
                <div class="clear"></div>
            </div>
            <div id="post-details-imgs" class="none"></div>
	   </div>
 	   <div class="form-group none" data-payload-type="2">
	         <label data-l10n-id="link"></label>
             <input type="text" name="link" class="form-control" value="{{model.link}}"/>
	   </div>
       <div class="form-group none" data-payload-type="3">
	         <label data-l10n-id="p.community.topic"></label>
              <input type="hidden" name="topicId" class="form-control" value="{{payload.topicId}}"/>
             <input type="text" class="form-control" value="{{payload.name}}" readonly="readonly"/>
		</div>

		<div class="form-group">
			<label class="title">目标人群 <b>*</b>：</label>
			<div style="display: flex;align-items: center;">
				<input style="vertical-align: middle;margin-top: 0;" id="all" type="radio" name="all" value="all"/><label for="all" style="padding:0 10px;font-weight: normal;margin-bottom: 0;">全部用户</label>
				<input style="vertical-align: middle;margin-top: 0;" id="userGroup" type="radio" name="userGroup" value="userGroup"/><label for="userGroup" style="padding:0 10px;font-weight: normal;margin-bottom: 0;">用户分群</label>
			</div>
			<div style="display: none; margin-top: 5px;" id="user-group-selector">
				<select multiple name="tagId" class="form-control" onSelect=""></select>
				<p style="margin: 10px 0;  margin-top: 5px;">
					没有想要的用户分群？去
					<a style="cursor: pointer;" href="#/user/group?action=create">新增用户分群</a>
				</p>
			</div>
		</div>

		<div class="form-group" id="user-group-estimate-number" style="display: none;">
			<label>预估人数:</label>
			<span id="estimate-number">0</span>
		</div>
		
       <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
</form>