<script src="js/search_add_panel.js"></script>
<style type="text/css">
</style>
<script>
	App.ready(function() {
		//menu
		App.menu.select('/community/posts');
		
		var form = $('#save-form');
		var panel = new SearchAddPanel('tag', {'api': '/community/tag_search', 'initApi': '/community/tag_search',
				'initApiParams': {'limit': 10}});
		var src = App.router.getParameter('src');
		var saveApi = (src == '1') ? 'post_remove_from_plaza' : 'post_tag_update';
		
		var save = function() {
			var data = form.serializeObject();
			var tagIds = [];
			var tags = panel.getValue();
			$.each(tags, function(index, item){
				tagIds.push(item.id);
			});
			data.tagIds = tagIds.join(',');
			
			App.api('/community/' + saveApi, data).then(function(){
				window.history.back();
			});
		};
		
		var init = function() {
            App.api('/community/post', {'id': App.router.getParameter('postId'), 'withTag': true}, {
                success: function (result) {
                    form.renderModel(result);
                    panel.setValue(result.tags);
                }
            });
            if (src == '1') {
            	$('#save-btn').text('从广场中移除');
            }
            $('#save-btn').click(save);
		};
		
		init();
	});
</script>

<div class="panel panel-default x-panel">
	<div class="panel-body">
		<form id="save-form" role="form" onsubmit="return false;">
			<input type="hidden" name="postId" ng-model="post.id" />
			<div class="form-group">
				<label>发贴人：</label>
				<div class="controls">
					<input type="text" class="form-control" ng-model="post.author.nick" disabled="disabled"/>
				</div>
			</div>
			<div class="form-group">
				<label>内容：</label>
				<div class="controls">
					<textarea class="form-control" rows="5" ng-model="post.detail" disabled="disabled"></textarea>
				</div>
			</div>
			<div class="form-group">
				<label>标签：</label>
				<div class="panel panel-default">
					<div class="panel-body">
						<div class="row" style="margin-bottom:10px;">
							<div class="col-md-4">
								<input type="text" class="form-control" placeholder="标签名称" id="tag-search-input"/>
							</div>
							<div class="col-md-2">
								<button class="btn btn-primary" id="tag-search-btn" data-l10n-id="search"></button>
							</div>
						</div>
						<div class="row">
							<div class="col-md-5">
								<label>搜索结果：</label>
								<select multiple class="form-control" id="tag-search-result-select" style="height:200px;">
								</select>
							</div>
							<div class="col-md-2" style="text-align:center;">
								<div>
									<button class="btn btn-primary" id="tag-add-btn" style="margin-top:60px;margin-bottom:50px;">添加</button>
								</div>
							</div>
							<div class="col-md-5">
								<label>已添加标签：</label>
								<div class="panel panel-default" id="tag-selected-panel" style="height:200px;overflow-y:scroll;padding:10px 10px;"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		
			<div class="form-group">
				<label></label>
				<div class="controls">
					<a href="javascript:;" id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
				</div>
			</div>
		</form>
	</div>
</div>