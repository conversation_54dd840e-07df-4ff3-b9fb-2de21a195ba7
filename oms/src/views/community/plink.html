<script src="js/localespec.js"></script>
<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>

<script>
	App.ready(function() {
		App.menu.select('/community/pushes');

		var form = $('#save-form');

		var previewImage = function(url) {
			if (url) {
				$('#img-preview').html('<img src="'+url+'"/>');
			} else {
				$('#img-preview').html('');
			}
			form.find('[name="imgUrl"]').val(url);
		};

		App.upload.init({
			namespace:'plink',
			type:'image',
			id: 'upload-btn',
			fileUploaded: function (info) {
				if (typeof info == 'string') {
					info = JSON.parse(info);
				}
				previewImage(info.url);
			},
			error: function (errTip) {
				alert('文件上传出错' + errTip);
			}
		});

		window.addLocaleSpec = function(locale) {
			var spec = LocaleSpec.get(locale);
			App.openDialog({
				url : App.router.getRealPath('/community/plinklocalespec.html'),
				context : {
					spec : spec
				},
				noHeader : true,
				primaryClick : LocaleSpec.save,
				beforeRender : function(body) {
					var select = body.find('[name="locale"]');
					$.each(LocaleSpec.getLocaleList(), function(index, item) {
						select.append('<option value="'+item.key+'">'
								+ item.name + '</option>');
					});
				}
			});
		};

		var save = function() {
			var data = form.serializeObject();
			var specs = LocaleSpec.getAllByKeys();
			for ( var key in specs) {
				var spec = specs[key];
				data[key] = spec;
			}
			App.api('/community/saveplink', {
				'model' : JSON.stringify(data)
			}, {
				success : function() {
					App.router.go('/community/plinks');
				}
			});
		};

		var renderModel = function(model) {
			App.renderPage({
				'model' : model
			});
			previewImage(model.imgUrl);
			var localizedKeys = [ 'body' ];
			var localizedProps = {};
			for (var i = 0; i < localizedKeys.length; i++) {
				var key = localizedKeys[i];
				localizedProps[key] = model[key];
			}
			LocaleSpec.setAllByKeys(localizedProps);
		};

		var loadModel = function() {
			var id = App.router.getParameter('id');
			if (!id) {
				model = {};
				renderModel(model);
				return;
			}
			return App.api('/community/plink', {
				'id' : id
			}, {
				success : function(data) {
					model = data;
					if ($.isEmptyObject(model)) {
						return;
					}
					renderModel(model);
				}
			});
		};

		//init
		LocaleSpec.loadLocales().then(loadModel).then(function() {
			$('#save-btn').click(save);
			$('#add-locale-btn').click(addLocaleSpec);
		});
	});
</script>
<style type="text/css">
#img-preview img{
	max-width: 200px;
}
</style>

<form id="save-form" role="form" onsubmit="return false;">
	<input type="hidden" name="id" ng-model="model.id" /> <input type="hidden" name="imgUrl" ng-model="model.imgUrl" />
	<div class="form-group">
		<label>URL</label> <input type="text" name="url" class="form-control" ng-model="model.url" />
	</div>
	<div class="form-group">
		<label>配图</label>
		<div class="controls">
			<div>
				<a href="javascript:;" id="upload-btn">+选择上传文件</a>
			</div>
			<div id="img-preview"></div>
		</div>
	</div>
	<div class="form-group">
		<div class="controls">
			<a href="javascript:;" id="add-locale-btn"><span>+</span><span data-l10n-id="localize"></span></a>
			<div id="locale-spec-template">
				<div class="locale-spec" data-locale="{{locale.key}}">
					<div class="locale-spec-item">
						<label data-l10n-id="locale"></label>:<span class="locale">{{locale.name}}</span> <a
							href="javascript:addLocaleSpec('{{locale.key}}');" class="edit" data-l10n-id="edit"></a> <a
							href="javascript:LocaleSpec.remove('{{locale.key}}');" class="edit" data-l10n-id="delete"></a>
					</div>
					<div class="locale-spec-item">
						<span>{{spec.body}}</span>
					</div>
				</div>
			</div>
			<div id="locale-specs"></div>
		</div>
	</div>
	<a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
	<a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
</form>