<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>

<style type="text/css">
.remove-tag-btn {
	margin-left: 10px;
	color: red;
}
#imgs-preview .item{
	position:relative;
	width:320px;
	margin:10px 0 0 0;
}
#imgs-preview .item .tclose{
	top:-15px;
	right: -15px;
}
#imgs-preview .item img{
	width:320px;
}
#preview-cover{
	display:block;
	width:320px;
	margin:0 0 10px 0;
	border:1px solid #CCC;
}
</style>
<script>
App.ready(function() {
	var form = $('#save-form');
	var save = function() {
		{
			var imgs = [];
			$('#imgs-preview').find('img').each(function(){
				imgs.push(this.src);
			});
			form.find('[name=imgs]').val(imgs.join(','));
		}
		var data = form.serializeObject();
 		App.api('/community/pushweb', data).then(function(){
 			App.router.go('/community/pushes');
 		});
	};
	
	
  var inits = function() {
	  return App.api('/community/findCategory').then(function(result){
		  var select = form.find('[name="parentId"]');
		  $.each(result, function(index, item) {
				select.append('<option value="'+item.id+'">'+item.name+'</option>');
			});
	  });
  };
  
	var addImg = function(url){
		var preview = $('<img/>');
		preview.attr('src',url);
		var wrap = $('<div class="item"/>');
		var close = $('<div class="tclose"/>');
		close.click(function(){
			$(this).parent().remove();
		});
		wrap.append(close);
		wrap.append(preview);
		$('#imgs-preview').append(wrap);
	};

	App.upload.init({
		namespace:'post',
		type:'image',
		id: 'upload-btn',
		fileUploaded: function (info) {
			if (typeof info == 'string') {
				info = JSON.parse(info);
			}
			addImg(info.url);
		},
		error: function (errTip) {
			alert('文件上传出错' + errTip);
		}
	});

  //init
  inits().then(function(){
	  $('#save-btn').click(save);
  });
}); 
</script>

<form id="save-form" role="form" onsubmit="return false;">
	<div class="form-group">
	    <label>内容</label>
	    <input  name="body" class="form-control"></input>
	 </div>
		 <div class="form-group">
	     <label>url</label>
	      <input  name="url" class="form-control"></input>
	 </div>
     </div>
      <div class="form-group">
	         <label>图片：</label>
	         <div class="controls postimgs">
				<input type="hidden" name="imgs"/>
				 <div>
					 <a href="javascript:;" id="upload-btn">+选择上传文件</a>
				 </div>
	            <div id="imgs-preview"></div>
	         </div>
       </div>
     <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
</form>