<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>

<style type="text/css">
    .remove-tag-btn {
        margin-left: 10px;
        color: red;
    }

    #imgs-preview .item {
        position: relative;
        width: 320px;
        margin: 10px 0 0 0;
    }

    #imgs-preview .item .tclose {
        top: -15px;
        right: -15px;
    }

    #imgs-preview .item img {
        width: 320px;
    }

    #preview-cover {
        display: block;
        width: 320px;
        margin: 0 0 10px 0;
        border: 1px solid #CCC;
    }
</style>
<script>
    App.ready(function () {
    	App.menu.select('/community/topics');
        var form = $('#save-form');
        var save = function () {
            {
                var imgs = [];
                $('#imgs-preview').find('img').each(function () {
                    imgs.push(this.src);
                });
                form.find('[name=imgs]').val(imgs.join(','));
            }
            var data = form.serializeObject();
            var checked = $("[ng-model='topic.joinPrivilege']").prop('checked');
            data.joinPrivilege = !checked;
            App.api('/community/savetopic', data).then(function () {
                App.router.go('/community/topics');
            });
        };
        var addImg = function (url) {
            var preview = $('<img/>');
            preview.attr('src', url);
            var wrap = $('<div class="item"/>');
            var close = $('<div class="tclose"/>');
            close.click(function () {
                $(this).parent().remove();
            });
            wrap.append(close);
            wrap.append(preview);
            $('#imgs-preview').append(wrap);
        };
        //init
        var loadTopic = function() {
       	      var topicId = App.router.getParameter('topicId');
              if (topicId) {
              	return App.api('/community/topic', {'id': topicId}, {
                      success: function (topic) {
                          if (topic.imgs != null) {
                            addImg(topic.imgs);
                          }
                          console.log(topic);
                          form.renderModel({topic: topic});
                          $("[ng-model='topic.joinPrivilege']").prop('checked', !topic.joinPrivilege);
                      }
                  });
              } else {
            	  form.find('[name="topicname"]').removeAttr('disabled');
            	  var defer = $.Deferred();
            	  defer.resolve();
            	  return defer;
              }
        };
        var init = function () {
            App.upload.init({
                namespace: 'post',
                type: 'image',
                id: 'upload-btn',
                fileUploaded: function (info) {
                    if (typeof info == 'string') {
                        info = JSON.parse(info);
                    }
                    addImg(info.url);
                },
                error: function (errTip) {
                    alert(App.text('network.error'));
                }
            });
            loadTopic().done(function(){
            	$('#save-btn').click(save);
            });
        };
        init();
    });
</script>

<form id="save-form" role="form" onsubmit="return false;">
	<input type="hidden" name="topicId" ng-model="topic.topicId"/>
    <div class="form-group">
        <label data-l10n-id="name"></label>
        <input name="topicname" class="form-control" ng-model="topic.topicname" disabled="disabled"/>
    </div>
    <div class="form-group">
        <label data-l10n-id="detail"></label>
        <textarea name="describe" class="form-control" rows="5" ng-model="topic.describe"></textarea>
    </div>
    </div>
    <div class="form-group">
        <label data-l10n-id="image"></label>
        <div>
            <a href="javascript:;" id="upload-btn" data-l10n-id="file.selector"></a>
        </div>
        <div class="controls postimgs">
            <input type="hidden" name="imgs"/>
            <div id="imgs-preview"></div>
        </div>
    </div>
    <div class="form-group">
        <label data-l10n-id="privilege"></label>
        <input type="checkbox" ng-model="topic.joinPrivilege">
    </div>
    <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
    <a href="#/community/topics" class="btn btn-info" data-l10n-id="cancel"></a>
</form>