<style type="text/css">
    .x-table img.postimg {
        padding: 10px 10px 0 0;
    }
    .x-table img.emoji {
        width: 20px;
        height: 20px;
    }
</style>
<script src="js/community/post.js"></script>
<script>
    App.ready(function () {
        var form = $('#search-form');
        var requestParams = $.extend({'limit': 20, 'type': 'latest'}, App.router.getParameters());
        form.renderModel(requestParams);
        var postType = function(p) {
        	 if (p.link) {
        		 return App.text('link');
        	 }
        	 if (p.video) {
        		 return App.text('p.community.short.video');
        	 }
        	 if (p.imgs) {
        		 return App.text('image');
        	 }
        	 return App.text('p.community.text');
        };
        var request = function (offset) {
            App.scrollToTop();
            if (!U.isNull(offset)) {
            	requestParams.offset = offset;
            }
            App.api('/community/posts', requestParams, {
                success: function (ps) {
                    var items = ps.items || [];
                    var table = $('#the-table');
                    var template = table.attr('data-template');
                    if (!template) {
                        template = '<tr>' + table.find('tr.template').html() + '</tr>';
                        table.attr('data-template', template);
                    }
                    table.find('tr:not(.header)').remove();
                    for (var i = 0; i < items.length; i++) {
                        var context = items[i];
                        if (!context.author.nick) {
                        	context.author.nick = 'u' + context.author.id;
                        }
                        context.type = postType(context);
                        context.detail = postAsText(context);
                        context.createdAt = U.date.format(U.date.parse(context.createdAt), 'MM-dd HH:mm');
                        context.hideText = App.text(context.hide ? 'yes': 'no');
                        context.visibilityText = App.text(context.visibility === 1 ? 'yes': 'no');
                        context.hideOperation = App.text(context.hide ? 'p.community.remove.hidden': 'p.community.make.hidden');
                        context.recommendText = App.text(context.recommend ? 'yes': 'no');
                        context.recommendOperation = App.text(context.recommend ? 'p.community.remove.recommended': 'p.community.make.recommended');
                        table.append($(U.template(template, context)));
                    }
                    initOperationButtons();
                    App.pagination(ps, request);
                }
            });
        };
        var searchIt = function () {
            requestParams = form.serializeObject();
            var type = requestParams['type'];
            $('[data-posts-btn]').removeClass('btn-warning');
            $('#' + type + '-posts-btn').addClass('btn-warning');
            request(0);
        };
        var doneAndRequest = function() {
        	alert(App.text('done'));
        	request();
        };
        var hide = function (id, state) {
     /*    	var text = state > 0 ? 'p.community.hide.confirm' : 'p.community.remove.hidden.confirm';
            if (!confirm(App.text(text))) {
            	return;
            } */
            if (state == 0) {
            	var text = App.text('p.community.remove.hidden.confirm');
            	if (!confirm(text)) {
            		return;
            	}
            	doHide(id, state);
            } else {
            	  App.openDialog({
                      url: App.router.getRealPath('/community/hide_reason.html'),
                      noHeader: true,
                      primaryClick: function() {
                    	  var reason = $('#dialog-form').serializeObject()['reason'];
                    	  if (mars.isNull(reason) || reason.length == 0) {
                    		  alert('请填写理由');
                    		  return;
                    	  }
                    	  App.closeDialog();
                    	  doHide(id, state, reason);
                      }
                  });
            }
        };
        var doHide = function(id, state, reason) {
        	  App.api('/community/hideorshow', {
                  'postId': id,
                  'state': state,
                  'reason': mars.isNull(reason) ? '' : reason
              }).then(doneAndRequest);
        };
        var recommend = function(id, state) {
        	var text = state > 0 ? 'p.community.recommend.confirm' : 'p.community.remove.recommended.confirm';
            if (!confirm(App.text(text))) {
            	return;
            }
            App.api('/community/recommend', {
                'state': state,
                'postId': id
            }).then(doneAndRequest);
        };
        var push = function(id) {
            App.router.go('/community/push', {
                'type': 'post',
                'resourceId': id
            });
        };
        var updateTag = function(id, src) {
            App.router.go('/community/post_save_tags', {
                'postId': id,
                'src': src
            });
        };
        var initOperationButtons = function() {
        	$('[data-op-type="push"]').click(function(){
        		push($(this).attr('data-op-key'));
        	});
        	$('[data-op-type="recommend"]').click(function(){
        		recommend($(this).attr('data-op-key'), 1);
        	});
        	$('[data-op-type="remove-recommended"]').click(function(){
        		recommend($(this).attr('data-op-key'), 0);
        	});
         	$('[data-op-type="hide"]').click(function(){
         		hide($(this).attr('data-op-key'), 1);
        	});
        	$('[data-op-type="remove-hidden"]').click(function(){
        		hide($(this).attr('data-op-key'), 0);
        	});
        	$('[data-op-type="update-tag"]').click(function(){
        		updateTag($(this).attr('data-op-key'), 0);
        	});
        	$('[data-op-type="remove-from-plaza"]').click(function(){
        		updateTag($(this).attr('data-op-key'), 1);
        	});
    		$('[data-op-display="hide1"]').parent().addClass('none');
    		$('[data-op-display="remove-hidden0"]').parent().addClass('none');
    		$('[data-op-display="recommend1"]').parent().addClass('none');
    		$('[data-op-display="remove-recommended0"]').parent().addClass('none');
        };
		//init
		var searchAuthoredPosts = function() {
			var authorId = form.find('[name="authorId"]').val();
			if (!authorId) {
				return;
			}
			form.find('[name="type"]').val('authored');
			searchIt();
		};
		var init = function() {
			 form.find('[name="authorId"]').keydown(function (e) {
	            if (e.keyCode == 13) {
	            	searchAuthoredPosts();
	            }
		     });
			$('#authored-posts-btn').click(searchAuthoredPosts);
			$('#latest-posts-btn').click(function(){
				form.find('[name="type"]').val('latest');
				form.find('[name="authorId"]').val('');
				searchIt();
			});
	        $('#recommended-posts-btn').click(function(){
				form.find('[name="type"]').val('recommended');
				form.find('[name="authorId"]').val('');
				searchIt();
			});
           $('#post-btn').click(function () {
               App.router.go('/community/savepost');
           });
           searchIt();
		};
		init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" method="post" onsubmit="return false;">
            <input type="hidden" name="type" ng-model="type"/>
            <input type="hidden" name="topicId" ng-model="topicId"/>
            <input type="hidden" name="tagId" ng-model="tagId"/>
            <input type="hidden" name="limit" ng-model="limit"/>
            <input type="text" name="authorId" ng-model="authorId" class="form-control" data-l10n-id="p.user.id" data-l10n-for="placeholder"/>
            <a href="javascript:;" id="authored-posts-btn" class="btn btn-primary" data-posts-btn="1" style="margin-left: 10px;" data-l10n-id="search"></a>
            <a href="javascript:;" id="latest-posts-btn" class="btn btn-primary" data-posts-btn="1" style="margin-left: 10px;" data-l10n-id="p.community.roster.latest"></a>
            <a href="javascript:;" id="recommended-posts-btn" class="btn btn-primary" data-posts-btn="1" style="margin-left: 10px;" data-l10n-id="p.community.roster.recommend"></a>
            <a href="javascript:;" id="post-btn" class="btn btn-success" style="margin-left: 10px;" data-l10n-id="create"></a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 15%" data-l10n-id="createdby"></th>
                <th style="width: 10%" data-l10n-id="type"></th>
                <th style="width: 35%" data-l10n-id="detail"></th>
                <th style="width: 10%" data-l10n-id="createdat"></th>
                <th style="width: 20%" data-l10n-id="p.community.recommend.and.hide"></th>
                <th style="width: 10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td><a href="#/user/users?username={{author.id}}" target="_blank">{{author.nick}}</a></td>
                <td>{{type}}</td>
                <td>{{detail}}</td>
                <td>{{createdAt}}</td>
                <td><a href="#/community/post?id={{id}}">{{recommendText}}/{{hideText}}/{{visibilityText}}</a></td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu  dropdown-menu-right">
                            <li>
                                <a href="#/community/post?id={{id}}" data-l10n-id="detail"></a>
                            </li>
                            <li>
                                <a href="#/community/savepost?id={{id}}" data-l10n-id="edit"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-display="hide{{hide}}" data-op-type="hide" data-op-key="{{id}}" data-l10n-id="p.community.make.hidden"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-display="remove-hidden{{hide}}" data-op-type="remove-hidden" data-op-key="{{id}}" data-l10n-id="p.community.remove.hidden"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-display="recommend{{recommend}}" data-op-type="recommend" data-op-key="{{id}}" data-l10n-id="p.community.make.recommended"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-display="remove-recommended{{recommend}}" data-op-type="remove-recommended" data-op-key="{{id}}" data-l10n-id="p.community.remove.recommended"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="update-tag" data-op-key="{{id}}" data-l10n-id="p.community.update.tag"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="remove-from-plaza" data-op-key="{{id}}" data-l10n-id="p.community.remove.from.plaza"></a>
                            </li>
                            <li>
                                <a href="#/community/savebanner?type=1&post={{id}}" data-l10n-id="p.community.set.as.banner"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="push" data-op-key="{{id}}" data-l10n-id="p.community.push"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>