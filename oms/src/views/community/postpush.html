<style>
#img-preview img{
	width: 80px;
}
.post-img {
  float: left;
  padding-left: 10px;
}
.post-img img {
  display: block;
  width: 60px;
  height: 60px;
  border:1px solid #CCC;
  margin-bottom: 5px;
}
.post-img a {
  font-size: 90%;
}
</style>
<script>
App.ready(function() {
	var form = $('#save-form');
	var loadModel = function() {
	};
	
	var initButtons = function() {
		$('#save-btn').click(function(){
			var data = form.serializeObject();
	 		App.api('/community/pushpost', data).then(function() {
	 			App.router.go('/community/banners');
	 		});
		});
	};
	
	$.when(loadModel()).then(initButtons);
});
</script>

<form id="save-form" role="form" onsubmit="return false;">
	   <input type="hidden" name="id" ng-model="model.id"/>
	   <input type="hidden" name="type" ng-model="model.type"/>
	   <div class="form-group">
         	<label>类型：</label>
            <input type="text" class="form-control" ng-model="model._type" disabled="disabled"/>
       </div>
 	   <div class="form-group">
         	<label>标题：</label>
            <input type="text" name="title" class="form-control" ng-model="model.title"/>
       </div>
       <a id="save-btn" class="btn btn-primary">保存</a>
</form>