<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script src="js/suggest.js"></script>

<style type="text/css">
.suggest-container{
	z-index:2000;position:absolute;
	border:1px solid #000;background:#FFFFFF;
}
.suggest-item{
	z-index:2001;
	background:#FFF;
	color:#000;
	cursor:default;
	height: 40px;
	line-height: 40px;
	white-space:nowrap;
	overflow:hidden;
}
.suggest-item-on{
	background:#3366CC;color:#FFF;cursor:pointer;
}
.suggest-item img {
	width: 40px;
	height: 40px;
	padding-right: 5px;
}

.remove-tag-btn {
	margin-left: 10px;
	color: red;
}

#imgs-preview .item {
	position: relative;
	width: 320px;
	margin: 10px 0 0 0;
}

#imgs-preview .item .tclose {
	top: -15px;
	right: -15px;
}

#imgs-preview .item img {
	width: 320px;
}

#preview-cover {
	display: block;
	width: 320px;
	margin: 0 0 10px 0;
	border: 1px solid #CCC;
}

#at-users .item {
	float: left;
	position: relative;
	margin: 20px 20px 0 0;
}
#at-users .item .tclose {
	top: -25px;
	right: -25px;
}
</style>
<script>
	App.ready(function() {
		//menu
		App.menu.select('/community/posts');
		
		var form = $('#save-form');
		var atBox = form.find('[id="at"]');
		var postImgs = [];
		var video = null;

		var addImg = function(info) {
			if (info.imageInfo) {
				info.w = info.imageInfo.width;
				info.h = info.imageInfo.height;
				delete info.imageInfo;
			}
			postImgs.push(info);
      var wrap = $('<div class="item"/>');
			var preview = $('<img/>');
      var close = $('<div class="tclose"/>');
			preview.attr('src', info.url);
			close.on('click', info, function() {
				for (var i = 0; i < postImgs.length; i++) {
					if (postImgs[i] == info) {
						postImgs.splice(i, 1);
						break;
					}
				}
				$(this).parent().remove();
			});
			wrap.append(close);
			wrap.append(preview);
			$('#imgs-preview').append(wrap);
		};

    var addVideo = function(info) {
      video = info;
      delete info;
      var wrap = $('<div class="item"/>');
      var preview = $('<video height="100%" width="100%" controls="controls"/>');
      preview.attr('src', info.url);
      var close = $('<div class="tclose"/>');
      close.on('click', info, function() {
        video = null;
        $(this).parent().remove();
      });
      wrap.append(close);
      wrap.append(preview);
      $('#imgs-preview').append(wrap);
    };

		var appendAtUser = function(data) {
			atBox.val('');
			var wrap = $('<div class="item"/>');
			wrap.attr('data-user-id', data.id);
			wrap.attr('data-user-nick', data.nick);
			var span = $('<span/>');
			span.html('<a href="#/user/users?username=#'+ data.id+'" target="_blank">' + atUserHtml(data) + "</a>");
			var close = $('<div class="tclose"/>');
			close.click(function() {
				$(this).parent().remove();
			});
			wrap.append(span);
			wrap.append(close);
			$('#at-users').append(wrap);
		};

		var initPost = function() {
			return App.api('/community/post', App.router.getParameters(), {
				success : function(post) {
					$('#save-form').renderModel({
						post : post
					});
					$.each(post.images || [], function(index, item) {
						addImg(item);
					});
					$.each(post.topics || [],function(i,topic){
						var html='<div class="alert alert-warning alert-dismissible" role="alert" style="padding:5px 20px;margin-bottom:5px;" topicId="'+topic.topicId+'">'+
										'<button type="button" class="close" data-dismiss="alert" aria-label="Close">'+
										'<span aria-hidden="true">&times;</span>'+
									'</button>'+topic.topicname
								'</div>';
						$('.panel-topic').append(html);
					});
					if (post.detail && post.detail.indexOf('<a ') >= 0) {
						form.find('[name=detail]').attr('readonly', 'readonly');
					}
				}
			});
		};

		var save = function() {
			var data = form.serializeObject();
			data.images = JSON.stringify(postImgs);
			if (video) {
        data.video = JSON.stringify(video);
      }

			var topicIds=[];
			$('.panel-topic .alert').each(function(){
				topicIds.push($(this).attr('topicId'));
			});
			data.topicId = topicIds.join(',');

			var detail = data.detail;
			$('#at-users').find('.item').each(function() {
				var id = $(this).attr('data-user-id');
				var nick = $(this).attr('data-user-nick');
				if (!nick) {
					nick = 'u' + id;
				}
				detail += '<a href="user:'+ id +'">@' + nick + '</a>';
			});
			data.detail = detail;

			App.api('/community/savepost', data, {
				success : function(post) {
					App.router.go('/community/posts', {
						id : post.id
					});
				}
			});
		};

		var atUserHtml = function(data) {
			var img = '';
			if (data.avatar) {
				img = App.previewImgUrl(data.avatar, 40, 40);
			} else {
				img = App.resourcePath('/img/avatar_default.jpg');
			}
			var s = '<img src="'+ img +'"/>';
			s += data.nick + '(ID:' + data.id;
			if (data.locality) {
				s += ',' + data.locality;
			}
			s += ')';
			return s;
		};

		var init = function() {
			initPost().then(function(){
				var suggest = new Suggest(atBox, 'user/findbynick', {
					handleAppend: function(item, data) {
						item.innerHTML = atUserHtml(data);
					},
					handleEnter: appendAtUser,
					handleClick: appendAtUser
				});
				atBox.keyup(function(e){
					suggest.doSuggest(e.keyCode);
				});
				$('#save-btn').click(save);

				$('.btn-add-topic').click(function(){
					$('.select-topic option:selected').each(function(){
						var topicId=$(this).attr('topicId');
						var value=$(this).val();
						var html='<div class="alert alert-warning alert-dismissible" role="alert" style="padding:5px 20px;margin-bottom:5px;" topicId="'+topicId+'">'+
		  							'<button type="button" class="close" data-dismiss="alert" aria-label="Close">'+
										'<span aria-hidden="true">&times;</span>'+
									'</button>'+value
								'</div>';
						var exists=false;
						$('.panel-topic .alert').each(function(){
							if($(this).attr('topicId')==topicId){
								exists=true;
							}
						});
						if(!exists){
							$('.panel-topic').append(html);
						}
					});
				});

				$('.btn-search').click(function(){
					$('.select-topic').html('');
					App.api('community/gettopics',{name:$('.input-topicName').val()}).then(function(result){
						$.each(result,function(i,topic){
							var option=$('<option></option>');
							option.attr('topicId',topic.topicId);
							option.html(topic.topicname);
							$('.select-topic').append(option);
						});
					});
				});

				$('#srcType').change(function () {
				  var srcType = $("#srcType").val();
				  if (srcType == 'video') { //选择上传视频
            postImgs = []; // 清空图片
            $('#imgs-preview').html(''); //清空图片预览
            App.upload.init({ // 初始化七牛上传(获取token)
              namespace:'post',
              type:'video',
              id: 'upload-video-btn',
              fileUploaded: function (info) {
                console.log(info);
                addVideo(info);
              }
            });
				    $("#upload-btn").hide();
            $("#upload-video-btn").show();
					} else {
				    video = null; //清空视频
            App.upload.init({
              namespace:'post',
              type:'image',
              id: 'upload-btn',
              fileUploaded: function (info) {
                console.log(info);
                addImg(info);
              }
            });
				    $("#upload-btn").show();
            $("#upload-video-btn").hide();
					}
        });
				
				App.upload.init({
					namespace:'post',
					type:'image',
					id: 'upload-btn',
					fileUploaded: function (info) {
						console.log(info);
						addImg(info);
					}
				});
			});
		};
		
		init();
	});
</script>

<div class="panel panel-default x-panel">
	<div class="panel-body">
		<form id="save-form" role="form" onsubmit="return false;">
			<input type="hidden" name="id" ng-model="post.id" />
			<div class="form-group">
				<label>内容：</label>
				<div class="controls">
					<textarea name="detail" class="form-control" rows="5" ng-model="post.detail"></textarea>
				</div>
			</div>
			<div class="form-group">
				<label>提醒谁看（输入昵称&nbsp;或&nbsp;#ID#）：</label>
				<div class="controls">
					<input type="text" id="at" class="form-control" autocomplete="off"/>
					<div id="at-users"></div>
					<div class="clear"></div>
				</div>
			</div>
			<div class="form-group">
				<label>长博文link：</label>
				<div class="controls">
					<input name="link" class="form-control" ng-model="post.link" />
				</div>
			</div>
			<div class="form-group">
				<label>长博文摘要：</label>
				<div class="controls">
					<textarea name="summary" class="form-control" rows="2" ng-model="post.summary"></textarea>
				</div>
			</div>
			<div class="form-group">
				<label>选择类型：</label>
        <select class="form-control" id="srcType">
					<option value="img">图片</option>
					<option value="video">视频</option>
				</select>
				<div>
					<a href="javascript:;" id="upload-btn">+选择上传图片</a>
				</div>
				<div>
					<a href="javascript:;" id="upload-video-btn" style="display: none">+选择上传视频(最大100M,不支持多个)</a>
				</div>
				<div class="controls postimgs">
					<input type="hidden" name="imgs" />
					<div id="imgs-preview"></div>
				</div>
			</div>
			<div class="form-group">
				<label>话题：</label>
				<div class="panel panel-default">
					<div class="panel-body">
						<div class="row" style="margin-bottom:10px;">
							<div class="col-md-4">
								<input type="text" class="form-control input-topicName" placeholder="话题关键字">
							</div>
							<div class="col-md-2">
								<button class="btn btn-primary btn-search">搜索</button>
							</div>
						</div>
						<div class="row">
							<div class="col-md-5">
								<label>搜索结果：</label>
								<select multiple class="form-control select-topic" style="height:200px;">
								</select>
							</div>
							<div class="col-md-2" style="text-align:center;">
								<div>
									<button class="btn btn-primary btn-add-topic" style="margin-top:60px;margin-bottom:50px;">添加</button>
								</div>
							</div>
							<div class="col-md-5">
								<label>已添加话题：</label>
								<div class="panel panel-default panel-topic" style="height:200px;overflow-y:scroll;padding:10px 10px;"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		
			<div class="form-group">
				<label></label>
				<div class="controls">
					<a href="javascript:;" id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
				</div>
			</div>
		</form>
	</div>
</div>