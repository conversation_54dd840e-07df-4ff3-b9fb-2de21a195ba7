<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script src="js/region-select.js"></script>
<link rel="stylesheet" type="text/css" href="vendor/datetimepicker/bootstrap-datetimepicker.css"/>
<link rel="stylesheet" type="text/css" href="vendor/select2/select2.css"/>
<script src="vendor/moment.js"></script>
<script src="vendor/datetimepicker/bootstrap-datetimepicker.js"></script>
<script src="vendor/datetimepicker/locales/bootstrap-datetimepicker.zh-CN.js"></script>


<style type="text/css">
    #imgs-preview .item {
        position: relative;
        width: 320px;
        margin: 10px 0 0 0;
    }

    #imgs-preview .item .tclose {
        top: -15px;
        right: -15px;
    }

    #imgs-preview .item img {
        width: 320px;
    }
</style>

<script src="js/localesaver.js"></script>
<script>
    App.ready(function () {
        var localeSaver = new LocaleSaver({'dialogUrl': '/community/copywriting_spec.html'});
        var form = $('#save-form');
        var setImgUrl = function (url) {
            form.find('[name=url]').val(url);
        };
        var addImg = function (url) {
            var preview = $('<img/>');
            preview.attr('src', url);
            var wrap = $('<div class="item"/>');
            var close = $('<div class="tclose"/>');
            close.click(function () {
                $(this).parent().remove();
                setImgUrl('');
            });
            wrap.append(close);
            wrap.append(preview);
            $('#imgs-preview').html('').append(wrap);
            setImgUrl(url);
        };
        var id = App.router.getParameter("id");
        var uniqueId = App.router.getParameter("uniqueId");
        var save = function () {
            var data = form.serializeObject();
            data.title = localeSaver.getDataAsModel().title;
            data.subTitle = localeSaver.getDataAsModel().subTitle;
            data.copyWriting = localeSaver.getDataAsModel().copyWriting;
            data.link = localeSaver.getDataAsModel().link;
            if (!data.url) {
                alert("配图不能为空");
                return;
            }
            if (!data.priority) {
                alert("优先级不能为空");
                return;
            }
            if (!data.pageType) {
                alert("页面类型不能为空");
                return;
            }

            if (!data.title) {
                alert("主标题不能为空");
                return;
            }
            if (id) {
                data["id"] = id
            }

            if (uniqueId) {
                data["uniqueId"] = uniqueId
            }

            return App.api('/community/updateAppCopyWriting',
                data).then(function () {
                App.router.go('/community/copy_writing_list');
            });
        };

        var init = function () {
            App.upload.init({
                namespace: 'post',
                type: 'image',
                id: 'upload-btn',
                fileUploaded: function (info) {
                    addImg(info.url);
                },
                error: function (errTip) {
                    alert('文件上传出错' + errTip);
                }
            });
            if (uniqueId) {
                App.api('/community/findCopyWriting', {
                    'uniqueId': uniqueId
                }, {
                    success: function (copyWriting) {
                        form.renderModel(copyWriting);
                        localeSaver.setDataByModel(copyWriting.localizedProps, ['title','subTitle','link','copyWriting'])
                        addImg(copyWriting.url);
                    }
                });
            }
            $.when(localeSaver.init()).then(function () {
                $('#save-btn').click(save);
            });

        };
        init();
    })
    ;
</script>
<style>
    .title b {
        color: red;
        padding-right: 5px;
    }

    #the-table img {
        width: 100px;
        height: auto;
    }
</style>
<form id="save-form" role="form" onsubmit="return false;">

    <!--<div class="form-group">
        <label class="title">主标题：</label>
        <input type="text" name="title" id="title" class="form-control" ng-model="title" required/>
    </div>

    <div class="form-group">
        <label class="title">副标题：</label>
        <input type="text" name="subTitle" id="subTitle" class="form-control" ng-model="subTitle"/>

    </div>-->

    <div class="form-group">
        <label >配图：</label>
        <div class="controls">
            <input type="hidden" name="url" ng-model="url"/>
            <div>
                <a href="javascript:;" id="upload-btn">+选择上传文件</a>
            </div>
            <div id="imgs-preview"></div>
        </div>
    </div>


   <!-- <div class="form-group">
        <label>链接：</label>
        <div class="controls">
            <input type="text" name="link.url" class="form-control" ng-model="link.url"/>
        </div>
    </div>
    <div class="form-group" hidden>
        <label>链接是否需要登录：</label>
        <div class="controls">
            <select name="link.needLogin" class="form-control" ng-model="link.needLogin">
                <option value="0" selected>否 </option>
                <option value="1">是</option>
            </select>
        </div>
    </div>-->

    <!--<div class="form-group">
        <label>文案内容：</label>
        <div class="controls">
           <textarea rows="10" cols="200" name="copyWriting" class="form-control" ng-model="copyWriting" required>
           </textarea>
        </div>
    </div>-->


    <div class="form-group">
        <label>页面类型：</label>
        <select name="pageType" class="form-control" ng-model="pageType" required>
            <option value="feed">出粮</option>
            <option value="feedPart">出粮(份数)</option>
            <option value="eat">喂食</option>
            <option value="toilet">如厕</option>
            <option value="activity">运动</option>
            <option value="sleep">睡眠</option>
            <option value="petLog">宠物日志</option>
            <option value="weight">宠物体重</option>
            <option value="walkpet">遛宠</option>
        </select>
    </div>
    <div class="form-group">
        <label class="title">优先级：<b>*</b></label>
        <input type="text" name="priority" id="priority" class="form-control" ng-model="priority"  required/>
    </div>

    <div class="form-group">
        <label>是否启用：</label>
        <select name="enable" class="form-control" ng-model="enable">
            <option value="0">否</option>
            <option value="1">是</option>
        </select>
    </div>


    <div class="form-group">
        <div class="controls">

            <a href="javascript:;" id="local-add-btn" >
                <span>+</span>
                <span data-l10n-id="localize"></span>
            </a>

            <div id="locale-spec-template">
                <div class="locale-spec">
                    <div class="locale-spec-item">
                        <label data-l10n-id="locale"></label>:
                        <span class="locale">{{locale.name}}</span>
                        <a href="javascript:;" class="edit" data-l10n-id="edit" data-op-type="update" data-op-key="{{locale.key}}"></a>
                        <a href="javascript:;" class="edit" data-l10n-id="delete" data-op-type="remove" data-op-key="{{locale.key}}"></a>
                    </div>
                    <div class="locale-spec-item">
                        <label>主标题</label>:<span>{{spec.title}}</span><br/>
                        <label>副标题</label>:<span>{{spec.subTitle}}</span><br/>
                        <label>链接</label>:<span>{{spec.link}}</span><br/>
                        <label>文案内容</label>:<span>{{spec.copyWriting}}</span><br/>
                    </div>
                </div>
            </div>
            <div id="locale-specs"></div>
        </div>
    </div>

    <div class="form-group">
        <label></label>
        <div class="controls">
            <a href="javascript:;" id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
            <label> </label>
            <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
        </div>
    </div>
</form>