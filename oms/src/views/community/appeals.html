<script src="js/community/post.js"></script>
<script>
    App.ready(function () {
        var request = function (offset) {
            App.scrollToTop();
            var params = $.extend({'limit': 10}, App.router.getParameters(), {'offset': offset});
            App.api('/community/appeal_list', params).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
                    context.post._detail = postAsText(context.post); 
                    var row = U.template(template, context);
                    table.append(row);
                }
                initOperationButtons();
                App.pagination(ps, request);
            });
        };
        var initOperationButtons = function() {
        	$('[data-op-type=remove]').click(function(){
        		remove($(this).attr('data-op-key'));
        	});
        	$('[data-op-type=approve]').click(function(){
        		approve($(this).attr('data-op-key'), 1, null);
        	});
        	$('[data-op-type=decline]').click(function(){
        		var feedback = prompt('请输入拒绝理由', '');
        		if (!feedback) {
        			alert('请输入拒绝理由');
        			return;
        		}
        		approve($(this).attr('data-op-key'), 0, feedback);
        	});
        };
        var approve = function(id, state, feedback) {
        	  if (!confirm(App.text('confirm.operation'))) {
        		  return;
        	  }
	       	  return App.api('/community/appeal_deal', {'postId':id, 'approve':state, 'feedback': feedback}).then(function(){
	       		  request(0);
	       	  });
        };
        var remove = function(id) {
	      	  if (!confirm(App.text('confirm.delete'))) {
	      		  return;
	      	  }
	       	  return App.api('/community/appeal_remove', {'postId': id}).then(function(){
	       		  request(0);
	       	  });
        };
        request();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
         贴子屏蔽申诉
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 20%" data-l10n-id="p.user.nick"></th>
                <th style="width: 30%">贴子</th>
                <th style="width: 25%">理由</th>
                <th style="width: 15%">时间</th>
                <th style="width: 10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td><a href="#/community/post?id={{post.id}}" target="_blank">{{post.author.nick}}</a></td>
                <td>{{post._detail}}</td>
                <td>{{detail}}</td>
                <td>{{_createdAt}}</td>
                <td class="ops">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:;" data-op-type="approve" data-op-key="{{post.id}}">通过</a>
                                <a href="javascript:;" data-op-type="decline" data-op-key="{{post.id}}">拒绝</a>
                                <a href="javascript:;" data-op-type="remove" data-op-key="{{post.id}}" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>