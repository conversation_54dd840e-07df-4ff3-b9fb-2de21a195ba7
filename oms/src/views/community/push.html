<link rel="stylesheet" type="text/css" href="vendor/datetimepicker/bootstrap-datetimepicker.css"/>
<link rel="stylesheet" type="text/css" href="vendor/select2/select2.css"/>
<script src="vendor/select2/select2.min.js"></script>
<script src="js/region-select.js"></script>
<script src="js/device-select.js"></script>
<script src="vendor/moment.js"></script>
<script src="vendor/datetimepicker/bootstrap-datetimepicker.js"></script>
<script src="vendor/datetimepicker/locales/bootstrap-datetimepicker.zh-CN.js"></script>
<script>
    App.ready(function () {
        App.menu.select('/community/pushes');
        var form = $('#save-form');
        var dateKeys = ['sendAt'];
        var contentProvider = {};
        Date.prototype.Format = function (fmt) { //author: meizz
            var o = {
                "M+": this.getMonth() + 1, //月份
                "d+": this.getDate(), //日
                "H+": this.getHours(), //小时
                "m+": this.getMinutes(), //分
                "s+": this.getSeconds(), //秒
                "q+": Math.floor((this.getMonth() + 3) / 3), //季度
                "S": this.getMilliseconds() //毫秒
            };
            if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
                if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        };

        var save = function () {
            var date= new Date($("#sendAt").data("DateTimePicker").getDate());

            if (date.getHours()<7||date.getHours()>=21){
                alert(App.text('p.community.push.please.set.time'));
                return;
            }
            var data = form.serializeObject();
            $.each(dateKeys, function (index, key) {
                var raw = form.find('[name="' + key + '"]').val();
                data[key] = U.date.formatISO8601(U.date.parse(raw));
            });
            var recipient = {};
            var regions = getRegions();
            if (regions === false) {
                return;
            }
            var devices = getDevices();
            if (devices === false) {
                return;
            }
            var mobiles = getMobiles();
            if (mobiles === false) {
                return;
            }
            if (regions.length > 0) {
                recipient['regions'] = regions;
            }
            if (devices.length > 0) {
                recipient['devices'] = devices;
            }
            if (mobiles.length > 0) {
                recipient['mobiles'] = mobiles;
            }
            var defaultRecipient = contentProvider['recipient'];
            if (defaultRecipient) {
                for (var key in defaultRecipient) {
                    recipient[key] = defaultRecipient[key];
                }
            }
            for (var key in contentProvider) {
                data[key] = contentProvider[key];
            }
            data['recipient'] = recipient;

            var promptText = App.text('prompt.input', ['push']);
            if (prompt(promptText) != 'push') {
                return;
            }
            return App.api('/community/push', {
                'model': JSON.stringify(data)
            }).then(function () {
                App.router.go('/community/pushes');
            });
        };

        var limitRegion = null;
        var limitDevice = null;
        var limitMobile = null;
        var initContent = function () {
            var type = App.router.getParameter('type');
            var resourceId = App.router.getParameter('resourceId');
            form.find('[name="type"]').val(type);
            form.find('[name="typeName"]').val(App.text('p.community.push.' + type));
            return App.api('/community/pushcontent', {
                'type': type,
                'resourceId': resourceId
            }).then(function (result) {
                contentProvider = result.content;
                limitRegion = result.region;
                limitDevice = result.device;
                limitMobile = result.mobile;
                showSnapshot(contentProvider);
            });
        };

        var showSnapshot = function (result) {
            var snapshots = result.snapshot;
            if (!snapshots) {
                return;
            }
            var s = '';
            for (var key in snapshots) {
                s += snapshots[key];
                s += '\n';
            }
            $('#snapshot').val(s);
        };

        var offset = 0;
        var regions = {};
        var addRegion = function () {
            offset++;
            var key = '' + offset;
            var template = $('#recipient-region-template').html();
            var prefix = 'regionkey-' + offset;
            var html = U.string
                .replaceAll(template, 'regionkey-', prefix + '-');
            var dom = $(html.trim());
            $('#recipient-region-container').append(dom);
            dom.on('click', '.remove-btn', key, function (e) {
                var key = e.data;
                delete regions[key];
                $('#regionkey-' + e.data + '-row').remove();
            });
            var select = new RegionSelect(prefix);
            select.init().done(function () {
                if (limitRegion) {
                    select.setValue(limitRegion);
                    $("[data-region-1]").prop("disabled", true);
                }
            });
            regions[key] = select;
        };
        var deviceOffset = 0;
        var devices = {};
        var addDevice = function () {
            deviceOffset++;
            var key = '' + deviceOffset;
            var template = $('#recipient-device-template').html();
            var prefix = 'devicekey-' + deviceOffset;
            var html = U.string
                .replaceAll(template, 'devicekey-', prefix + '-');
            var dom = $(html.trim());
            $('#recipient-device-container').append(dom);
            dom.on('click', '.remove-btn', key, function (e) {
                var key = e.data;
                delete devices[key];
                $('#devicekey-' + e.data + '-row').remove();
            });
            var select = new DeviceSelect(prefix);
            select.init().done(function () {
                if (limitDevice) {
                    select.setValue(limitDevice);
                    $("[data-device-1]").prop("disabled", true);
                }
            });
            devices[key] = select;
        };
        var mobileOffset = 0;
        var mobiles = {};
        var addMobile = function () {
            mobileOffset++;
            var key = '' + mobileOffset;
            var template = $('#recipient-mobile-template').html();
            var prefix = 'mobilekey-' + mobileOffset;
            var html = U.string
                .replaceAll(template, 'mobilekey-', prefix + '-');
            var dom = $(html.trim());
            $('#recipient-mobile-container').append(dom);
            dom.on('click', '.remove-btn', key, function (e) {
                var key = e.data;
                delete mobiles[key];
                $('#mobilekey-' + e.data + '-row').remove();
            });
            var select = new MobilesSelect(prefix);
            select.init().done(function () {
                if (limitMobile) {
                    select.setValue(limitMobile);
                    $("[data-mobile-1]").prop("disabled", true);
                }
            });
            mobiles[key] = select;
        };


        var getDevices = function () {
            var filterDevices = [];
            for (var key in devices) {
                var device = devices[key];
                var v = device.getValue();
                if (!v) {
                    alert(App.text('p.community.push.please.select.device'));
                    return false;
                }
                filterDevices.push(v);
            }
            return filterDevices;
        };

        var getMobiles = function () {
            var filterMobiles = [];
            for (var key in mobiles) {
                var mobile = mobiles[key];
                var v = mobile.getValue();
                if (!v) {
                    alert(App.text('p.community.push.please.select.mobile'));
                    return false;
                }
                filterMobiles.push(v);
            }
            return filterMobiles;
        };


        var getRegions = function () {
            var filterRegions = [];
            for (var key in regions) {
                var region = regions[key];
                var v = region.getValue();
                if (!v) {
                    alert(App.text('p.community.push.please.select.region'));
                    return false;
                }
                filterRegions.push(v);
            }
            return filterRegions;
        };
        var date = new Date();
        var startDate = new Date();
        if (date.getHours() >= 21) {
            startDate.setMinutes(0);
            startDate.setDate(date.getDate() + 1);
            startDate.setHours(7);
            $('#sendAt').val(startDate.Format('yyyy-MM-dd HH:mm'));
        } else if (date.getHours() < 7) {
            startDate.setMinutes(0);
            startDate.setHours(7);
            $('#sendAt').val(startDate.Format('yyyy-MM-dd HH:mm'));
        }

        //init
        initContent().then(function () {
            // init buttons
            $.each(dateKeys, function (index, key) {
                form.find('[name="' + key + '"]').datetimepicker({
                    useSeconds: false,
                    language: 'zh-CN',
                    minDate:startDate
                });
            });

            $('#save-btn').click(save);
            $('#recipient-region-add-btn').click(addRegion);
            $('#recipient-device-add-btn').click(addDevice);
            $('#recipient-mobile-add-btn').click(addMobile);
        });
    });
</script>

<form id="save-form" role="form" onsubmit="return false;">
    <input type="hidden" name="type" ng-model="REQUEST.type"/>
    <div id="recipient-region-template" class="none">
        <div class="region-select-container" id="regionkey-row">
            <select class="form-control" id="regionkey-s1" data-region-1="1"></select>
            <select class="form-control" id="regionkey-s2"></select>
            <select class="form-control" id="regionkey-s3"></select>
            <select class="form-control" id="regionkey-s4"></select>
            <a href="javascript:;" class="remove-btn" data-l10n-id="delete"></a>
        </div>
    </div>
    <div id="recipient-device-template" class="none">
        <div class="region-select-container" id="devicekey-row">
            <select class="form-control" id="devicekey-s1" data-device-1="1"></select>
            <a href="javascript:;" class="remove-btn" data-l10n-id="delete"></a>
        </div>
    </div>
    <div id="recipient-mobile-template" class="none">
        <div class="region-select-container" id="mobilekey-row">
            <select class="form-control" id="mobilekey-s1" data-device-1="1"></select>
            <a href="javascript:;" class="remove-btn" data-l10n-id="delete"></a>
        </div>
    </div>
    <div class="form-group">
        <label data-l10n-id="type"></label>
        <input type="text" name="typeName" disabled="disabled" class="form-control"/>
    </div>
    <div class="form-group">
        <label data-l10n-id="detail"></label>
        <textarea id="snapshot" class="form-control" rows="5" readonly="readonly"></textarea>
    </div>
    <div class="form-group">
        <a href="javascript:;" id="recipient-region-add-btn" class="btn btn-success"
           data-l10n-id="p.community.add.region"></a>
        <div class="controls" id="recipient-region-container"></div>
    </div>
    <div class="form-group">
        <a href="javascript:;" id="recipient-device-add-btn" class="btn btn-success"
           data-l10n-id="p.community.add.device"></a>
        <div class="controls" id="recipient-device-container"></div>
    </div>
    <div class="form-group">
        <a href="javascript:;" id="recipient-mobile-add-btn" class="btn btn-success"
           data-l10n-id="p.community.add.mobile"></a>
        <div class="controls" id="recipient-mobile-container"></div>
    </div>
    <div class="form-group">
        <label data-l10n-id="p.community.schedule.push.at"></label>
        <input type="text" name="sendAt" id="sendAt" class="form-control" data-date-format="YYYY-MM-DD HH:mm" readonly="readonly"/>
    </div>
    <a id="save-btn" class="btn btn-warning" data-l10n-id="p.community.push"></a>
    <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
</form>