<script>
    App.ready(function () {
        App.menu.select('/community/copy_writing_list');

        window.edit = function (uniqueId) {

            var params = App.router.getParameters();
            params['petType'] = 1;

            //App.router.go('/community/ll', params);

            App.router.go('/community/copy_writing?uniqueId=' + uniqueId);
        };


        var cpType;
        var showLocalizedString = function (map) {
            var s = '';
            if (!map) {
                return s;
            }
            var i = 0;
            for (var locale in map) {
                if (i > 0) {
                    s += '<br/><br/>';
                }
                s += locale + ":";
                s += '<br/>';
                s += map[locale];
                i++;
            }
            return s;
        };

        window.disable = function (uniqueId) {
            var data = {};
            data['enable'] = 0;
            App.api('/community/enableCopyWriting', {
                'uniqueId': uniqueId,
                'doc': JSON.stringify(data)
            }).then(function () {
                request();
            });
        };
        window.remove = function (uniqueId) {
            if (!confirm(App.text('confirm.delete'))) {
                return;
            }
            App.api('/community/deleteAppCopyWriting', {
                'uniqueId': uniqueId
            }).then(function () {
                window.location.reload();
            });
        };
        window.enable = function (uniqueId) {
            var data = {};
            data['enable'] = 1;
            App.api('/community/enableCopyWriting', {
                'uniqueId': uniqueId,
                'doc': JSON.stringify(data)
            }).then(function () {
                request();
            });
        };


        var request = function (offset, type) {
            if (!U.isNull(type)) {
                cpType = type;
            }
            App.scrollToTop();
            var params = $.extend({
                'limit': 10
            }, App.router.getParameters());
            params.offset = (!U.isNull(offset)) ? offset : params.offset;
            params.type =cpType;
            App.api('/community/appCopyWritings', params).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>' + table.find('tr.template').html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._body = showLocalizedString(context.body);
                    context._updateAt = new Date(context.updateAt + 8 * 3600 * 1000).toJSON().substr(0, 16).replace('T', ' ').replace(/-/g, '.');
                    context._title = context.title;
                    context._subTitle = context.subTitle;
                    /*if (context.link) {
                        context._url     = '<a href="' + context.link.url + '" target="_blank">' + context.link.url + '</a>';
                    }*/
                    context._link = context.link;
                    context._copyWriting = context.copyWriting;

                    context._pageType = context.pageType;
                    context._priority = context.priority;

                    context._enable = context.enable === 1 ? "已启用" : "未启用";

                    var icon = App.previewImgUrl(context.url, 120, 120);
                    if (!icon) {
                        context._img = null;
                    } else {
                        context._img = '<img class="plink-img" src="' + icon + '">';
                    }
                    var row = U.template(template, context);
                    table.append(row);
                    var hideKeys = ['enable1', 'disable0'];
                    $.each(hideKeys, function (index, item) {
                        $('[data-op-display=' + item + ']').addClass('none');
                    });
                }

                App.pagination(ps, request);
                $('.btn-primary').removeClass("active");

            });
        };
        window.request = request;
        var init = function () {
            cpType = App.router.getParameter('cpType');
            switch (cpType) {
                case '2':
                    $('#wait')[0].click();
                    break;
                default:
                    $('#all')[0].click();
            }
            window.remove = remove;
        };

        init();
    });
</script>
<style>
    .region b {
        color: red;
        padding-right: 5px;
    }

    #the-table img {
        width: 100px;
        height: auto;
    }
</style>

<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form class="form-inline" action="#" onsubmit="return false;">
            <a href="javascript:request(null,0);" id="all" class="btn btn-primary">全部内容</a>
            <a href="javascript:request(null,1);" id="wait" class="btn btn-primary">未启用</a>
            <a href="#/community/copy_writing" class="btn btn-success">创建</a>

        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 8%">更新时间</th>
                <th style="width: 10%">主标题</th>
                <th style="width: 12%">副标题</th>
                <th style="width: 12%">配图</th>
                <th style="width: 10%">链接</th>
                <th style="width: 16%">文案内容</th>
                <th style="width: 8%">页面类型</th>
                <th style="width: 8%">优先级</th>
                <th style="width: 8%">是否启用</th>
                <th style="width: 8%">操作</th>
            </tr>
            <tr class="template">
                <td>{{_updateAt}}</td>
                <td>{{_title}}</td>
                <td>{{_subTitle}}</td>
                <td>{{_img}}</td>
                <td>{{_link}}</td>
                <td>{{_copyWriting}}</td>
                <td>{{_pageType}}</td>
                <td>{{_priority}}</td>
                <td>{{_enable}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:edit('{{uniqueId}}');">编辑</a>
                            </li>
                            <li data-op-display="enable{{enable}}">
                                <a href="javascript:enable('{{uniqueId}}');">启用</a>
                            </li>
                            <li data-op-display="disable{{enable}}">
                                <a href="javascript:disable('{{uniqueId}}');">禁用</a>
                            </li>
                            <li>
                                <a href="javascript:remove('{{uniqueId}}');" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>