<script>
    App.ready(function () {
        App.menu.select('/community/old_appbanner_list');

        window.edit = function (id) {
            App.router.go('/community/appbanner?id=' + id);
        };


        var splashType;
        var showLocalizedString = function (map) {
            var s = '';
            if (!map) {
                return s;
            }
            var i = 0;
            for (var locale in map) {
                if (i > 0) {
                    s += '<br/><br/>';
                }
                s += locale + ":";
                s += '<br/>';
                s += map[locale];
                i++;
            }
            return s;
        };
        window.disable = function (id) {
            var data = {};
            data['enable'] = 0;
            App.api('/community/enableAppBanner', {
                'id': id,
                'doc': JSON.stringify(data)
            }).then(function () {
                request();
            });
        };
        window.remove = function (id) {
            if (!confirm(App.text('confirm.delete'))) {
                return;
            }
            App.api('/community/deleteAppBanner', {
                'id': id
            }).then(function () {
                window.location.reload();
            });
        };
        window.enable = function (id) {
            var data = {};
            data['enable'] = 1;
            App.api('/community/enableAppBanner', {
                'id': id,
                'doc': JSON.stringify(data)
            }).then(function () {
                request();
            });
        };
        var showRegions = function (list) {
            var s = '';
            if (!list) {
                return s;
            }
            for (var i = 0; i < list.length; i++) {
                if (!window.__CONFIG__.oversea) {
                    s += list[i].replace("中国-", "");
                } else {
                    s += list[i];
                }

                s += '</br>';
            }
            return s;
        };

        var request = function (offset, type) {
            if (!U.isNull(type)) {
                splashType = type;
            }
            App.scrollToTop();
            var params = $.extend({
                'limit': 10
            }, App.router.getParameters());
            params.offset = (!U.isNull(offset)) ? offset : params.offset;
            params.type = splashType;
            App.api('/community/appbanners', params).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>' + table.find('tr.template').html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._body = showLocalizedString(context.body);
                    context._title = context.title;
                    const start = new Date(context.startTime + 8 * 3600 * 1000).toJSON().substr(0, 16).replace('T', ' ').replace(/-/g, '.');
                    const end = new Date(context.endTime + 8 * 3600 * 1000).toJSON().substr(0, 16).replace('T', ' ').replace(/-/g, '.');
                    context._updateAt = new Date(context.updateAt + 8 * 3600 * 1000).toJSON().substr(0, 16).replace('T', ' ').replace(/-/g, '.');
                    context._validTime = start + "-" + end;
                    if (context.link) {
                        context._url = '<a href="' + context.link.url + '" target="_blank">' + context.link.url + '</a>';
                        context._needLogin = context.link.needLogin === 1 ? "是" : "否";
                    }
                    context._enable = context.enable === 1 ? "已启用" : "未启用";
                    if (new Date().getTime() > context.endTime) {
                        context._enable = '已失效';
                    }
                    context._region = '<div class="region">' + showRegions(context.regions) + "</div>";

                    var icon = App.previewImgUrl(context.url, 120, 120);
                    if (!icon) {
                        context._img = null;
                    } else {
                        context._img = '<img class="plink-img" src="' + icon + '">';
                    }
                    context.tagName = context.tagName || '全部用户';
                    var row = U.template(template, context);
                    table.append(row);
                    var hideKeys = ['enable1', 'disable0'];
                    $.each(hideKeys, function (index, item) {
                        $('[data-op-display=' + item + ']').addClass('none');
                    });
                }

                App.pagination(ps, request);
                $('.btn-primary').removeClass("active");

            });
        };
        window.request = request;
        var init = function () {
            splashType = App.router.getParameter('splashType');
            switch (splashType) {
                case '1':
                    $('#using')[0].click();
                    break;
                case '2':
                    $('#wait')[0].click();
                    break;
                case '3':
                    $('#invalid')[0].click();
                    break;
                default:
                    $('#all')[0].click();
            }
            window.remove = remove;
        };

        init();
    });
</script>
<style>
    .region b {
        color: #ff0000;
        padding-right: 5px;
    }

    #the-table img {
        width: 100px;
        height: auto;
    }
</style>

<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form class="form-inline" action="#" onsubmit="return false;">
            <a href="javascript:request(null,0);" id="all" class="btn btn-primary">全部内容</a>
            <a href="javascript:request(null,1);" id="using" class="btn btn-primary">正在使用</a>
            <a href="javascript:request(null,2);" id="wait" class="btn btn-primary">未启用</a>
            <a href="javascript:request(null,3);" id="invalid" class="btn btn-primary">已失效</a>
            <a href="#/community/appbanner" class="btn btn-success">创建</a>

        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 10%">更新时间</th>
                <th style="width: 8%">标题</th>
                <th style="width: 12%">配图</th>
                <th style="width: 12%">链接</th>
                <!--<th style="width: 8%">需要登录</th>-->
                <th style="width: 14%">指定地区</th>
                <th style="width: 14%" data-l10n-id="userGroup"></th>
                <th style="width: 14%">有效期</th>
                <th style="width: 8%">当前状态</th>
                <th style="width: 8%">操作</th>
            </tr>
            <tr class="template">
                <td>{{_updateAt}}</td>
                <td>{{_title}}</td>
                <td>{{_img}}</td>
                <td>{{_url}}</td>
               <!-- <td>{{_needLogin}}</td>-->
                <td>{{_region}}</td>
                <td>{{tagName}}</td>
                <td>{{_validTime}}</td>
                <td>{{_enable}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:edit('{{id}}');">编辑</a>
                            </li>
                            <li data-op-display="enable{{enable}}">
                                <a href="javascript:enable('{{id}}');">启用</a>
                            </li>
                            <li data-op-display="disable{{enable}}">
                                <a href="javascript:disable('{{id}}');">禁用</a>
                            </li>
                            <li>
                                <a href="javascript:remove('{{id}}');" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>