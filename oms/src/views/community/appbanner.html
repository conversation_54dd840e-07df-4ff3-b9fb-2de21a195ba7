<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script src="js/region-select.js"></script>
<link
  rel="stylesheet"
  type="text/css"
  href="vendor/datetimepicker/bootstrap-datetimepicker.css"
/>
<link rel="stylesheet" type="text/css" href="vendor/select2/select2.min.css" />
<script src="vendor/select2/select2.min.js"></script>
<script src="vendor/moment.js"></script>
<script src="vendor/datetimepicker/bootstrap-datetimepicker.js"></script>
<script src="vendor/datetimepicker/locales/bootstrap-datetimepicker.zh-CN.js"></script>

<style type="text/css">
  #imgs-preview .item {
    position: relative;
    width: 320px;
    margin: 10px 0 0 0;
  }

  #imgs-preview .item .tclose {
    top: -15px;
    right: -15px;
  }

  #imgs-preview .item img {
    width: 320px;
  }
</style>
<script>
  App.ready(function () {
    var splashResult = null;
    var form = $('#save-form');
    var userGroupList = [];

    var setImgUrl = function (url) {
      form.find('[name=url]').val(url);
    };

    var setUserGroupEstimateNumber = function (ids) {
      const userGroups = userGroupList.filter(
        (item) => (ids || []).indexOf(item.id) > -1
      );
      $('#estimate-number').text(
        userGroups.reduce((prev, curr) => prev + curr.estimate, 0)
      );
    };

    var addImg = function (url) {
      var preview = $('<img/>');
      preview.attr('src', url);
      var wrap = $('<div class="item"/>');
      var close = $('<div class="tclose"/>');
      close.click(function () {
        $(this).parent().remove();
        setImgUrl('');
      });
      wrap.append(close);
      wrap.append(preview);
      $('#imgs-preview').html('').append(wrap);
      setImgUrl(url);
    };

    var loadUserGroups = function () {
      return App.api(
        'tag/user/list',
        { offset: 0, limit: 100000 },
        { server: 'new-api' }
      ).then(function (result) {
        var select = form.find('[name="tagId"]');
        select.append(
          '<option style="display:none;" value="">请选择用户分群</option>'
        );
        userGroupList = result.items;
        var tagIds =
          splashResult && splashResult.tagId
            ? splashResult.tagId.split(',').map((item) => +item)
            : [];
        setUserGroupEstimateNumber(tagIds);
        userGroupList
          .filter((item) => item.estimate >= 0 && item.enable === 1)
          .map((item) => {
            if (splashResult && tagIds.indexOf(item.id) > -1) {
              select.append(
                '<option selected value="' +
                  item.id +
                  '">' +
                  item.name +
                  '</option>'
              );
            } else {
              select.append(
                '<option value="' + item.id + '">' + item.name + '</option>'
              );
            }
          });
        select.select2({ width: '500px' });
      });
    };

    var id = App.router.getParameter('id');
    var save = function () {
      var data = form.serializeObject();
      if (!data.title) {
        alert('标题不能为空');
        return;
      }

      if (!data.url) {
        alert('配图不能为空');
        return;
      }
      const startTime = new Date(
        $('#startTime').data('DateTimePicker').getDate()
      );
      const endTime = new Date($('#endTime').data('DateTimePicker').getDate());
      if (!startTime || !endTime) {
        alert('请设定有效期');
        return;
      }

      if (endTime <= startTime) {
        alert('请设定合适的有效期');
        return;
      }

      if (data.countdown > 5) {
        alert('倒计时应小于5秒');
        return;
      }

      // if (endTime.getTime() - startTime.getTime() > 7 * 24 * 3600 * 1000) {
      //     alert(App.text('p.community.splash.please.set.time'));
      //     return;
      // }
      $.each(dateKeys, function (index, key) {
        var raw = form.find('[name="' + key + '"]').val();
        data[key] = U.date.parse(raw).getTime();
      });

      if (id) {
        data['id'] = id;
      }
      var regions = getRegions();
      if (regions === false) {
        return;
      }
      if (regions.length > 0) {
        data['regions'] = regions;
      } else {
        data['regions'] = ['ALL'];
      }

      // 选择用户分群的时候，需要存在tagId，否则提示
      if (data.userGroup && !data.tagId) {
        alert('请选择用户分群');
        return;
      }

      // 用户分群如果选择了全部，则需要手动删除一下tagId
      if (data.all) {
        delete data['tagId'];
      } else if (typeof data.tagId !== 'string') {
        data.tagId = data.tagId.join();
      }
      delete data['all'];
      delete data['userGroup'];

      return App.api('/community/updateAppBanner', data).then(function () {
        App.router.go('/community/old_appbanner_list');
      });
    };

    var dateKeys = ['startTime', 'endTime'];
    Date.prototype.Format = function (fmt) {
      //author: meizz
      var o = {
        'M+': this.getMonth() + 1, //月份
        'd+': this.getDate(), //日
        'H+': this.getHours(), //小时
        'm+': this.getMinutes(), //分
        's+': this.getSeconds(), //秒
        'q+': Math.floor((this.getMonth() + 3) / 3), //季度
        S: this.getMilliseconds(), //毫秒
      };
      if (/(y+)/.test(fmt))
        fmt = fmt.replace(
          RegExp.$1,
          (this.getFullYear() + '').substr(4 - RegExp.$1.length)
        );
      for (var k in o)
        if (new RegExp('(' + k + ')').test(fmt))
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length == 1
              ? o[k]
              : ('00' + o[k]).substr(('' + o[k]).length)
          );
      return fmt;
    };

    App.menu.select('/community/old_appbanner_list');
    var offset = 0;
    var regions = {};
    var addRegion = function (limitRegion) {
      offset++;
      var key = '' + offset;
      var template = $('#recipient-region-template').html();
      var prefix = 'regionkey-' + offset;
      var html = U.string.replaceAll(template, 'regionkey-', prefix + '-');
      var dom = $(html.trim());
      $('#recipient-region-container').append(dom);
      dom.on('click', '.remove-btn', key, function (e) {
        var key = e.data;
        delete regions[key];
        $('#regionkey-' + e.data + '-row').remove();
      });
      var select = new RegionSelect(prefix);
      select.init().done(function () {
        if (limitRegion.length > 0) {
          select.setChinaAllValue(limitRegion);
        } else {
          select.setValue('CN');
        }
        $('[data-region-1]').prop('disabled', true);
      });
      regions[key] = select;
    };

    var getRegions = function () {
      var filterRegions = [];
      for (var key in regions) {
        var region = regions[key];
        var v = region.getValue();
        if (!v) {
          alert(App.text('p.community.push.please.select.region'));
          return false;
        }
        filterRegions.push(v);
      }
      return filterRegions;
    };

    var initRadioButtons = () => {
      $('input[type="radio"]').on('click', function (ev) {
        $(ev.target).siblings('input').removeProp('checked');
        const value = $(ev.target).val();
        // 只有两个值：all，userGroup
        if (value === 'userGroup') {
          $('#user-group-selector').css('display', 'block');
          $('#user-group-estimate-number').css('display', 'block');
        } else {
          $('#user-group-selector').css('display', 'none');
          $('#user-group-estimate-number').css('display', 'none');
        }
      });
    };

    var initUserGroupSelector = () => {
      $('[name="tagId"]').change(function (ev) {
        const tagIds = $('[name="tagId"]')
          .val()
          .map((item) => +item);
        setUserGroupEstimateNumber(tagIds);
      });
    };

    var init = function () {
      App.upload.init({
        namespace: 'post',
        type: 'image',
        id: 'upload-btn',
        fileUploaded: function (info) {
          addImg(info.url);
        },
        error: function (errTip) {
          alert('文件上传出错' + errTip);
        },
      });
      var oneDayTimes = 24 * 3600 * 1000;
      var startDate = new Date(new Date().getTime() - oneDayTimes);

      if (id) {
        App.api(
          '/community/findAppBanner',
          {
            id: id,
          },
          {
            success: function (splash) {
              splashResult = splash;
              form.renderModel(splash);
              if (splash.regions) {
                for (let i = 0; i < splash.regions.length; i++) {
                  if (splash.regions[i] !== 'ALL') {
                    addRegion(splash.regions[i]);
                  }
                }
              }
              addImg(splash.url);
              form.find('[name= startTime]').datetimepicker({
                useSeconds: false,
                language: 'zh-CN',
                defaultDate: `${new Date(
                  splash.startTime
                ).toLocaleDateString()} ${new Date(
                  splash.startTime
                ).toLocaleTimeString()}`,
                minDate: startDate,
              });
              form.find('[name= endTime]').datetimepicker({
                useSeconds: false,
                language: 'zh-CN',
                defaultDate: `${new Date(
                  splash.endTime
                ).toLocaleDateString()} ${new Date(
                  splash.endTime
                ).toLocaleTimeString()}`,
                minDate: startDate,
              });

              initRadioButtons();
              loadUserGroups();
              initUserGroupSelector();

              // 需要默认选中用户分群相关radio
              if (splashResult && splashResult.tagId) {
                $('#userGroup').prop('checked', true);
                $('#user-group-selector').css('display', 'block');
                $('#user-group-estimate-number').css('display', 'block');
              } else {
                $('#all').prop('checked', true);
                $('#user-group-estimate-number').css('display', 'none');
              }
            },
          }
        );
      } else {
        initRadioButtons();
        loadUserGroups();
        initUserGroupSelector();
        $('#all').prop('checked', true);
        $.each(dateKeys, function (index, key) {
          form.find('[name="' + key + '"]').datetimepicker({
            useSeconds: false,
            language: 'zh-CN',
            minDate: startDate,
          });
        });
      }

      $('#save-btn').click(save);
      $('#recipient-region-add-btn').click(addRegion);
    };
    init();
  });
</script>
<style>
  .title b {
    color: #ff0000;
    padding-right: 5px;
  }

  #the-table img {
    width: 100px;
    height: auto;
  }
</style>
<form id="save-form" role="form" onsubmit="return false;">
  <div id="recipient-region-template" class="none">
    <div class="region-select-container" id="regionkey-row">
      <select
        class="form-control hidden"
        id="regionkey-s1"
        data-region-1="1"
      ></select>
      <select class="form-control" id="regionkey-s2"></select>
      <select class="form-control" id="regionkey-s3"></select>
      <select class="form-control" id="regionkey-s4"></select>
      <a href="javascript:;" class="remove-btn" data-l10n-id="delete"></a>
    </div>
  </div>
  <div class="form-group">
    <label class="title">标题（用户不可见）<b>*</b></label>
    <input
      type="text"
      name="title"
      id="title"
      class="form-control"
      ng-model="title"
    />
  </div>

  <div class="form-group">
    <label>是否启用：</label>
    <select name="enable" class="form-control" ng-model="enable">
      <option value="0">否</option>
      <option value="1">是</option>
    </select>
  </div>

  <label class="title">有效期 <b>*</b></label>
  <div class="form-group">
    <input
      style="width: 200pt; display: inline"
      type="text"
      name="startTime"
      id="startTime"
      class="form-control"
      data-date-format="YYYY-MM-DD HH:mm"
      readonly="readonly"
    />
    <label>-</label>
    <input
      style="width: 200pt; display: inline"
      type="text"
      name="endTime"
      id="endTime"
      class="form-control"
      data-date-format="YYYY-MM-DD HH:mm"
      readonly="readonly"
    />
  </div>
  <div class="form-group">
    <label class="title">配图 <b>*</b>：</label>
    <div class="controls">
      <input type="hidden" name="url" ng-model="url" />
      <div>
        <a href="javascript:;" id="upload-btn">+选择上传文件</a>
      </div>
      <div id="imgs-preview"></div>
    </div>
  </div>
  <div class="form-group">
    <label>链接：</label>
    <div class="controls">
      <input
        type="text"
        name="link.url"
        class="form-control"
        ng-model="link.url"
      />
    </div>
  </div>
  <div class="form-group" hidden>
    <label>链接是否需要登录：</label>
    <div class="controls">
      <select
        name="link.needLogin"
        class="form-control"
        ng-model="link.needLogin"
      >
        <option value="0" selected>否</option>
        <option value="1">是</option>
      </select>
    </div>
  </div>
  <div class="form-group">
    <a
      href="javascript:;"
      id="recipient-region-add-btn"
      class="btn btn-success"
      data-l10n-id="p.community.add.region"
    ></a>
    <div class="controls" id="recipient-region-container"></div>
  </div>
  <!--<div class="form-group">
        <label>倒计时（秒）：</label>
        <div class="controls">
            <input type="number" name="countdown" class="form-control" ng-model="countdown"/>
        </div>
    </div>-->
  <div class="form-group">
    <label class="title">目标人群 <b>*</b>：</label>
    <div style="display: flex; align-items: center">
      <input
        style="vertical-align: middle; margin-top: 0"
        id="all"
        type="radio"
        name="all"
        value="all"
      /><label
        for="all"
        style="padding: 0 10px; font-weight: normal; margin-bottom: 0"
        >全部用户</label
      >
      <input
        style="vertical-align: middle; margin-top: 0"
        id="userGroup"
        type="radio"
        name="userGroup"
        value="userGroup"
      /><label
        for="userGroup"
        style="padding: 0 10px; font-weight: normal; margin-bottom: 0"
        >用户分群</label
      >
    </div>
    <div style="display: none; margin-top: 5px" id="user-group-selector">
      <select multiple name="tagId" class="form-control"></select>
      <p style="margin: 10px 0; margin-top: 5px">
        没有想要的用户分群？去
        <a style="cursor: pointer" href="#/user/group?action=create"
          >新增用户分群</a
        >
      </p>
    </div>
  </div>
  <div class="form-group" id="user-group-estimate-number" style="display: none">
    <label>预估人数:</label>
    <span id="estimate-number">0</span>
  </div>
  <div class="form-group">
    <label></label>
    <div class="controls">
      <a
        href="javascript:;"
        id="save-btn"
        class="btn btn-primary"
        data-l10n-id="save"
      ></a>
      <label> </label>
      <a
        href="javascript:window.history.back();"
        class="btn btn-info"
        data-l10n-id="cancel"
      ></a>
    </div>
  </div>
  <p></p>
</form>
