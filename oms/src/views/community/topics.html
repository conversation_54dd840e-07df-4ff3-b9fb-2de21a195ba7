<style type="text/css">
    .x-table img.postimg {
        padding-right: 10px;
    }
    .x-table img.emoji {
        width: 20px;
        height: 20px;
    }
</style>

<script>
    App.ready(function () {
        var form = $('#search-form');
        //init post search action
        var postParams = null;
        var request = function (offset) {
            App.scrollToTop();
            if (!U.isNull(offset)) {
            	postParams.offset = offset;
            }
            if (!postParams.limit) {
            	postParams.limit = 20;
            }
            App.api('/community/topics', postParams, {
                success: function (ps) {
                    var items = ps.items || [];
                    var table = $('#the-table');
                    var template = table.attr('data-template');
                    if (!template) {
                        template = '<tr>' + table.find('tr.template').html() + '</tr>';
                        table.attr('data-template', template);
                    }
                    table.find('tr:not(.header)').remove();
                    for (var i = 0; i < items.length; i++) {
                        var context = items[i];
                        if (!context.author.nick) {
                        	context.author.nick = 'u' + context.author.id;
                        }
                        context.imgs = context.imgs ? U.template('<a href="{{img}}" target="_blank"><img src="{{img}}" class="postimg"/></a>', {
                            'img': App.previewImgUrl(context.imgs.split(',')[0], 120, 120)
                        }) : '';
                        context.createdAt = U.date.format(U.date.parse(context.createdAt), 'MM-dd HH:mm');
                        table.append($(U.template(template, context)));
                    }
                    initOperationButtons();
                    App.pagination(ps, request);
                }
            });
        };
        var initOperationButtons = function() {
        	$('[data-op-type=push]').click(function(){
        		push($(this).attr('data-op-key'));
        	});
        	$('[data-op-type=recommend-create]').click(function(){
        		recommend($(this).attr('data-op-key'), 1);
        	});
        	$('[data-op-type=recommend-remove]').click(function(){
        		recommend($(this).attr('data-op-key'), 0);
        	});
        	$('[data-op-type=recommend-stick]').click(function(){
        		recommendStick($(this).attr('data-op-key'));
        	});
        	$('[data-op-type=official-create]').click(function(){
        		official($(this).attr('data-op-key'), 1);
        	});
        	$('[data-op-type=official-remove]').click(function(){
        		official($(this).attr('data-op-key'), 0);
        	});
        	$('[data-op-type=official-stick]').click(function(){
        		officialStick($(this).attr('data-op-key'));
        	});
        	if (postParams.type != 'recommended') {
        		$('[data-op-type="recommend-remove"]').parent().addClass('none');
        		$('[data-op-type="recommend-stick"]').parent().addClass('none');
        	}
        	if (postParams.type == 'recommended') {
        		$('[data-op-type="recommend-create"]').parent().addClass('none');
        	}
        	if (postParams.type != 'official') {
        		$('[data-op-type="official-remove"]').parent().addClass('none');
        		$('[data-op-type="official-stick"]').parent().addClass('none');
        	}
        	if (postParams.type == 'official') {
        		$('[data-op-type="official-create"]').parent().addClass('none');
        	}
        };
        var searchIt = function () {
            postParams = form.serializeObject();
            $('[data-topics-btn]').removeClass('btn-warning');
            $('[data-topics-btn=' + postParams.type +']').addClass('btn-warning');
            request(0);
        };
        var doneAndRequest = function() {
        	alert(App.text('done'));
            request();
        };
        var recommend = function (id, state) {
            if (!confirm(App.text('confirm.operation'))) {
            	return;
            }
            App.api('/community/topic_recommend', {
                'state': state,
                'topicId': id
            }).then(doneAndRequest);
        };
        var recommendStick = function(id) {
        	if (!confirm(App.text('confirm.operation'))) {
            	return;
            }
            App.api('/community/topic_recommend_stick', {
                'topicId': id
            }).then(doneAndRequest);
        };
        var official = function (id, state) {
        	if (!confirm(App.text('confirm.operation'))) {
            	return;
            }
            App.api('/community/topic_official', {
                'state': state,
                'topicId': id
            }).then(doneAndRequest);
        };
        var officialStick = function(id) {
        	if (!confirm(App.text('confirm.operation'))) {
            	return;
            }
            App.api('/community/topic_official_stick', {
                'topicId': id
            }).then(doneAndRequest);
        };
        var push = function (id) {
            App.router.go('/community/push', {
                'type': 'topic',
                'resourceId': id
            });
        };
        //init
		var searchTopics = function() {
			var name = form.find('[name="name"]').val();
			if (!name) {
				return;
			}
			form.find('[name="type"]').val('named');
			searchIt();
		};
		var init = function() {
			 form.find('[name="name"]').keydown(function (e) {
	            if (e.keyCode == 13) {
	            	searchTopics();
	            }
		     });
			 $('[data-topics-btn]').click(function(){
					var key = $(this).attr('data-topics-btn');
					if (key == 'named') {
						searchTopics();
						return;
					}
					form.find('[name="type"]').val(key);
					searchIt();
			 });
            searchIt();
		};
		init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
    	 <form id="search-form" class="form-inline" action="#" method="post" onsubmit="return false;">
            <input type="hidden" name="type" value="latest"/>
            <input type="hidden" name="limit" value="REQUEST.limit"/>
            <input type="text" name="name" class="form-control" data-l10n-id="p.community.topic.name" data-l10n-for="placeholder"/>
            <a href="javascript:;" class="btn btn-primary" data-topics-btn="named" style="margin-left: 10px;" data-l10n-id="search"></a>
            <a href="javascript:;" class="btn btn-primary" data-topics-btn="latest" style="margin-left: 10px;" data-l10n-id="p.community.roster.latest"></a>
            <a href="javascript:;" class="btn btn-primary" data-topics-btn="official" style="margin-left: 10px;" data-l10n-id="p.community.roster.official"></a>
            <a href="javascript:;" class="btn btn-primary" data-topics-btn="recommended" style="margin-left: 10px;" data-l10n-id="p.community.roster.recommend"></a>
            <a href="#/community/savetopic" id="create-btn" class="btn btn-success" style="margin-left: 10px;" data-l10n-id="create"></a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 25%" data-l10n-id="name"></th>
                <th style="width: 10%" data-l10n-id="image"></th>
                <th style="width: 20%" data-l10n-id="detail"></th>
                <th style="width: 10%" data-l10n-id="createdat"></th>
                <th style="width: 15%" data-l10n-id="createdby"></th>
                <th style="width: 10%" data-l10n-id="p.community.topic.visit.and.partake"></th>
                <th style="width: 10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{topicname}}</td>
                <td>{{imgs}}</td>
                <td>{{describe}}</td>
                <td>{{createdAt}}</td>
                <td>{{author.nick}}</td>
                <td>{{visitCount}}/{{joinCount}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="#/community/savetopic?topicId={{topicId}}" data-l10n-id="edit"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="recommend-create" data-op-key="{{topicId}}" data-l10n-id="p.community.make.recommended"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="recommend-remove" data-op-key="{{topicId}}" data-l10n-id="p.community.remove.recommended"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="recommend-stick" data-op-key="{{topicId}}" data-l10n-id="p.community.stick"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="official-create" data-op-key="{{topicId}}" data-l10n-id="p.community.make.official"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="official-remove" data-op-key="{{topicId}}" data-l10n-id="p.community.remove.official"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="official-stick" data-op-key="{{topicId}}" data-l10n-id="p.community.stick"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="push" data-op-key="{{topicId}}" data-l10n-id="p.community.push"></a>
                            </li>
                            <li>
                                <a href="#/community/savebanner?type=3&topicId={{topicId}}" data-l10n-id="p.community.set.as.banner"></a>
                            </li>
                            <li>
                                <a href="#/community/posts?topicId={{topicId}}&type=topic" data-l10n-id="p.community.post"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>