<script src="js/miscuploader.js"></script>
<script>
App.ready(function(){
	var request = function(offset) {
		App.scrollToTop();
		App.api('/community/fwords',	{
			's' : App.router.getParameter('s'),
			'offset' : U.isNull(offset) ? 0 : offset,
			'limit' : 20
		}).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for ( var i = 0; i < list.length; i++) {
					var context = list[i];
					context = {'s' : context};
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
		});
	};
	
	var remove = function(s) {
		if (!confirm(App.text('confirm.operation'))) {
			return;
		}
		App.api('/community/removefword', {'s': s}).then(function(){
			window.location.reload();
		});
	};
	
	var doUpload = function() {
	    App.uploadFile({
          fileElement: $('#dialog-form').find('[name="file"]')[0],
          url: '/community/uploadfwords',
	      success: function (){
	      	 window.location.reload();
	      }
	   });
	};
	
	var save = function() {
		App.api('/community/savefword', $('#dialog-form').serializeObject()).then(function(){
			window.location.reload();
		});
	};
	
/* 	var openImportDialog = function() {
		App.openDialog({
			url : App.router.getRealPath('/community/uploadfwords.html'),
			noHeader : true,
			primaryClick : doUpload
		});
	}; */
	
	var add = function() {
		App.openDialog({
			url : App.router.getRealPath('/community/addfword.html'),
			noHeader : true,
			primaryClick : save
		});
	};
	
	var submitIt = function() {
		App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
	};
	
	var init = function() {
		$('#search-btn').click(submitIt);
		$("#search-key").keydown(function(e){
		    　if(e.keyCode == 13) {
		    	submitIt();
		    　}
		});
		$('#export-btn').attr('href', App.apiPath('/community/exportfwords', App.specialApiParams()));
		$('#export-btn').attr('target', '_blank');
		//$('#import-btn').click(openImportDialog);
		$('#add-btn').click(add);
		request();
		
		window.remove = remove;
	};
	
	init();
});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
			<input id="search-key" type="text" class="form-control" name="s" ng-model="REQUEST.s" />
			<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
			<a href="javascript:;" id="add-btn" class="btn btn-warning" data-l10n-id="add"></a>
			<a href="javascript:;" id="export-btn" class="btn btn-info">导出</a>
			<!-- <a href="javascript:;" id="import-btn" class="btn btn-success">导入</a> -->
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 50%">敏感词</th>
				<th style="width: 50%" data-l10n-id="operations"></th>
			</tr>
			<tr class="template">
				<td>{{s}}</td>
				<td class="operations">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu">
							<li>
								<a href="javascript:remove('{{s}}')" data-l10n-id="delete"></a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>