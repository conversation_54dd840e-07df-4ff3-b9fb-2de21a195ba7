
<script>
    App.ready(function () {
        var init = function () {
            App.api('/community/buylinks').then(function (result) {
                var table = $('.table-main');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>' + table.find('tr.template').html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < result.length; i++) {
                    var r = result[i];
                    var imgUrl = App.previewImgUrl(r.imgUrl, 120, 120);
                    if (!imgUrl) {
                        r._imgUrl = null;
                    } else {
                        r._imgUrl = '<img src="' + imgUrl + '">';
                    }
                    r._link = r.link;
                    r._priority = r.priority;
                    var row = U.template(template, r);
                    table.append(row);
                }
            });
        };
        init();
        $('.table-main').on('click', '.btn-delete', function () {
        	if (!confirm(App.text('confirm.delete'))) {
        		return;
        	}
            var _id = $(this).attr('id');
            App.api('/community/buylink_remove', {id: _id}).then(function (result) {
              	window.location.reload();
            });
        });
    });
</script>
<style>
</style>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <a href="#/community/buylink" class="btn btn-primary btn-add" role="button">添加</a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table x-table table-main">
            <tr class="header">
                <th style="width: 10%">排序</th>
                <th style="width: 20%">图标</th>
                <th style="width: 50%">link</th>
                <th style="width: 20%">操作</th>
            </tr>
            <tr class="template">
                <td>{{_priority}}</td>
                <td>
                    {{_imgUrl}}
                </td>
                <td>{{_link}}</td>
                <td>
                    <div class="dropdown" style="display: inline-block;">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="#/community/buylink?id={{id}}">编辑</a>
                            </li>
                            <li>
                                <a href="javascript:void(0);" class="btn-delete" id="{{id}}"> 删除</a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>