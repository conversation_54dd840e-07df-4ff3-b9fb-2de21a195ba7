<style type="text/css">
    #post-container {
        margin: 0 0 0 10px;
        word-break: break-all;
        overflow: hidden;
    }

    #post-container .title {
        margin: 10px 0 20px 0;
        padding: 0 0 20px 0;
        border-bottom: 1px dashed;
    }

    #post-container .cover img, #post-container .imgs img {
        width: 320px;
        margin-top: 20px;
    }

    #post-container .pdetail {
        margin-top: 20px;
    }

    .author-img {
        display: block;
        float: left;
        border: 1px solid #CCC;
        width: 60px;
        height: 60px;
    }

    .author-fr {
        float: left;
        padding-left: 10px;
        line-height: 30px;
    }

    #comment-container {
        margin: 30px 0 0 0;
    }

    .comment-sep {
        width: 100%;
        height: 1px;
        margin: 5px 0;
        border-bottom: 1px dashed #CCC;
    }

    .comment > * {
        float: left;
    }

    .comment .d1 {
        /* width: 15%; */
        margin-right: 10px;
    }

    .comment .d1 img {
        width: 30px;
    }

    .comment .d2 {
        width: 60%;
    }

    .comment .nick {
        color: #336699;
    }

    .comment .detail p {
        font-size: 11px;
        margin: 0;
    }

    .comment .detail img {
        width: 100px;
    }

    .comment .d3 {
        width: 25%;
    }

    .comment .time {
        font-size: 11px;
        color: #999;
    }

    textarea.input-xxlarge {
        width: 800px;
        height: 200px;
    }

    img.emoji {
        width: 20px !important;
        height: 20px !important;
    }
</style>
<script>
    App.ready(function () {
        //menu
        App.menu.select('/community/posts');
        var postId = App.router.getParameter('id');
        var hide = function(state){
        	var text = state > 0 ? 'p.community.hide.confirm' : 'p.community.remove.hidden.confirm';
            if (!confirm(App.text(text))) {
            	return;
            }
            App.api('/community/hideorshow', {
                'state': state,
                'postId': postId
            }).then(function() {
            	alert(App.text('done'));
            	window.location.reload();
            });
        };
        var remove = function () {
            if (!confirm(App.text('confirm.delete'))) {
                return;
            }
            App.api('/community/removepost', {'id': postId}, {
                success: function () {
                    App.router.go('/community/posts');
                }
            });
        };
        var loadPost = function () {
            return App.api('/community/post', {'id': postId}, {
                success: function (post) {
                    if ($.isEmptyObject(post)) {
                        alert('Post doest not exists or has been removed');
                        return false;
                    }
                    if (post.author) {
                    	post.author.avatar = makeAvatar(post.author.avatar);
                    }
                    post._createdAt = U.date.format(U.date.parse(post.createdAt), 'MM-dd HH:mm');
                    var imgs = null;
                    if (post.images) {
                    	imgs = '';
                        var imgsTemplate = $('#imgs-template').getCommentTemplate();
                        for (var i = 0; i < post.images.length; i++) {
                            imgs += U.template(imgsTemplate, {'img': post.images[i].url});
                        }
                    }
                    post._imgs = imgs;
                    var container = $('#post-container');
                    var template = container.html();
                    container.empty();
                    var context = {'post': post};
                    container.html(U.template(template, context));
                    container.renderModel(context);
                    if (mars.isEmpty(post.detail)) {
                    	container.find('.post-detail').addClass('none');
                    }
                    if (mars.isEmpty(post.link)) {
                    	container.find('.post-link').addClass('none');
                    }
                    if (mars.isEmpty(post.summary)) {
                    	container.find('.post-summary').addClass('none');
                    }
                    if (mars.isEmpty(post._imgs)) {
                    	container.find('.post-imgs').addClass('none');
                    }
                    if ($.isEmptyObject(post.video)) {
                    	container.find('.post-video').addClass('none');
                    }
                    if (post.hide) {
                    	container.find('#hide-btn').addClass('none');
                    } else {
                    	container.find('#remove-hidden-btn').addClass('none');
                    }
                    container.removeClass('none');
                }
            });
        };
        var makeAvatar = function(url) {
        	if (url) {
        		return App.previewImgUrl(url, 120, 120);
        	}
        	return App.resourcePath('/img/avatar_default.jpg');
        };
        var renderComments = function (ps) {
            var list = ps.items || [];
            var template = $('#comments-template').getCommentTemplate();
            var container = $('#comment-container');
            container.empty();
            for (var i = 0; i < list.length; i++) {
                var context = list[i];
                var commentor = context.commentor;
                commentor.avatar = makeAvatar(commentor.avatar);
                commentor.nick = commentor.nick ? commentor.nick : 'u' + commentor.id;
                if (context.replyTo && !context.replyTo.nick) {
                    context.replyTo.nick = 'u' + context.replyTo.id;
                }
                context._img = context.img ? '<a href="'+context.img+'" target="_blank"><img src="' +
            			App.previewImgUrl(context.img, 120, 120) + '"/></a>' : null;
                context._createdAt = U.date.format(U.date.parse(context.createdAt), 'M-d HH:mm');
                context._replyTo = context.replyTo ? '' : 'none';
                if (context.bigEmotion) {
                	context.detail = '<img src="' + context.bigEmotion.url + '"/>';
                }
                container.append(U.template(template, context));
            }
            App.pagination(ps, loadComments);
        };
        var loadComments = function (offset) {
            var loadParams = {
                postId: postId,
                offset: U.isNull(offset) ? 0 : offset,
                limit: 10
            };
            return App.api('/community/comments', loadParams).then(renderComments);
        };
        var removeComment = function (id) {
            if (!confirm(App.text('confirm.delete'))) {
                return;
            }
            return App.api('/community/removecomment', {id: id}).then(function () {
                loadComments(0);
            });
        };
        var submitComment = function () {
            App.api('/community/comment2', $('#comment-form').serializeObject(), {
                success: function () {
                    $('#comment-form').find('[name="detail"]').val('');
                    loadComments();
                }
            });
        };
        var replyComment = function (id, userId, nick) {
            var commentForm = $('#comment-form');
            commentForm.find('[name="replyTo"]').val(userId);
            commentForm.find('#comment-replyto-nick').val(nick);
            commentForm.find('[name="detail"]').focus();
        };
        //init
        var init = function() {
        	  loadPost().then(loadComments).then(function () {
                  $('#remove-btn').click(remove);
                  $('#hide-btn').click(function(){
                	  hide(1);
                  });
                  $('#remove-hidden-btn').click(function(){
                	  hide(0);
                  });
                  window.removeComment = removeComment;
                  $('#comment-clearreplyto-btn').click(function () {
                      var commentForm = $('#comment-form');
                      commentForm.find('input[name="replyTo"]').val('');
                      commentForm.find('#comment-replyto-nick').val('');
                  });
                  $('#comment-btn').click(submitComment);
                  window.replyComment = replyComment;
              });
        };
        init();
    });
</script>
<div id="imgs-template" class="none"><!-- <li><img src="{{img}}" /></li> --></div>
<div id="comments-template" class="none">
    <!-- 	<div id="comment-{{id}}" class="comment">
            <div class="d1"><img src="{{commentor.avatar}}"/></div>
            <div class="d2">
                <div class="nick">{{commentor.nick}}</div>
                <div class="detail">
                    <p>
                        <span class="{{_replyTo}}">回复<a>{{replyTo.nick}}</a>：</span>
                        {{detail}}
                    </p>
                    {{_img}}
                </div>
            </div>
            <div class="d3">
                <span class="time">{{_createdAt}}</span>
                <div>
                    <a href="javascript:replyComment('{{id}}','{{commentor.id}}','{{commentor.nick}}');" class="replybtn">回复</a>
                    <a href="javascript:removeComment('{{id}}');" class="deletebtn">删除</a>
                </div>
            </div>
        </div>
        <div class="clear"></div>
        <div class="comment-sep"></div> -->
</div>
<div id="post-container">
    <div class="author">
        <img class="author-img" ng-model="post.author.avatar"/>
        <div class="author-fr">
           	用户昵称：<span>{{post.author.nick}}</span> &nbsp;&nbsp;&nbsp;&nbsp;
            用户id：<span><a href="#/user/users?username={{post.author.id}}">{{post.author.id}}</a></span><br/>
            博文id：<span>{{post.id}}</span>&nbsp;&nbsp;&nbsp;&nbsp;
            发布时间：<span>{{post._createdAt}}</span>
        </div>
    </div>
    <div class="clear"></div>
    <div class="pdetail post-detail">{{post.detail}}</div>
    <div class="pdetail post-link">链接：{{post.link}}</div>
    <div class="pdetail post-summary">摘要：{{post.summary}}</div>
    <video class="post-video" ng-model="post.video.url" controls="controls" height="{{post.video.height}}" width="{{post.video.width}}">
          <span>您的浏览器不支持 video 标签。</span>
    </video>
    <ul class="imgs post-imgs">{{post._imgs}}</ul>
    <p style="margin-top:20px;">
        <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="back"></a>
        <a href="javascript:;" id="hide-btn" class="btn btn-warning" data-l10n-id="p.community.make.hidden"></a>
        <a href="javascript:;" id="remove-hidden-btn" class="btn btn-warning" data-l10n-id="p.community.remove.hidden"></a>
        <a href="javascript:;" id="remove-btn" class="btn btn-danger" data-l10n-id="delete"></a>
        <a href="#/community/savepost?id={{post.id}}" class="btn btn-primary" data-l10n-id="edit"></a>
    </p>
    <a name="comments"></a>
    <div id="comment-container"></div>
    <div class="clear h10"></div>
    <div id="pagination-wrap"></div>
     <div id="comment-box">
        <form id="comment-form" role="form" onsubmit="return false;" style="width:480px;">
            <input type="hidden" name="postId" value="{{post.id}}"/>
            <input type="hidden" name="replyTo" value=""/>
            <input type="hidden" name="img" value=""/>
            <div class="form-group">
               <label>回复：</label>
               <input type="text" id="comment-replyto-nick" class="form-control" disabled="disabled"/>
               <a href="javascript:;" id="comment-clearreplyto-btn">清除回复人</a>
           </div>
           <div class="form-group">
              <label>内容：</label>
              <textarea name="detail" class="form-control" rows="2"></textarea>
           </div>
           <div class="form-group">
                <label></label>
                <a id="comment-btn" class="btn btn-primary">评论</a>
           </div>
        </form>
    </div>
</div>

