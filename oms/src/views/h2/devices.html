<script src="js/localesaver.js"></script>
<script>
    App.ready(function () {
        var requestParams = $.extend({'limit': 10}, App.router.getParameters());
        var itemMap;


        var save = function () {
            App.closeDialog();
        };
        var request = function (offset) {
            App.scrollToTop();
            if (mars.isEmpty(requestParams.type) || mars.isEmpty(requestParams.s)) {
                return;
            }
            if (!U.isNull(offset)) {
                requestParams.offset = offset;
            }
            App.api('/h2/devices', requestParams).then(
                function (ps) {
                    itemMap = {};
                    var list = ps.items || [];
                    var table = $('#the-table');
                    var template = table
                        .attr('data-template');
                    if (!template) {
                        template = '<tr>'
                            + table.find('tr.template')
                                .html() + '</tr>';
                        table.attr('data-template',
                            template);
                    }
                    table.find('tr:not(.header)').remove();
                    for (var i = 0; i < list.length; i++) {
                        var context = list[i];
                        context._pim = context.pim === -1 ? '未知' : (context.pim === 0 ? '离线' : '在线')
                        context._hasOwner = context.userId ? 1 : 0;
                        context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
                        context._registered = context.registered ? '已激活' : "未激活"
                        var row = U.template(template, context);
                        table.append(row);
                        itemMap[context.id] = context;
                    }
                    initOperations();
                    App.pagination(ps, request);
                });
        };
        var initOperations = function () {
            var hideKeys = ['link1', 'unlink0'];
            $.each(hideKeys, function (index, item) {
                $('[data-op-display=' + item + ']').addClass('none');
            });
            $('[data-op-type=link]').click(function () {
                link($(this).attr('data-op-key'));
            });
            $('[data-op-type=unlink]').click(function () {
                unlink($(this).attr('data-op-key'));
            });
            $('[data-op-type=show-secret]').click(function () {
                showSecret($(this).attr('data-op-key'));
            });
        };
        var link = function (id) {
            var groupId = prompt('请输入家庭组ID', '');
            if (!groupId) {
                return;
            }
            if (!confirm('确定绑定此设备到此家庭组？')) {
                return;
            }
            return App.api('/h2/link', {id, groupId}).done(function (ps) {
                if (ps.id) {
                    alert('绑定成功');
                } else {
                    alert('绑定失败，设备未注册到主账号且现在不在线');
                }
                request();
            });
        };
        var unlink = function (id) {
            if (!confirm('确定要解绑此设备？')) {
                return;
            }
            return App.api('/h2/unlink', {'id': id}).done(function () {
                alert('解绑成功');
                request();
            });
        };
        var showSecret = function (id) {
            alert(itemMap[id].secret);
        };
        var submitIt = function () {
            var data = $('#search-form').serializeObject();
            App.router.go(App.router.getCurrentPath(), data);
        };
        var init = function () {
            $('#search-btn').click(submitIt);
            $("#search-key").keydown(function (e) {
                if (e.keyCode === 13) {
                    submitIt();
                }
            });
            request();

        };
        init();
    });
</script>

<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
            <select class="form-control" name="type" ng-model="REQUEST.type">
                <option value="1">设备ID</option>
                <option value="2">主人ID</option>
                <option value="3">SN</option>
            </select>
            <input id="search-key" type="text" class="form-control" name="s" ng-model="REQUEST.s"/>
            <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 10%">id</th>
                <th style="width: 10%">SN</th>
                <th style="width: 10%">型号</th>
                <th style="width: 10%">主人</th>
                <th style="width: 10%">所属家庭</th>
                <th style="width: 10%">激活状态</th>
                <th style="width: 10%">在线状态</th>
                <th style="width: 20%">注册时间</th>
                <th style="width: 10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{id}}</td>
                <td>{{sn}}</td>
                <td>{{model}}</td>
                <td>
                    <a href="#/user/users?username={{userId}}">{{userId}}</a>
                </td>
                <td>
                    <a href="#/user/family?groupId={{familyId}}">{{familyId}}</a>
                </td>
                <td>{{_registered}}</td>
                <td>{{_pim}}</td>
                <td>{{_createdAt}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:;" data-op-type="show-secret" data-op-key="{{id}}">显示密钥</a>
                            </li>
                            <li data-op-display="link{{_hasOwner}}">
                                <a href="javascript:;" data-op-type="link" data-op-key="{{id}}">绑定</a>
                            </li>
                            <li data-op-display="unlink{{_hasOwner}}">
                                <a href="javascript:;" data-op-type="unlink" data-op-key="{{id}}">解绑</a>
                            </li>
                            <li>
                                <a href="#/h2/linkhistory?id={{id}}">绑定历史</a>
                            </li>

                        </ul>
                    </div>
                </td>
            </tr>

        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>
