<script>
	App.ready(function () {
		var addImg = function (url) {
			var preview = $('<img/>');
			preview.attr('src', url);
			var wrap = $('<div class="item"/>');
			var close = $('<div class="tclose"/>');
			close.click(function () {
				$(this).parent().remove();
			});
			wrap.append(close);
			wrap.append(preview);
			$('#imgs-preview').append(wrap);
			var val = $('#url').val();
			if(val.length <=0) {
				$('#url').val(url)
			}else{
				$('#url').val(val+","+url)
			}
			console.log($('#url').val())
		};
		var init = function () {
			App.upload.init({
				namespace: 'post',
				type: 'image',
				id: 'upload-btn',
				fileUploaded: function (info) {
					addImg(info.url);
				},
				error: function (errTip) {
					alert('文件上传出错' + errTip);
				}
			});
		};
		init();
	})
	;
</script>


<form id="dialog-form" role="form" onsubmit="return false;">
   <div class="form-group">
   	  <label data-l10n-id="locale"></label>
   	  <div class="controls">
   	  	 <select name="locale" class="form-control" ng-model="locale"></select>
   	  </div>
   </div>

	<div class="form-group">
		<label class="title">描述(多段描述以,分隔)：<b>*</b></label>
		<input type="text" name="spec.tipDesc" id="spec.tipDesc" class="form-control" ng-model="spec.tipDesc"/>
	</div>

	<div class="form-group">
		<label class="title">图片：<b>*</b></label>
		<div class="controls">
			<input type="text" style="display:none;" name="spec.url" id="url" ng-model="spec.url"/>
			<div>
				<a href="javascript:;" id="upload-btn">+选择上传文件</a>
			</div>
			<div id="imgs-preview"></div>
		</div>
	</div>

</form>