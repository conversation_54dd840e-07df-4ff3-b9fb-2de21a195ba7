<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script src="js/region-select.js"></script>
<link
  rel="stylesheet"
  type="text/css"
  href="vendor/datetimepicker/bootstrap-datetimepicker.css"
/>
<link rel="stylesheet" type="text/css" href="vendor/select2/select2.css" />
<script src="vendor/moment.js"></script>
<script src="vendor/datetimepicker/bootstrap-datetimepicker.js"></script>
<script src="vendor/datetimepicker/locales/bootstrap-datetimepicker.zh-CN.js"></script>

<style type="text/css">
  #imgs-preview .item {
    position: relative;
    width: 320px;
    margin: 10px 0 0 0;
  }

  #imgs-preview .item .tclose {
    top: -15px;
    right: -15px;
  }

  #imgs-preview .item img {
    width: 320px;
  }
</style>

<script src="js/localesaver.js"></script>
<script>
  App.ready(function () {
    var localeSaver = new LocaleSaver({
      dialogUrl: '/system/tip_info_spec.html',
    });
    var form = $('#save-form');
    // var setImgUrl = function (url) {
    //     form.find('[name=deviceTypeUrl]').val(url);
    // };
    var addImg = function (url) {
      var preview = $('<img/>');
      preview.attr('src', url);
      var wrap = $('<div class="item"/>');
      var close = $('<div class="tclose"/>');
      close.click(function () {
        $(this).parent().remove();
        // setImgUrl('');
      });
      wrap.append(close);
      wrap.append(preview);
      $('#imgs-preview').append(wrap);
      // setImgUrl(url);
    };
    var id = App.router.getParameter('id');
    var save = function () {
      var data = form.serializeObject();
      data.url = localeSaver.getDataAsModel().url;
      data.tipDesc = localeSaver.getDataAsModel().tipDesc;
      if (!data.url) {
        alert('配图不能为空');
        return;
      }
      if (!data.tipDesc) {
        alert('描述不能为空');
        return;
      }

      if (id) {
        data['id'] = id;
      }

      return App.api('/system/tipInfo', data).then(function () {
        // App.router.go("/system/tip_info_list");
      });
    };

    var init = function () {
      App.upload.init({
        namespace: 'post',
        type: 'image',
        id: 'upload-btn',
        fileUploaded: function (info) {
          addImg(info.url);
        },
        error: function (errTip) {
          alert('文件上传出错' + errTip);
        },
      });
      if (id) {
        App.api(
          '/system/findTipInfo',
          {
            id: id,
          },
          {
            success: function (appDeviceInfo) {
              form.renderModel(appDeviceInfo);
              localeSaver.setDataByModel(appDeviceInfo.localizedProps, [
                'tipDesc',
                'url',
              ]);
              //addImg(appDeviceInfo.deviceTypeUrl);
            },
          }
        );
      }
      $.when(localeSaver.init()).then(function () {
        $('#save-btn').click(save);
      });
    };
    init();
  });
</script>
<style>
  .title b {
    color: red;
    padding-right: 5px;
  }

  #the-table img {
    width: 100px;
    height: auto;
  }
</style>
<form id="save-form" role="form" onsubmit="return false;">
  <div class="form-group">
    <label class="title">设备Key：<b>*</b></label>
    <input
      type="text"
      name="keyId"
      id="keyId"
      class="form-control"
      ng-model="keyId"
      required
    />
  </div>

  <div class="form-group">
    <label class="title">描述：</label>
    <input
      type="text"
      name="annotation"
      id="annotation"
      class="form-control"
      ng-model="annotation"
      required
    />
  </div>

  <div class="form-group">
    <label>是否启用：</label>
    <select name="enable" class="form-control" ng-model="enable">
      <option value="0">否</option>
      <option value="1">是</option>
    </select>
  </div>

  <div class="form-group">
    <div class="controls">
      <a href="javascript:;" id="local-add-btn">
        <span>+</span>
        <span data-l10n-id="localize"></span>
      </a>

      <div id="locale-spec-template">
        <div class="locale-spec">
          <div class="locale-spec-item">
            <label data-l10n-id="locale"></label>:
            <span class="locale">{{locale.name}}</span>
            <a
              href="javascript:;"
              class="edit"
              data-l10n-id="edit"
              data-op-type="update"
              data-op-key="{{locale.key}}"
            ></a>
            <a
              href="javascript:;"
              class="edit"
              data-l10n-id="delete"
              data-op-type="remove"
              data-op-key="{{locale.key}}"
            ></a>
          </div>
          <div class="locale-spec-item">
            <label>描述</label>:<span>{{spec.tipDesc}}</span><br />
            <label>图片</label>:<span>{{spec.url}}</span><br />
          </div>
        </div>
      </div>
      <div id="locale-specs"></div>
    </div>
  </div>

  <div class="form-group">
    <label></label>
    <div class="controls">
      <a
        href="javascript:;"
        id="save-btn"
        class="btn btn-primary"
        data-l10n-id="save"
      ></a>
      <label> </label>
      <a
        href="javascript:window.history.back();"
        class="btn btn-info"
        data-l10n-id="cancel"
      ></a>
    </div>
  </div>
</form>
