<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script src="js/region-select.js"></script>
<link rel="stylesheet" type="text/css" href="vendor/datetimepicker/bootstrap-datetimepicker.css"/>
<link rel="stylesheet" type="text/css" href="vendor/select2/select2.css"/>
<script src="vendor/moment.js"></script>
<script src="vendor/datetimepicker/bootstrap-datetimepicker.js"></script>
<script src="vendor/datetimepicker/locales/bootstrap-datetimepicker.zh-CN.js"></script>


<style type="text/css">
    #imgs-preview .item {
        position: relative;
        width: 320px;
        margin: 10px 0 0 0;
    }

    #imgs-preview .item .tclose {
        top: -15px;
        right: -15px;
    }

    #imgs-preview .item img {
        width: 320px;
    }
</style>

<script src="js/localesaver.js"></script>
<script>
    App.ready(function () {
        var localeSaver = new LocaleSaver({'dialogUrl': '/system/app_device_spec.html'});
        var form = $('#save-form');
        var setImgUrl = function (url) {
            form.find('[name=deviceTypeUrl]').val(url);
        };
        var addImg = function (url) {
            var preview = $('<img/>');
            preview.attr('src', url);
            var wrap = $('<div class="item"/>');
            var close = $('<div class="tclose"/>');
            close.click(function () {
                $(this).parent().remove();
                setImgUrl('');
            });
            wrap.append(close);
            wrap.append(preview);
            $('#imgs-preview').html('').append(wrap);
            setImgUrl(url);
        };
        var id = App.router.getParameter("id");
        var save = function () {
            var data = form.serializeObject();
            data.deviceTypeName = localeSaver.getDataAsModel().deviceTypeName;
            data.typeName = localeSaver.getDataAsModel().typeName;
            if (!data.deviceTypeUrl) {
                alert("配图不能为空");
                return;
            }
            if (!data.seqId) {
                alert("优先级不能为空");
                return;
            }
            if (!data.typeName) {
                alert("设备类型不能为空");
                return;
            }

            if (!data.deviceTypeName) {
                alert("主标题不能为空");
                return;
            }
            if (id) {
                data["id"] = id
            }

            return App.api('/system/updateAppDeviceInfo',
                data).then(function () {
                App.router.go('/system/app_device_list');
            });
        };

        var init = function () {
            App.upload.init({
                namespace: 'post',
                type: 'image',
                id: 'upload-btn',
                fileUploaded: function (info) {
                    addImg(info.url);
                },
                error: function (errTip) {
                    alert('文件上传出错' + errTip);
                }
            });
            if (id) {
                App.api('/system/findAppDeviceInfo', {
                    'id': id
                }, {
                    success: function (appDeviceInfo) {
                        form.renderModel(appDeviceInfo);
                        localeSaver.setDataByModel(appDeviceInfo.localizedProps, ['deviceTypeName','typeName'])
                        addImg(appDeviceInfo.deviceTypeUrl);
                    }
                });
            }
            $.when(localeSaver.init()).then(function () {
                $('#save-btn').click(save);
            });

        };
        init();
    })
    ;
</script>
<style>
    .title b {
        color: red;
        padding-right: 5px;
    }

    #the-table img {
        width: 100px;
        height: auto;
    }
</style>
<form id="save-form" role="form" onsubmit="return false;">

    <div class="form-group">
        <label class="title">设备Key：<b>*</b></label>
        <input type="text" name="keyId" id="keyId" class="form-control" ng-model="keyId"  required/>
    </div>

    <div class="form-group">
        <label class="title">设备ID：<b>*</b></label>
        <input type="text" name="deviceTypeId" id="deviceTypeId" class="form-control" ng-model="deviceTypeId"  required/>
    </div>

    <div class="form-group">
        <label >配图：</label>
        <div class="controls">
            <input type="hidden" name="deviceTypeUrl" ng-model="deviceTypeUrl"/>
            <div>
                <a href="javascript:;" id="upload-btn">+选择上传文件</a>
            </div>
            <div id="imgs-preview"></div>
        </div>
    </div>

    <div class="form-group">
        <label class="title">区别码：</label>
        <input type="text" name="typeCode" id="typeCode" class="form-control" ng-model="typeCode" />
    </div>

    <div class="form-group">
        <label class="title">分类ID：<b>*</b></label>
        <input type="text" name="typeId" id="typeId" class="form-control" ng-model="typeId"  required/>
        <div class="ant-alert-message">数字越大，该类型整体优先级越高</div>
    </div>

    <div class="form-group">
        <label class="title">优先级：<b>*</b></label>
        <input type="text" name="seqId" id="seqId" class="form-control" ng-model="seqId"  required/>
        <div class="ant-alert-message">数字越大，同类型内优先级越高</div>
    </div>

    <div class="form-group">
        <label>是否启用：</label>
        <select name="enable" class="form-control" ng-model="enable">
            <option value="0">否</option>
            <option value="1">是</option>
        </select>
    </div>


    <div class="form-group">
        <div class="controls">

            <a href="javascript:;" id="local-add-btn" >
                <span>+</span>
                <span data-l10n-id="localize"></span>
            </a>

            <div id="locale-spec-template">
                <div class="locale-spec">
                    <div class="locale-spec-item">
                        <label data-l10n-id="locale"></label>:
                        <span class="locale">{{locale.name}}</span>
                        <a href="javascript:;" class="edit" data-l10n-id="edit" data-op-type="update" data-op-key="{{locale.key}}"></a>
                        <a href="javascript:;" class="edit" data-l10n-id="delete" data-op-type="remove" data-op-key="{{locale.key}}"></a>
                    </div>
                    <div class="locale-spec-item">
                        <label>设备名</label>:<span>{{spec.deviceTypeName}}</span><br/>
                        <label>设备类型</label>:<span>{{spec.typeName}}</span><br/>
                    </div>
                </div>
            </div>
            <div id="locale-specs"></div>
        </div>
    </div>

    <div class="form-group">
        <label></label>
        <div class="controls">
            <a href="javascript:;" id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
            <label> </label>
            <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
        </div>
    </div>
</form>