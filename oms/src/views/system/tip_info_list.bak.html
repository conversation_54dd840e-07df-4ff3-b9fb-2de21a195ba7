<!-- <script>
  App.ready(function () {
    window.edit = function (id) {
      /*var params = App.router.getParameters();
            params['petType'] = 1;*/
      App.router.go("/system/tip_info?id=" + id);
    };

    var cpType;
    var bundle = "tips";
    var showLocalizedString = function (map) {
      var s = "";
      if (!map) {
        return s;
      }
      var i = 0;
      for (var locale in map) {
        if (i > 0) {
          s += "<br/><br/>";
        }
        s += locale + ":";
        s += "<br/>";
        s += map[locale];
        i++;
      }
      return s;
    };

    window.disable = function (id) {
      var data = {};
      data["enable"] = 0;
      App.api("/system/enableTipInfo", {
        id: id,
        doc: JSON.stringify(data),
      }).then(function () {
        request();
      });
    };
    window.remove = function (id) {
      if (!confirm(App.text("confirm.delete"))) {
        return;
      }
      App.api("/system/deleteTipInfo", {
        id: id,
      }).then(function () {
        window.location.reload();
      });
    };
    window.enable = function (id) {
      var data = {};
      data["enable"] = 1;
      App.api("/system/enableTipInfo", {
        id: id,
        doc: JSON.stringify(data),
      }).then(function () {
        request();
      });
    };

    var request = function (offset, type) {
      if (!U.isNull(type)) {
        cpType = type;
      }
      App.scrollToTop();
      var params = $.extend(
        {
          limit: 10,
        },
        App.router.getParameters()
      );
      params.offset = !U.isNull(offset) ? offset : params.offset;
      var keyId = App.router.getParameter("keyId");
      var enable = App.router.getParameter("enable");
      params.keyId = keyId;
      params.enable = enable;
      App.api("/system/tipList", params).then(function (ps) {
        var list = ps.items || [];
        var table = $("#the-table");
        var template = table.attr("data-template");
        if (!template) {
          template = "<tr>" + table.find("tr.template").html() + "</tr>";
          table.attr("data-template", template);
        }
        table.find("tr:not(.header)").remove();
        for (var i = 0; i < list.length; i++) {
          var context = list[i];
          context._keyId = context.keyId;
          context._id = context.id;
          context._enable = context.enable === 1 ? "已启用" : "未启用";

          var row = U.template(template, context);
          table.append(row);
          var hideKeys = ["enable1", "disable0"];
          $.each(hideKeys, function (index, item) {
            $("[data-op-display=" + item + "]").addClass("none");
          });
        }

        App.pagination(ps, request);
        $(".btn-primary").removeClass("active");
      });
    };
    window.request = request;

    var init = function () {
      $("#search-btn").click(function () {
        App.router.go(
          App.router.getCurrentPath(),
          $("#search-form").serializeObject()
        );
      });
      window.remove = remove;
      $("#import-btn").click(function () {
        var bundleText = "提示表";
        if (!confirm("将导入到" + bundleText + "?")) {
          return;
        }
        App.router.go("/system/tips_localebundle_import", { bundle: "tips" });
      });

      $("#import-btn-url").click(function () {
        var bundleText = "提示表";
        if (!confirm("将导入到" + bundleText + "?")) {
          return;
        }
        App.router.go("/system/tips_url_localebundle_import", {
          bundle: "tipsUrl",
        });
      });

      $("#export-btn").attr(
        "href",
        App.apiPath(
          "/system/localebundle_export",
          $.extend({ bundle: "tips" }, App.specialApiParams())
        )
      );
      $("#export-btn").attr("target", "_blank");

      $("#export-btn-url").attr(
        "href",
        App.apiPath(
          "/system/localebundle_export",
          $.extend({ bundle: "tipsUrl" }, App.specialApiParams())
        )
      );
      $("#export-btn-url").attr("target", "_blank");
      request();
    };

    init();
  });
</script> -->
<!-- <style>
  .region b {
    color: red;
    padding-right: 5px;
  }

  #the-table img {
    width: 100px;
    height: auto;
  }
</style> -->

<!-- <div class="panel panel-default x-panel">
  <div class="panel-heading">
    <form
      id="search-form"
      class="form-inline"
      action="#"
      method="get"
      onsubmit="return false;"
    >
      是否启用:
      <select name="enable" class="form-control" ng-model="REQUEST.enable">
        <option value="0">否</option>
        <option value="1" selected="selected">是</option>
      </select>
      ID：
      <input
        type="text"
        name="keyId"
        class="form-control"
        ng-model="REQUEST.keyId"
      />
      <a
        href="javascript:;"
        id="search-btn"
        class="btn btn-primary"
        data-l10n-id="search"
      ></a>
      <a href="#/system/tip_info" class="btn btn-info" data-l10n-id="add"></a>
      <a href="javascript:;" id="export-btn" class="btn btn-info">导出描述</a>
      <a href="javascript:;" id="export-btn-url" class="btn btn-info"
        >导出图片</a
      >
      <a href="javascript:;" id="import-btn" class="btn btn-warning"
        >导入描述</a
      >
      <a href="javascript:;" id="import-btn-url" class="btn btn-warning"
        >导入图片</a
      >
    </form>
  </div>

  <div class="panel-body">
    <table class="table table-hover x-table" id="the-table">
      <tr class="header">
        <th style="width: 15%">id</th>
        <th style="width: 15%">key</th>
        <th style="width: 35%">描述</th>
        <th style="width: 10%">是否启用</th>
        <th style="width: 25%">操作</th>
      </tr>
      <tr class="template">
        <td>{{_id}}</td>
        <td>{{_keyId}}</td>
        <td>{{annotation}}</td>
        <td>{{_enable}}</td>
        <td class="operations">
          <div class="dropdown">
            <a
              class="dropdown-toggle"
              data-toggle="dropdown"
              href="javascript:;"
            >
              <span data-l10n-id="operations"></span>
              <b class="caret"></b>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
              <li>
                <a href="javascript:edit('{{id}}');">编辑</a>
              </li>
              <li data-op-display="enable{{enable}}">
                <a href="javascript:enable('{{id}}');">启用</a>
              </li>
              <li data-op-display="disable{{enable}}">
                <a href="javascript:disable('{{id}}');">禁用</a>
              </li>
              <li>
                <a
                  href="javascript:remove('{{id}}');"
                  data-l10n-id="delete"
                ></a>
              </li>
            </ul>
          </div>
        </td>
      </tr>
    </table>
  </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div> -->

<script>
  App.ready(function () {
    App.generator.generatePage(
      '/oms/system/tip-info/list',
      '/system/tip_info_list'
    );
  });
</script>

<div style="height: calc(100vh - 60px)">
  <iframe id="iframe" frameborder="0" width="100%" height="100%"></iframe>
</div>
