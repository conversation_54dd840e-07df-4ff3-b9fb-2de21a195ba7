<script>
    App.ready(function () {
        //App.menu.select('/system/app_device_list');

        window.edit = function (id) {
            /*var params = App.router.getParameters();
            params['petType'] = 1;*/
            App.router.go('/system/app_device?id=' + id);
        };


        var cpType;
        var bundle = "devices";
        var showLocalizedString = function (map) {
            var s = '';
            if (!map) {
                return s;
            }
            var i = 0;
            for (var locale in map) {
                if (i > 0) {
                    s += '<br/><br/>';
                }
                s += locale + ":";
                s += '<br/>';
                s += map[locale];
                i++;
            }
            return s;
        };

        window.disable = function (id) {
            var data = {};
            data['enable'] = 0;
            App.api('/system/enableAppDeviceInfo', {
                'id': id,
                'doc': JSON.stringify(data)
            }).then(function () {
                request();
            });
        };
        window.remove = function (id) {
            if (!confirm(App.text('confirm.delete'))) {
                return;
            }
            App.api('/system/deleteAppDeviceInfo', {
                'id': id
            }).then(function () {
                window.location.reload();
            });
        };
        window.enable = function (id) {
            var data = {};
            data['enable'] = 1;
            App.api('/system/enableAppDeviceInfo', {
                'id': id,
                'doc': JSON.stringify(data)
            }).then(function () {
                request();
            });
        };


        var request = function (offset, type) {
            if (!U.isNull(type)) {
                cpType = type;
            }
            App.scrollToTop();
            var params = $.extend({
                'limit': 10
            }, App.router.getParameters());
            params.offset = (!U.isNull(offset)) ? offset : params.offset;
            var typeId = App.router.getParameter('typeId');
            var enable = App.router.getParameter('enable');
            var deviceTypeId = App.router.getParameter('deviceTypeId');
            params.typeId = typeId;
            params.enable = enable;
            params.deviceTypeId = deviceTypeId;
            App.api('/system/appDeviceInfo', params).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>' + table.find('tr.template').html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._body = showLocalizedString(context.body);
                    context._keyId = context.keyId;
                    context._deviceTypeId = context.deviceTypeId;
                    context._deviceTypeName = context.deviceTypeName;
                    context._typeName = context.typeName;
                    context._deviceTypeUrl = context.deviceTypeUrl;
                    context._typeCode = context.typeCode;
                    context._typeId = context.typeId;
                    context._seqId = context.seqId;
                    context._enable = context.enable === 1 ? "已启用" : "未启用";

                    var row = U.template(template, context);
                    table.append(row);
                    var hideKeys = ['enable1', 'disable0'];
                    $.each(hideKeys, function (index, item) {
                        $('[data-op-display=' + item + ']').addClass('none');
                    });
                }

                App.pagination(ps, request);
                $('.btn-primary').removeClass("active");

            });
        };
        window.request = request;


        var init = function () {
            $('#search-btn').click(function() {
                App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
            });
            window.remove = remove;
            $('#import-btn').click(function(){
                var bundleText = "设备表";
                if (!confirm('将导入到' + bundleText + '?')) {
                    return;
                }
                App.router.go('/system/devices_localebundle_import', {'bundle': bundle});
            });
            $('#export-btn').attr('href', App.apiPath('/system/localebundle_export', $.extend({'bundle': bundle}, App.specialApiParams())));
            $('#export-btn').attr('target', '_blank');
            request();
        };

        init();
    });
</script>
<style>
    .region b {
        color: red;
        padding-right: 5px;
    }

    #the-table img {
        width: 100px;
        height: auto;
    }
</style>

<div class="panel panel-default x-panel">

    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
            是否启用:
            <select name="enable" class="form-control" ng-model="REQUEST.enable">
                <option value="0" >否</option>
                <option value="1" selected="selected">是</option>
            </select>
            分类ID：
            <input type="text" name="typeId" class="form-control" ng-model="REQUEST.typeId" />
            设备ID:
            <input type="text" name="deviceTypeId" class="form-control" ng-model="REQUEST.deviceTypeId" />
            <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
            <a href="#/system/app_device" class="btn btn-info" data-l10n-id="add"></a>
            <a href="javascript:;" id="export-btn" class="btn btn-info">导出</a>
            <a href="javascript:;" id="import-btn" class="btn btn-warning">导入</a>
        </form>
    </div>


    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 6%">设备Key</th>
                <th style="width: 6%">设备ID</th>
                <th style="width: 16%">设备名</th>
                <th style="width: 12%">设备类型</th>
                <th style="width: 12%">设备配图</th>
                <th style="width: 6%">区别码</th>
                <th style="width: 6%">分类ID</th>
                <th style="width: 6%">优先级</th>
                <th style="width: 8%">是否启用</th>
                <th style="width: 8%">操作</th>
            </tr>
            <tr class="template">
                <td>{{_keyId}}</td>
                <td>{{_deviceTypeId}}</td>
                <td>{{_deviceTypeName}}</td>
                <td>{{_typeName}}</td>
                <td>{{_deviceTypeUrl}}</td>
                <td>{{_typeCode}}</td>
                <td>{{_typeId}}</td>
                <td>{{_seqId}}</td>
                <td>{{_enable}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:edit('{{id}}');">编辑</a>
                            </li>
                            <li data-op-display="enable{{enable}}">
                                <a href="javascript:enable('{{id}}');">启用</a>
                            </li>
                            <li data-op-display="disable{{enable}}">
                                <a href="javascript:disable('{{id}}');">禁用</a>
                            </li>
                            <li>
                                <a href="javascript:remove('{{id}}');" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>