<script>
  App.ready(function () {
    var deviceType = App.router.getParameter('deviceType');
    var hardware = App.router.getParameter('hardware');
    var urlParam = App.router.getParameters();
    var hardwares = [1];
    var url = '/oms/device/' + urlParam.deviceType + '/logs-with-file';
    var selectedUrl =
      '/new-dev/logs-with-file?deviceType=' + urlParam.deviceType;

    var params = [];
    if (urlParam.deviceId) {
      params.push('deviceId=' + urlParam.deviceId);
    }
    if (!!hardware) {
      params.push('hardware=' + hardware);
      selectedUrl += '&hardware=' + hardware;
    }

    // if (params.length > 0) {
    //   url += "?" + params.join("&");
    // }

    App.generator.generatePage(url, selectedUrl);
  });
</script>
<div style="height: calc(100vh - 60px)">
  <iframe id="iframe" frameborder="0" width="100%" height="100%"></iframe>
</div>
