<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script>
    App.ready(function () {
        var form = $('#save-form');
        App.menu.select('/d3/sound_firmwares');
        var previewBinary = function (result) {
            var preview = $('#binary-preview');

            if (result) {
                preview.html('<a href="' + result.url + '" target="_blank">' + result.url + '</a>');
            } else {
                preview.html('');
            }
            form.find('input[name="url"]').val(result ? result.url : '');
            form.find('input[name="size"]').val(result ? result.size : '');
            form.find('input[name="digest"]').val(result ? result.digest : '');
        };
        var result = null;
        App.upload.init({
            namespace: 'd3-sound',
            type: 'firmware',
            id: 'upload-btn',
            fileUploaded: function (info) {
                $.ajax({
                    url: info.url + '?hash/md5',
                    dataType: 'json',
                    success: function (ret) {
                        info.digest = ret.md5;
                        result = info;
                        previewBinary(result);
                    }
                });

            },
            error: function (errTip) {
                alert('文件上传出错' + errTip);
            }
        });


        var save = function () {
            var data = form.serializeObject();
            App.api('/d3/sound_firmware_save', data).then(function () {
                App.router.go('/d3/sound_firmwares');
            });
        };

        var loadModel = function () {
            var id = App.router.getParameter('id');
            if (!id) {
                return;
            }
            return App.api('/d3/sound_firmware', {'id': id}).then(function (data) {
                if ($.isEmptyObject(data)) {
                    alert('The firmware doest not exists or has been removed')
                    return false;
                }
                form.renderModel({model: data});
                previewBinary(data);
            });
        };
        var init = function () {
            //init buttons
            $('#save-btn').click(save);
        }
        //init
        loadModel();
        init();


    });
</script>
<form id="save-form" role="form" onsubmit="return false;">
    <input type="hidden" name="id" ng-model="model.id"/>
    <input type="hidden" name="url" ng-model="model.url"/>
    <input type="hidden" name="size" ng-model="model.size"/>
    <input type="hidden" name="digest" ng-model="model.digest"/>

    <div class="form-group">
        <label>版本号</label>
        <input type="number" name="version" class="form-control" ng-model="model.version"/>
    </div>
    <div class="form-group" id="upload-form">
        <label data-l10n-id="file"></label>
        <div>
            <a href="javascript:;" id="upload-btn">+选择上传文件</a>
        </div>
        <div id="binary-preview"></div>
    </div>

    <div class="form-group">
        <label data-l10n-id="detail"></label>
        <textarea name="note" class="form-control" rows="5" ng-model="model.note"></textarea>
    </div>
    <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
    <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
</form>
