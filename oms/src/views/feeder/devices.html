<script src="js/localesaver.js"></script>
<script>
  App.ready(function () {
    var requestParams = $.extend({ limit: 10 }, App.router.getParameters());
    var itemMap;
    // var showDetail = new LocaleSaver({'dialogUrl': '/feeder/feeder-detail.html'});
    var statusOptions = {
      0: "离线",
      1: "在线",
    };
    var switchOptions = {
      0: "关闭",
      1: "打开",
    };
    var feedingOptions = {
      0: "正常",
      1: "喂食中",
    };
    var updateOptions = {
      1: "准备升级",
      2: "升级中",
      3: "升级完成",
      4: "升级失败",
      5: "设备离线未开始升级",
    };
    var openDialog = function (model) {
      App.openDialog({
        type: "single",
        url: App.router.getRealPath("/feeder/feeder-detail.html"),
        context: model,
        titleTextKey: "detail",
        primaryClick: save,
      });
    };
    var cu = function (id) {
      App.api("/feeder/devices", { type: "id", s: id }).then(function (ps) {
        itemMap = {};
        var list = ps.items || [];
        for (var i = 0; i < list.length; i++) {
          var context = list[i];
          if (context.settings) {
            context._feedNotify =
              switchOptions[context.settings.feedNotify] || "未知";
            context._foodNotify =
              switchOptions[context.settings.foodNotify] || "未知";
            context._manualLock =
              switchOptions[context.settings.manualLock] || "未知";
          }
          if (context.state) {
            context._pim = statusOptions[context.state.pim] || "未知";
            context._feeding = feedingOptions[context.state.feeding] || "未知";
            context._ota = updateOptions[context.state.ota] || "未知";
            context._percent = context.state.percent + "%";
            context._food = context.state.food + "";

            if (context.state.wifi) {
              context._wifiSsid = context.state.wifi.ssid;
              context._wifiRsq = context.state.wifi.rsq;
            } else {
              context._wifiSsid = "-";
              context._wifiRsq = "-";
            }
          }
          context._hasOwner = context.owner ? 1 : 0;
          context._hardFirware =
            (context.hardware || "") + "/" + (context.firmware || "");
          context._createdAt = U.date.format(
            U.date.parse(context.createdAt),
            "yyyy-MM-dd HH:mm"
          );
          itemMap = context;
        }
        openDialog(itemMap);
      });
    };

    var restart = function (id) {
      if (confirm("确定重启该设备？")) {
        console.log(id);
        App.api("/feeder/reboot", { id: id }).then(function (result) {
          console.log(result);
        });
      }
    };

    var fixError = function (id) {
      if (confirm("确定要修复门店故障么？")) {
        console.log(id);
        App.api("/feeder/repair_door", { id: id }).then(function (result) {
          console.log(result);
        });
      }
    };

    var setLogLevel = function (id) {
      var logLevel = prompt("请设置Log等级：", "");
      var num = Number(logLevel);
      if (!Number.isNaN(num)) {
        console.log(id, num);
        App.api("/feeder/log_level", { id: id, level: num }).then(function (
          result
        ) {
          console.log(result);
        });
      } else {
        alert("请输入数字！");
      }
    };

    var save = function () {
      App.closeDialog();
    };
    var request = function (offset) {
      App.scrollToTop();
      if (mars.isEmpty(requestParams.type) || mars.isEmpty(requestParams.s)) {
        return;
      }
      if (!U.isNull(offset)) {
        requestParams.offset = offset;
      }
      App.api("/feeder/devices", requestParams).then(function (ps) {
        itemMap = {};
        var list = ps.items || [];
        var table = $("#the-table");
        var template = table.attr("data-template");
        if (!template) {
          template = "<tr>" + table.find("tr.template").html() + "</tr>";
          table.attr("data-template", template);
        }
        var petTemplate = table.find("td.petTemplate").html();
        table.find("tr:not(.header)").remove();
        for (var i = 0; i < list.length; i++) {
          var context = list[i];
          context._pim = context.state
            ? statusOptions[context.state.pim] || "未知"
            : "未知";
          context._hasOwner = context.relation ? 1 : 0;
          context._createdAt = U.date.format(
            U.date.parse(context.createdAt),
            "yyyy-MM-dd HH:mm"
          );

          if (
            context.relation &&
            context.relation.petIds &&
            context.relation.petIds.length
          ) {
            var container = "";
            for (var j = 0; j < context.relation.petIds.length; j++) {
              var petId = context.relation.petIds[j];
              var map = [];
              map.petId = petId;
              var regExp = new RegExp("{{petId}}", "g");
              container += petTemplate.replace(regExp, petId) + "\n";
            }
            if (context.relation.petIds.length > 0) {
              context.petContainer = container;
            }
          }
          var row = U.template(template, context);
          table.append(row);
          itemMap[context.id] = context;
        }
        initOperations();
        App.pagination(ps, request);
      });
    };
    var initOperations = function () {
      var hideKeys = ["link1", "unlink0"];
      $.each(hideKeys, function (index, item) {
        $("[data-op-display=" + item + "]").addClass("none");
      });
      $("[data-op-type=link]").click(function () {
        link($(this).attr("data-op-key"));
      });
      $("[data-op-type=unlink]").click(function () {
        unlink($(this).attr("data-op-key"));
      });
      $("[data-op-type=show-secret]").click(function () {
        showSecret($(this).attr("data-op-key"));
      });
    };
    var link = function (id) {
      // var petId = prompt('请输入宠物ID', '');
      // if (!petId) {
      //     return;
      // }
      // if (!confirm('确定绑定此宠物到此设备？')) {
      //     return;
      // }
      var groupId = prompt("请输入家庭组ID", "");
      if (!groupId) {
        return;
      }
      if (!confirm("确定绑定此家庭到设备么？")) {
        return;
      }
      return App.api("/feeder/link", { id: id, groupId: groupId }).done(
        function () {
          alert("绑定成功");
          request();
        }
      );
      // return App.api('/feeder/link', {'id': id, 'petId': petId}).done(function () {
      //     alert('绑定成功');
      //     request();
      // });
    };
    var unlink = function (id) {
      if (!confirm("确定要解绑此设备？")) {
        return;
      }
      return App.api("/feeder/unlink", { id: id }).done(function () {
        alert("解绑成功");
        request();
      });
    };
    var showSecret = function (id) {
      alert(itemMap[id].secret);
    };
    var submitIt = function () {
      var data = $("#search-form").serializeObject();
      App.router.go(App.router.getCurrentPath(), data);
    };
    var init = function () {
      $("#search-btn").click(submitIt);
      $("#search-key").keydown(function (e) {
        if (e.keyCode == 13) {
          submitIt();
        }
      });
      request();
    };
    init();
    window.cu = cu;
    window.restartFeeder = restart;
    window.fixFeederError = fixError;
    window.setFeederLogLevel = setLogLevel;
  });
</script>

<div class="panel panel-default x-panel">
  <div class="panel-heading">
    <form
      id="search-form"
      class="form-inline"
      action="#"
      method="get"
      onsubmit="return false;"
    >
      <select class="form-control" name="type" ng-model="REQUEST.type">
        <option value="id">设备ID</option>
        <option value="mac">Mac</option>
        <option value="sn">SN</option>
        <option value="owner">主人ID</option>
      </select>
      <input
        id="search-key"
        type="text"
        class="form-control"
        name="s"
        ng-model="REQUEST.s"
      />
      <a
        href="javascript:;"
        id="search-btn"
        class="btn btn-primary"
        data-l10n-id="search"
      ></a>
    </form>
  </div>
  <div class="panel-body">
    <table class="table table-hover x-table" id="the-table">
      <tr class="header">
        <th style="width: 8%">id</th>
        <th style="width: 8%">MAC</th>
        <th style="width: 14%">SN</th>
        <th style="width: 14%">注册时间</th>
        <th style="width: 10%">硬/固件</th>
        <th style="width: 6%">时区</th>
        <th style="width: 8%">主人</th>
        <th style="width: 8%">宠物</th>
        <th style="width: 8%">所属家庭</th>
        <th style="width: 6%">状态</th>
        <th style="width: 10%" data-l10n-id="operations"></th>
      </tr>
      <tr class="template">
        <td>{{id}}</td>
        <td>{{mac}}</td>
        <td>{{sn}}</td>
        <td>{{_createdAt}}</td>
        <td>{{hardware}}/{{firmware}}</td>
        <td>{{timezone}}</td>
        <td>
          <a href="#/user/users?username={{relation.userId}}"
            >{{relation.userId}}</a
          >
        </td>
        <td>{{petContainer}}</td>
        <td><a href="#/user/family?groupId={{familyId}}">{{familyId}}</a></td>
        <td>{{_pim}}</td>
        <td class="operations">
          <div class="dropdown">
            <a
              class="dropdown-toggle"
              data-toggle="dropdown"
              href="javascript:;"
            >
              <span data-l10n-id="operations"></span>
              <b class="caret"></b>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
              <li>
                <a href="javascript:restartFeeder('{{id}}')">重启设备</a>
              </li>
              <li>
                <a href="javascript:fixFeederError('{{id}}')">修复门故障</a>
              </li>
              <li>
                <a href="javascript:setFeederLogLevel('{{id}}')">设置log等级</a>
              </li>
              <li>
                <a href="javascript:cu('{{id}}')">设备详情</a>
              </li>
              <li>
                <a
                  href="javascript:;"
                  data-op-type="show-secret"
                  data-op-key="{{id}}"
                  >显示密钥</a
                >
              </li>
              <li data-op-display="link{{_hasOwner}}">
                <a href="javascript:;" data-op-type="link" data-op-key="{{id}}"
                  >绑定</a
                >
              </li>
              <li data-op-display="unlink{{_hasOwner}}">
                <a
                  href="javascript:;"
                  data-op-type="unlink"
                  data-op-key="{{id}}"
                  >解绑</a
                >
              </li>
              <li>
                <a href="#/feeder/linkhistory?id={{id}}">绑定历史</a>
              </li>
              <li>
                <a href="#/feeder/logs?deviceId={{id}}">Log</a>
              </li>
            </ul>
          </div>
        </td>
      </tr>
      <td class="petTemplate" hidden>
        <div>
          <a href="#/pet/pets?id={{petId}}">{{petId}}</a>
        </div>
      </td>
    </table>
  </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>
