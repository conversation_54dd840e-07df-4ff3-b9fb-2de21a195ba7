<style type="text/css">
#chart-wrap{
	margin: 0 20px;
}
#chart1{
	width: 100%;
	height: 600px;
}
</style>
<script src="vendor/highchart/highstock.min.js"></script>
<script src="vendor/highchart/exporting.min.js"></script>
<script>

App.ready(function(){
	var form = $('#search-form');
	var template = $('#detail-container').getCommentTemplate();
	var requestParams = App.router.getParameters();
	
	var clearResult = function() {
		$('#chart1').empty();
		$('#detail-container').empty();
	};
	
	var buildResult = function(result) {
		form.find('[name="timezone"]').val(result['timezone']);
		form.find('[name="day"]').val(result['day']);
		var dailydata = result.data;
		if (!dailydata) {
			$('#chart1').html('<h4 style="margin-top:40px;">没有数据</h4>');
			return;
		}
		
		var data = dailydata.data;
		var details = dailydata.detail;
		var categories=[];
		for(var i=1;i<=1440;i++){
			categories.push(i);
		}
		 var frequency = 86400/data.length;
		 var deltaT = frequency*1000;
		 for(var i=0;i<data.length;i++){
			 var v = data[i];
			 data[i]= [result.time+deltaT*i, (v>=0)?v:null];
		 }
	     $('#chart1').highcharts('StockChart',{
				rangeSelector : {
					selected : 1
				},
	            title: {
	                text: '报表',
	            },
	            xAxis: {
	                categories: categories,
	                title: { 
	                    text: '时间'
	                }
	            },
	            yAxis: {
	            	gridLineColor: '#DEDEDE',
	            	gridLineWidth: 1,
	            	gridLineDashStyle: 'longdash',
	            	min:0, 
	                title: {
	                    text: '活动量'
	                }
	            },
	            series: [{
	                name: '活动量',
	                data: data
	            }]
	        });
	     
	     if (details) {
    		 var container = $('#detail-container');
	    	 for (var key in details) {
	    		 container.append(U.template(template, {'key': key, 'value': details[key]}));
	    	 }
	     }
	};
	
	var request = function() {
		clearResult();
		if (!requestParams.petId) {
			return;
		}
		App.api('/fit/data', requestParams, {
			success: buildResult
		});
	};

    var submitIt = function () {
        var data = form.serializeObject();
        App.router.go(App.router.getCurrentPath(), data);
    };
	
	var requestDay = function(days) {
		var params = form.serializeObject();
		var day = params.day;
		var year = day / 10000;
		var month = (day % 10000) / 100 - 1;
		var date = day % 100;
		var currentDate = new Date(year, month, date, 0, 0, 0, 0);
		var newDate = new Date(currentDate.getTime() + days * (24 * 3600 * 1000));
		var newDay =  newDate.getFullYear() * 10000 + (newDate.getMonth() + 1)
	      * 100 + newDate.getDate();
		$('#search-form').find('[name="day"]').val(newDay);
		submitIt();
	};
    
	var init = function() {
		$('#submit-btn').click(submitIt);
		$('#submit-pre-btn').click(function(){
			requestDay(-1);
		});
		$('#submit-next-btn').click(function(){
			requestDay(1);
		});
		$('#history-btn').click(function(){
			var petId = form.find('[name=petId]').val();
			if (!petId) {
				alert('请输入宠物ID');
				return;
			}
			App.router.go('/fit/uploadhistory', {'petId': petId});
		});
		$('#recover-btn').click(function(){
			var petId = form.find('[name=petId]').val();
			if (!petId) {
				alert('请输入宠物ID');
				return;
			}
			App.router.go('/fit/recoverdata', {'petId': petId});
		});
		request();
	};
	
	init();
});
</script>
<form id="search-form" class="form-inline" onsubmit="return false;">
	<input type="hidden" name="version" ng-model="REQUEST.version"/>
	<input type="text" class="form-control" name="petId" ng-model="REQUEST.petId" placeholder="宠物ID"/>
	<input type="text" class="form-control" name="day" ng-model="REQUEST.day" placeholder="日期,格式如 20140121"/>
	<input type="text" class="form-control" name="timezone" ng-model="REQUEST.timezone" placeholder="时区,-12~12"/>
	<a href="javascript:;" id="submit-btn" class="btn btn-primary">查询</a>
	<a href="javascript:;" id="submit-pre-btn" class="btn btn-success">前一天</a>
	<a href="javascript:;" id="submit-next-btn" class="btn btn-info">后一天</a>
	<a href="javascript:;" id="history-btn" class="btn btn-warning">数据同步历史</a>
	<a href="javascript:;" id="recover-btn" class="btn btn-danger">恢复数据</a>
</form>
<div id="chart-wrap">
	<div id="chart1"></div>
	<div id="detail-container">
		<!-- <label>{{key}}</label>：<span>{{value}}</span><br/> -->
	</div>
</div>