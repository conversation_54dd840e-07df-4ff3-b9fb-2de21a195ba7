<script>
	App.ready(function() {
		App.menu.select('/fit/data');
		var requestParams = $.extend({'limit': 10}, App.router.getParameters());
		var petId = App.router.getParameter('petId');
		var request = function(offset) {
			App.scrollToTop();
			if (!U.isNull(offset)) {
				requestParams.offset = offset;
			}
			App.api('/fit/uploadhistory', requestParams).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
			});
		};
		request();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" onsubmit="return false;">
			活动数据上传历史
			<input type="text" class="form-control" ng-model="REQUEST.petId" readonly="readonly"/>
			<a href="javascript:window.history.back();" class="btn btn-primary" data-l10n-id="back"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 30%">电量</th>
				<th style="width: 30%">电压</th>
				<th style="width: 40%">上传时间</th>
			</tr>
			<tr class="template">
				<td>{{battery}}</td>
				<td>{{voltage}}</td>
				<td>{{_createdAt}}</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>