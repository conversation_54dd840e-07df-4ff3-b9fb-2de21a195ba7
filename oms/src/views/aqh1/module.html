<script>
  App.ready(function(){
    var requestParams = $.extend({'limit': 10}, App.router.getParameters());
    var request = function(offset) {
      App.scrollToTop();
      if (!U.isNull(offset)) {
        requestParams.offset = offset;
      }
      // App.api('/center/aqh1/modules', requestParams).then(function(ps) {
      App.api('/aqh1/modules', requestParams).then(function(ps) {
        var list = ps || [];
        console.log(list)
        var table = $('#the-table');
        var template = table.attr('data-template');
        if (!template) {
          template = '<tr>'
                  + table.find('tr.template')
                          .html() + '</tr>';
          table.attr('data-template', template);
        }
        table.find('tr:not(.header)').remove();
        for ( var i = 0; i < list.length; i++) {
          var context = list[i];
          context.result = JSON.stringify(list[i]).replaceAll("\"", "$");
          var row = U.template(template, context);
          table.append(row);
        }
        initOperations()
        App.pagination(ps, request);
      });
    };
    request();


    var initOperations = function () {
      //删除功能
      $('[data-op-type=delete]').click(function () {
        deleteModule($(this).attr('data-op-key'));
      });

      //修改弹出模态框
      $('[data-op-type=update]').click(function () {
        var result = JSON.parse($(this).attr('data-op-key').replaceAll("$", "\""))
        $("#id").val(result.id)
        $("#pirority").val(result.priority)
        $("#minVersion").val(result.minVersion)
        $("#logLevel").val(result.logLevel)
        $("#upgradeTime").val(result.upgradeTime)
        $("#editModal").modal('show')
      });

    };

    //弹出模态框
    $("#create").click(function(){
      $("#createModal").modal('show')
    })

    //创建模块
    $("#moduleCreate").click(function (){
      moduleCreate();
    })

    //修改模块
    $("#moduleUpdate").click(function (){
      moduleUpdate();
    })

    var deleteModule = function (id) {
      if (!confirm(App.text('确定要删除该模块吗？'))) {
        return;
      }
      // return App.api('/center/aqh1/module_remove', {'id': id}).done(function () {
      return App.api('/aqh1/module_remove', {'id': id}).done(function () {
        alert('删除成功');
        request();
      });
    };

    var moduleCreate = function(){
      // return App.api('/center/aqh1/module_save', {'id': $("#cid").val(), 'priority': $("#cpirority").val(),
      return App.api('/aqh1/module_save', {'id': $("#cid").val(), 'priority': $("#cpirority").val(),
        'minVersion':$("#cminVersion").val(),'logLevel': $("#clogLevel").val(),'upgradeTime': $("#cupgradeTime").val()}).done(function() {
        alert('创建成功');
        $("#createModal").modal('hide')
        $("#cid").val('')
        $("#cpirority").val('')
        $("#cminVersion").val('')
        $("#clogLevel").val('')
        $("#cupgradeTime").val('')
        request();
      });
    }

    var moduleUpdate = function(){
      // return App.api('/center/aqh1/module_save', {'id': $("#id").val(), 'priority': $("#pirority").val(),
      return App.api('/aqh1/module_save', {'id': $("#id").val(), 'priority': $("#pirority").val(),
        'minVersion':$("#minVersion").val(),'logLevel': $("#logLevel").val(),'upgradeTime': $("#upgradeTime").val()}).done(function() {
        alert('修改成功');
        $("#editModal").modal('hide')
        $("#id").val('')
        $("#pirority").val('')
        $("#minVersion").val('')
        $("#logLevel").val('')
        $("#upgradeTime").val('')
        request();
      });
    }


  });
</script>
<div class="panel panel-default x-panel">
  <div class="panel-heading">
    <a href="javascript:;" id="create" class="btn btn-danger" data-l10n-id="create"></a>
  </div>
  <div class="panel-body">
    <div class="table-responsive">
      <table class="table table-hover x-table" id="the-table">
        <tr class="header">
          <th style="width: 20%">模块</th>
          <th style="width: 15%">排序</th>
          <th style="width: 20%">最小版本</th>
          <th style="width: 15%">Log等级</th>
          <th style="width: 20%">升级时间(秒)</th>
          <th style="width: 10%" data-l10n-id="operations">操作</th>
        </tr>
        <tr class="template">
          <td>{{id}}</td>
          <td>{{priority}}</td>
          <td>{{minVersion}}</td>
          <td>{{logLevel}}</td>
          <td>{{upgradeTime}}</td>
          <td class="operations">
            <div class="dropdown">
              <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;"> <span
                      data-l10n-id="operations"></span>
                <b class="caret"></b>
              </a>
              <ul class="dropdown-menu dropdown-menu-right">
                <li><a href="javascript:;" data-op-type="update" data-op-key="{{result}}">修改</a></li>
                <li><a href="javascript:;" data-op-type="delete" data-op-key="{{id}}">删除</a></li>
              </ul>
            </div>
          </td>
        </tr>
      </table>
    </div>
  </div>
</div>
<div id="pagination-wrap"></div>

<div class="modal fade" tabindex="-1" role="dialog" style="display: none" id="editModal">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">修改</h4>
      </div>
      <div class="modal-body">
        <form>
          <div class="form-group">
            <label for="id">模块</label>
            <input type="text" class="form-control" id="id" placeholder="模块">
          </div>
          <div class="form-group">
            <label for="pirority">排序</label>
            <input type="text" class="form-control" id="pirority" placeholder="排序">
          </div>

          <div class="form-group">
            <label for="minVersion">最小版本</label>
            <input type="text" class="form-control" id="minVersion" placeholder="最小版本">
          </div>
          <div class="form-group">
            <label for="logLevel">Log等级</label>
            <input type="text" class="form-control" id="logLevel" placeholder="Log等级">
          </div>
          <div class="form-group">
            <label for="upgradeTime">升级时间(秒)</label>
            <input type="text" class="form-control" id="upgradeTime" placeholder="">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-danger" id="moduleUpdate">修改</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>

<div class="modal fade" tabindex="-1" role="dialog" style="display: none" id="createModal">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">创建</h4>
      </div>
      <div class="modal-body">
        <form>
          <div class="form-group">
            <label for="cid">模块</label>
            <input type="text" class="form-control" id="cid" placeholder="模块">
          </div>
          <div class="form-group">
            <label for="cpirority">排序</label>
            <input type="text" class="form-control" id="cpirority" placeholder="排序">
          </div>

          <div class="form-group">
            <label for="cminVersion">最小版本</label>
            <input type="text" class="form-control" id="cminVersion" placeholder="最小版本">
          </div>
          <div class="form-group">
            <label for="clogLevel">Log等级</label>
            <input type="text" class="form-control" id="clogLevel" placeholder="Log等级">
          </div>
          <div class="form-group">
            <label for="cupgradeTime">升级时间(秒)</label>
            <input type="text" class="form-control" id="cupgradeTime" placeholder="">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-danger" id="moduleCreate">创建</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>