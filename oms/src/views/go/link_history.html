<script>
App.ready(function(){
	var requestParams = $.extend({'limit': 10}, App.router.getParameters());
	var request = function(offset) {
		App.menu.select('/go/devices');
		App.scrollToTop();
		if (!U.isNull(offset)) {
			requestParams.offset = offset;
		}
		App.api('/go/link_history', requestParams).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for ( var i = 0; i < list.length; i++) {
					var context = list[i];
					context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
		});
	};
	request();
});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<a href="javascript:window.history.back();" class="btn btn-success" data-l10n-id="back"></a>
	</div>
	<div class="panel-body">
		<div class="table-responsive">
			<table class="table table-hover x-table" id="the-table">
				<tr class="header">
					<th style="width: 35%">设备ID</th>
					<th style="width: 35%">用户ID</th>
					<th style="width: 30%">绑定时间</th>
				</tr>
				<tr class="template">
					<td>{{deviceId}}</td>
					<td><a href="#/user/users?username={{userId}}">{{userId}}</a></td>
					<td>{{_createdAt}}</td>
				</tr>
			</table>
		</div>
	</div>
</div>
<div id="pagination-wrap"></div>