<script>
App.ready(function() {
	var requestParams = $.extend({'limit': 10}, App.router.getParameters());
	var form = $('#search-form');
	var request = function(offset) {
		App.scrollToTop();
		if (!U.isNull(offset)) {
			requestParams.offset = offset;
		}
		App.api('/w5/sn_list', requestParams).then(
			function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table
						.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template',
							template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					var row = U.template(template, context);
					table.append(row);
				}
				initOperations();
				App.pagination(ps, request);
		});
	};
	var initOperations = function() {
    	$('[data-op-type="remove"]').click(function(){
    		remove($(this).attr('data-op-key'), 1);
    	});
	};
    var remove = function(sn) {
 	   	if (!confirm(App.text('confirm.delete'))) {
            return;
        }
        App.api('/w5/sn_remove', {'sn': sn}).then(function(){
        	submitIt();
        });
    };
    var submitIt = function () {
        var data = form.serializeObject();
        App.router.go(App.router.getCurrentPath(), data);
    };
    var add = function () {
		App.openDialog({
			url : App.router.getRealPath('/w5/sn_save.html'),
			noHeader : true,
			primaryClick : save
		});
    };
    var save = function() {
		var data = $('#dialog-form').serializeObject();
		App.api('/w5/sn_save', data).then(function() {
			alert('添加成功');
			App.closeDialog();
			form.find('[name=type]').val('sn');
			form.find('[name=s]').val(data.sn);
			submitIt();
		});
    };
	var init = function() {
        $('#add-btn').click(add);
        $('#search-btn').click(submitIt);
        $("#search-key").keydown(function (e) {
            if (e.keyCode == 13) {
                submitIt();
            }
        });
		request();
	};
	init();
});
</script>

<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
			<select class="form-control" name="type" ng-model="REQUEST.type">
				<option value="sn">SN</option>
				<option value="mac">Mac</option>
			</select> 
			<input id="search-key" type="text" class="form-control" name="s" ng-model="REQUEST.s" /> 
			<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
			<a href="javascript:;" id="add-btn" class="btn btn-info" data-l10n-id="add"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 30%">SN</th>
				<th style="width: 30%">MAC</th>
				<th style="width: 30%">CHIPID</th>
				<th style="width: 10%" data-l10n-id="operations"></th>
			</tr>
			<tr class="template">
				<td>{{sn}}</td>
				<td>{{mac}}</td>
				<td>{{chipId}}</td>
				<td class="operations">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;"> <span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li><a href="javascript:;" data-op-type="remove" data-op-key="{{sn}}" data-l10n-id="delete"></a></li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>