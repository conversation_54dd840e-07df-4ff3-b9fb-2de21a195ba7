<link
  rel="stylesheet"
  href="vendor/ztree/metroStyle/metroStyle.css"
  type="text/css"
/>
<script
  type="text/javascript"
  src="vendor/ztree/jquery.ztree.core-3.5.min.js"
></script>
<script
  type="text/javascript"
  src="vendor/ztree/jquery.ztree.excheck-3.5.min.js"
></script>
<script>
  App.ready(function () {
    var form = $("#form1");
    var _model = null;
    var loadModel = function () {
      App.menu.select("/admin/groups");
      var id = App.router.getParameter("id");
      if (!id) {
        return $.when(null);
      }
      return App.api("/admin/group", { id: id }).then(function (model) {
        _model = model;
        form.renderModel(model);
        var tree = $.fn.zTree.getZTreeObj("tree");
        if (model.root) {
          $("#tree").hide();
          $("#pLabel").show();
        }
        if (!model.permissions) {
          return;
        }
        var ps = model.permissions.split("|");
        if (!ps) {
          return;
        }
        var nodes = tree.getNodes();
        nodes = tree.transformToArray(nodes);
        for (var i = 0; i < ps.length; i++) {
          for (var j = 0; j < nodes.length; j++) {
            if (nodes[j].value == ps[i]) {
              tree.checkNode(nodes[j], true, true);
            }
          }
        }
      });
    };
    var initTree = function () {
      var setting = {
        check: {
          chkboxType: { Y: "ps", N: "ps" },
          enable: true,
        },
        data: {
          simpleData: {
            enable: true,
          },
        },
      };
      return App.api("/admin/permissions").then(function (result) {
        var permissions = result;
        var nodes = [];
        //var arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm'];
        var arr = [];
        for (var i = 0; i < permissions.length; i++) {
          arr[i] = "a" + i;
        }
        for (var i = 0; i < permissions.length; i++) {
          var node = {};
          node.id = arr[i];
          node.pId = 0;
          var href = permissions[i].key;
          node.value = href;
          node.name = permissions[i].name;
          console.log("1", node);
          nodes.push(node);
          if (permissions[i].permissions) {
            for (var j = 0; j < permissions[i].permissions.length; j++) {
              var child = permissions[i].permissions[j];
              var cNode = {};
              cNode.id = node.id + "" + (j + 1);
              cNode.pId = node.id;
              var cHref = href + "/" + child.key;
              cNode.value = cHref;
              if (child.key.indexOf(".") >= 0) {
                cNode.value = child.key;
              }
              cNode.name = child.name;
              nodes.push(cNode);
              if (child.permissions) {
                for (var k = 0; k < child.permissions.length; k++) {
                  var cChild = child.permissions[k];
                  var ccNode = {};
                  ccNode.pId = cNode.id;
                  ccNode.value = cNode.value + "/" + cChild.key;
                  ccNode.name = cChild.name;
                  console.log("2", ccNode);
                  nodes.push(ccNode);
                }
              }
            }
          }
        }
        $.fn.zTree.init($("#tree"), setting, nodes);
      });
    };
    var init = function () {
      initTree().then(loadModel);
      $("#btn-save").click(function () {
        var tree = $.fn.zTree.getZTreeObj("tree");
        var model = form.serializeObject();
        var permissions = null;
        if (model.root === "false") {
          var checkedNodes = tree.getCheckedNodes(true);
          permissions = "";
          for (var i = 0; i < checkedNodes.length; i++) {
            if (!checkedNodes[i].children) {
              if (permissions.length > 0) {
                permissions += "|";
              }
              permissions += checkedNodes[i].value;
            }
          }
        }
        model.permissions = permissions;
        App.api("/admin/save_group", model).then(function () {
          App.router.go("/admin/groups");
        });
      });
      $('[name="root"]').change(function () {
        var tree = $.fn.zTree.getZTreeObj("tree");
        if ($(this).val() === "true") {
          $("#tree").hide();
          $("#pLabel").show();
        } else {
          $("#tree").show();
          $("#pLabel").hide();
        }
      });
    };
    init();
  });
</script>
<div class="panel panel-default">
  <div class="panel-body">
    <form id="form1" role="form" onsubmit="return false;">
      <input type="hidden" name="id" ng-model="id" />
      <div class="form-group">
        <label data-l10n-id="name"></label>
        <input type="text" name="name" class="form-control" ng-model="name" />
      </div>
      <div class="form-group">
        <label data-l10n-id="type"></label>
        <select name="root" class="form-control" ng-model="root">
          <option value="false">管理员</option>
          <option value="true">超级管理员</option>
        </select>
      </div>
      <div class="form-group">
        <label>
          权限：<span id="pLabel" style="color: red; display: none"
            >[所有权限]</span
          >
        </label>
        <ul id="tree" class="ztree"></ul>
      </div>
      <div class="form-group">
        <a
          href="javascript:;"
          class="btn btn-primary"
          id="btn-save"
          data-l10n-id="save"
        ></a>
        <a
          href="javascript:window.history.back();"
          class="btn btn-info"
          data-l10n-id="cancel"
        ></a>
      </div>
    </form>
  </div>
</div>
