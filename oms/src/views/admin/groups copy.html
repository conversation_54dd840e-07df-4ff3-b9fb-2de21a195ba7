<script>
    App.ready(function () {
        var requestParams = $.extend({'limit': 20}, App.router.getParameters());
        var request = function (offset) {
            App.scrollToTop();
            if (!mars.isNull(offset)) {
            	requestParams.offset = offset;
            }
            App.api('/admin/groups', requestParams).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._permissions = context.root ? '超级管理员': context.permissions;
                    var row = U.template(template, context);
                    table.append(row);
                }
                initOperationButtons();
                App.pagination(ps, request);
            });
        };
        var update = function(id) {
            App.router.go('/admin/group?id=' + id);
        };
        var remove = function(id) {
       	   	  if (!confirm(App.text('confirm.delete'))) {
                  return;
              }
              App.api('/admin/remove_group', {'id': id}).then(function () {
                  window.location.reload();
              });
        };
        var initOperationButtons = function() {
        	$('[data-op-type="update"]').click(function(){
        		update($(this).attr('data-op-key'));
        	});
        	$('[data-op-type="remove"]').click(function(){
        		remove($(this).attr('data-op-key'), 1);
        	});
        };
        var init = function() {
        	$('#create-btn').click(function () {
                App.router.go('/admin/group');
            });
            request();
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
         <a href="javascript:;" id="create-btn" class="btn btn-success" data-l10n-id="add"></a>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 30%" data-l10n-id="name"></th>
                <th style="width: 60%">权限</th>
                <th style="width: 10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{name}}</td>
                <td>{{_permissions}}</td>
                <td class="ops">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a data-op-type="update" data-op-key="{{id}}" data-l10n-id="edit"></a>
                            </li>
                            <li>
                                <a data-op-type="remove" data-op-key="{{id}}" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>