<link rel="stylesheet" type="text/css" href="vendor/select2/select2.css" />
<script src="vendor/select2/select2.min.js"></script>
<script src="js/region-select.js"></script>
<script>
App.ready(function(){
	var form = $('#form1');
	var _model = null;
	var _groupMap = {};
    var initRegionSelect = function() {
    	var select = new RegionSelect('region-select');
	 	var options = (_model && _model.region) ? {'value': _model.region}: {};
	    return select.init(options);
    };
    var loadModel = function () {
        App.menu.select('/admin/admins');
        var id = App.router.getParameter('id');
        if (!id) {
        	return $.when(null);
        }
        return App.api('/admin/find', {'id': id}).then(function (model) {
        	_model = model;
        	form.renderModel(model);
        	var select = $('#group-selected');
        	var groups = model.groups || [];
        	for (var i = 0; i < groups.length; i++) {
        		var group = groups[i];
        		_groupMap[group.id] = group;
          		select.append('<option value="' + group.id + '">' + group.name + '</option>');
        	}
        });
    };
    var loadGroups = function() {
    	  return App.api('/admin/groups', {'list': 'true'}).then(function(groups) {
          		var select = $('#group-not-selected');
          		for (var i = 0; i < groups.length; i++) {
          			var group = groups[i];
          			if (!_groupMap[group.id]) {
              			select.append('<option value="' + group.id + '">' + group.name + '</option>');
              		}
          		}
          });
    };
    var save = function() {
    	var admin = form.serializeObject();
    	var groups = [];
		$("#group-selected option").each(function(index, item){
	    		groups.push({'id': item.value});
		});
    	if (groups.length < 1) {
    		alert('请选择组');
    		return;
    	}
    	admin.groups = groups;
    	App.api('/admin/save', admin).then(function() {
      		App.router.go('/admin/admins');
        });
    };
    var init = function() {
    	loadModel().then(loadGroups).then(initRegionSelect).then(function(){
	      	  $('#group-add-btn').click(function(){
	      		  $("#group-not-selected option:selected").each(function(index, item){
	              		var select = $('#group-selected');
	              		select.append(item);
	      		  });
	      	  });
	      	  $('#group-remove-btn').click(function(){
	      		  $("#group-selected option:selected").each(function(index, item){
	              		var select = $('#group-not-selected');
	              		select.append(item);
	      		  });
	      	  });
	      	  $('#btn-save').click(save);
    	});
    };
    init();
});
</script>
<div class="panel panel-default">
    <div class="panel-body">
        <form id="form1" role="form" onsubmit="return false;">
        	<input type="hidden" name="id" ng-model="id"/>
            <div class="form-group">
                <label data-l10n-id="name"></label>
                <input type="text" name="name" class="form-control" ng-model="name"/>
            </div>
            <div class="form-group">
                <label>Email</label>
                <input type="text" name="email" class="form-control" ng-model="email"/>
            </div>
            <div class="form-group">
                <label>组</label>
        		<div class="panel panel-default">
				<div class="panel-body">
						<!-- <div class="row" style="margin-bottom:10px;">
							<div class="col-md-4">
								<input type="text" class="form-control" id="group-name-input" placeholder="组名">
							</div>
							<div class="col-md-2">
								<a href="javascript:;" class="btn btn-primary" id="group-search-btn" data-l10n-id="search"></a>
							</div>
						</div> -->
						<div class="row">
							<div class="col-md-5">
								<label>组：</label>
								<select multiple class="form-control" id="group-not-selected" style="height:200px;">
								</select>
							</div>
							<div class="col-md-2" style="text-align:center;">
								<div>
									<a href="javascript:;" id="group-add-btn" class="btn btn-success" data-l10n-id="add" 
										style="margin-top:60px;margin-bottom:50px;"></a>
									<div class="clear"></div>
									<a href="javascript:;" id="group-remove-btn" class="btn btn-warning" data-l10n-id="delete"></a>
								</div>
							</div>
							<div class="col-md-5">
								<label>已添加组：</label>
								<select multiple class="form-control" id="group-selected" name="groups" style="height:200px;">
								</select>
								<!-- <div class="panel panel-default" style="height:200px;overflow-y:scroll;padding:10px 10px;"></div> -->
							</div>
						</div>
					</div>
				</div>
            </div>
            <div class="form-group">
                <label>Region</label>
        		<select name="region" class="form-control" id="region-select-s1"></select>
            </div>
            <div class="form-group">
                <a href="javascript:;" class="btn btn-primary" id="btn-save" data-l10n-id="save"></a>
                <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
            </div>
        </form>
    </div>
</div>