<script>
	App.ready(function() {
		var form = $('#form1');
		var save = function() {
			/* var oldPassword = form.find('[name=oldPassword]').val();
			if (!oldPassword) {
				form.find('[name=oldPassword]').focus();
				return;
			} */
			var password = form.find('[name=password]').val();
			if (!password) {
				form.find('[name=password]').focus();
				return;
			}
			var retypeControl = $('#password-retype');
			var password2 = retypeControl.val();
			if (!password2 || password2 != password) {
				retypeControl.val('');
				retypeControl.focus();
				return;
			}
		    return App.api('/admin/update_password', {'oldPassword': '', 'password': password}).then(function (model) {
			    alert(App.text('done'));
			    App.router.go('/welcome');
		    });
		};
		$('#btn-save').click(save);
	});
</script>
<div class="panel panel-default">
    <div class="panel-body">
        <form id="form1" role="form" onsubmit="return false;">
        	<input type="hidden" name="id" ng-model="id"/>
            <!-- <div class="form-group">
                <label data-l10n-id="password.old"></label>
                <input type="password" name="oldPassword" class="form-control"/>
            </div> -->
            <div class="form-group">
                <label data-l10n-id="password.new"></label>
                <input type="password" name="password" class="form-control"/>
            </div>
            <div class="form-group">
                <label data-l10n-id="password.new.retype"></label>
                <input type="password" id="password-retype" class="form-control"/>
            </div>
            <div class="form-group">
                <a href="javascript:;" class="btn btn-primary" id="btn-save" data-l10n-id="save"></a>
            </div>
        </form>
    </div>
</div>