<script>
	App.ready(function() {
		var form = $('#form1');
		var select = form.find('[name="locale"]');
        var currentLocale = $.cookie('locale');
		var save = function() {
			var locale = select.val();
			$.cookie('locale', locale, {
				path : __CONFIG__.appPath
			});
			mars.l10n.setLocale(locale);
			alert(App.text('done'));
			App.router.go('/welcome');
		};
		var init = function() {
			App.api('/app/locales').then(function (locales) {
	              $.each(locales, function (index, item) {
	                  select.append('<option value="' + item.key + '">' + item.name + '</option>');
	              });
	              if (!mars.isEmpty(currentLocale)) {
	            	  select.val(currentLocale);
	              }
	        });
			$('#btn-save').click(save);
		};
		init();
	});
</script>
<div class="panel panel-default">
    <div class="panel-body">
        <form id="form1" role="form" onsubmit="return false;">
            <div class="form-group">
                <label data-l10n-id="locale"></label>
                <select name="locale" class="form-control"></select>
            </div>
            <div class="form-group">
                <a href="javascript:;" class="btn btn-primary" id="btn-save" data-l10n-id="save"></a>
            </div>
        </form>
    </div>
</div>