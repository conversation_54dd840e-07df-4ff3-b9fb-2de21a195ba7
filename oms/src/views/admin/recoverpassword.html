<script>
	App.ready(function() {
		var form = $('#form1');
		var recover = function() {
			var username = form.find('[name=username]').val();
			if (!username) {
				form.find('[name=username]').focus();
				return;
			}
		    return App.api('/admin/recover_password', {'username': username}).then(function(model) {
			    alert('A password recovery email has been sent to your email: ' + username);
			    App.router.go('/welcome');
		    });
		};
		form.find('[name=username]').val($.cookie('username'));
		$('#btn-recover').click(recover);
	});
</script>
<div class="panel panel-default">
    <div class="panel-body">
        <form id="form1" role="form" onsubmit="return false;">
            <div class="form-group">
                <label data-l10n-id="username"></label>
                <input type="text" name="username" class="form-control"/>
            </div>
            <div class="form-group">
                <a href="javascript:;" class="btn btn-primary" id="btn-recover" data-l10n-id="password.recover"></a>
                <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
            </div>
        </form>
    </div>
</div>