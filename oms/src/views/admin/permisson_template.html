<script>
	var typeName = function(t) {
		if (t == 1) {
			return '超级管理员';
		} else if (t == 2) {
			return '管理员';
		} else {
			return '';
		}
	};
    var request = function (offset) {
        App.scrollToTop();
        var params = $.extend({'limit': 10, 'offset': offset}, App.router.getParameters());
        App.api('/admin/admins', params).then(function (ps) {
            var list = ps.items || [];
            var table = $('#the-table');
            var template = table.attr('data-template');
            if (!template) {
                template = '<tr>'
                        + table.find('tr.template')
                                .html() + '</tr>';
                table.attr('data-template', template);
            }
            table.find('tr:not(.header)').remove();
            for (var i = 0; i < list.length; i++) {
                var context = list[i];
                context._type = typeName(context.type);
                context._remark = context.remark;
                context._permission = context.permissions;
                var row = U.template(template, context);
                table.append(row);
            }
            App.pagination(ps, request);
        });
    };
    request();
</script>
<div class="panel panel-default x-panel">
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 15%"></th>
                <th style="width: 20%">昵称</th>
                <th style="width: 15%">类型</th>
                <th style="width: 15%">备注</th>
            </tr>
            <tr class="template">
                <td><input type="checkbox" value="{{_permission}}"></td>
                <td>{{user.nick}}</td>
                <td>{{_type}}</td>
                <td>{{_remark}}</td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>