<script>
    App.ready(function () {
        var requestParams = $.extend({'limit': 20}, App.router.getParameters());
        var request = function (offset) {
            App.scrollToTop();
            if (!mars.isNull(offset)) {
            	requestParams.offset = offset;
            }
            App.api('/admin/admins', requestParams).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    var user = context.user;
                    if (user && !user.nick) {
                    	user.nick = 'u' + user.id;
                    }
                    var groups = '';
                    if (context.groups) {
                    	for (var k=0; k<context.groups.length;k++) {
                    		var group = context.groups[k];
                    		if (group.name) {
                    			if (groups.length > 0) {
                    				groups += ', ';
                    			}
                    			groups += group.name;
                    		}
                    	}
                    }
                    context._groups = groups;
                    var row = U.template(template, context);
                    table.append(row);
                }
                initOperationButtons();
                App.pagination(ps, request);
            });
        };
        var remove = function(id) {
     	   	if (!confirm(App.text('confirm.delete'))) {
                return;
            }
            App.api('/admin/remove', {'id': id}).then(function(){
               request(0);
            });
        };
        var resetPassword = function(id) {
	   	   	  if (!confirm(App.text('confirm.operation'))) {
	              return;
	          }
	          App.api('/admin/reset_password', {'id': id}).then(function(){
	             alert('已给管理员发送密码重置邮件，请提醒TA收取邮件');
	          });
        };
        var bindUser = function(id) {
        	var username = prompt(App.text('p.user.please.input.username'), '');
            if (!username) {
                return;
            }
            return App.api('/user/info', {'username': username}).done(function(result) {
            	var user = result.user;
            	if (!confirm('确定绑定用户： ' + user.nick)) {
            		return;
            	}
            	App.api('/admin/binduser', {'id': id, 'userId': user.id}).then(function(){
  	               request();
  	            });
      	    });
        };
        var unbindUser = function(id) {
	   	   	  if (!confirm(App.text('confirm.operation'))) {
	              return;
	          }
	          App.api('/admin/binduser', {'id': id}).then(function(){
	               request();
	          });
        };
        var initOperationButtons = function() {
        	$('[data-op-type="reset-password"]').click(function(){
        		resetPassword($(this).attr('data-op-key'));
        	});
        	$('[data-op-type="bind-user"]').click(function(){
        		bindUser($(this).attr('data-op-key'));
        	});
        	$('[data-op-type="unbind-user"]').click(function(){
        		unbindUser($(this).attr('data-op-key'));
        	});
        	$('[data-op-type="remove"]').click(function(){
        		remove($(this).attr('data-op-key'), 1);
        	});
        };
        var init = function() {
            request();
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
            <!-- <input id="search-key" type="text" class="form-control" name="s" placehoder="user id" ng-model="REQUEST.s"/>
            <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a> -->
            <a href="#/admin/admin" id="add-btn" class="btn btn-success" data-l10n-id="add"></a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 20%" data-l10n-id="name"></th>
                <th style="width: 20%">Email</th>
                <th style="width: 20%">所在组</th>
                <th style="width: 20%">绑定用户</th>
                <th style="width: 10%">Region</th>
                <th style="width: 10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{name}}</td>
                <td>{{email}}</td>
                <td>{{_groups}}</td>
                <td><a href="#/user/users?username={{user.id}}">{{user.nick}}</a></td>
                <td>{{region}}</td>
                <td class="ops">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="#/admin/admin?id={{id}}" data-l10n-id="edit"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="bind-user" data-op-key="{{id}}">绑定用户</a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="unbind-user" data-op-key="{{id}}">解绑用户</a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="reset-password" data-op-key="{{id}}">重置密码</a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="remove" data-op-key="{{id}}" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>