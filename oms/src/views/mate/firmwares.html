<script>
	App.ready(function() {
		var postOptions = {};
		var request = function(offset) {
			App.scrollToTop();
			var params = $.extend({
				'offset' : offset,
				'limit' : 20
			}, App.router.getParameters());
			App.api('/mate/firmwares', params, postOptions).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
			});
		};
		var remove = function(id) {
			var got = prompt(App.text('prompt.input', ['delete']), '');
			if (!got || got!='delete') {
				return;
			}
			App.api('/mate/removefirmware', {
				'id' : id
			}, postOptions).then(function() {
				window.location.reload();
			});
		};
		var init = function() {
			$('#search-btn').click(function() {
				App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
			});
			window.remove = remove;
			request();
		};
		init();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
			硬件：
			<input type="text" name="hardware" class="form-control" ng-model="REQUEST.hardware" />
			模块：
			<input type="text" name="module" class="form-control" ng-model="REQUEST.module" />
			<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
			<a href="#/mate/firmware" class="btn btn-info" data-l10n-id="add"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 10%">硬件</th>
				<th style="width: 10%">固件版本</th>
				<th style="width: 20%">模块</th>
				<th style="width: 20%">MD5</th>
				<th style="width: 10%">下载包</th>
				<th style="width: 20%">备注</th>
				<th style="width: 10%" data-l10n-id="operations"></th>
			</tr>
			<tr class="template">
				<td>{{hardware}}</td>
				<td>{{version}}</td>
				<td>{{module}}</td>
				<td>{{file.digest}}</td>
				<td><a href="{{file.url}}" target="_blank">{{file.size}} 字节</a></td>
				<td>{{notes}}</td>
				<td class="operations">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li>
								<a href="#/mate/firmware?id={{id}}" data-l10n-id="edit"></a>
							</li>
							<li>
								<a href="javascript:remove('{{id}}')" data-l10n-id="delete"></a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>