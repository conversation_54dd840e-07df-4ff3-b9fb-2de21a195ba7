<script>
App.ready(function(){
	App.menu.select('/mate/devices');
	var deviceId = App.router.getParameter('deviceId');
	var isCallee = App.router.getParameter('isCallee');
	var request = function(offset) {
		App.scrollToTop();
		var params = $.extend(App.router.getParameters(), {'offset' : U.isNull(offset) ? 0 : offset});
		App.api('/mate/callrecords', params).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for ( var i = 0; i < list.length; i++) {
					var context = list[i];
					context.callDateTime = U.date.format(U.date.parse(context.callDateTime), 'MM-dd HH:mm:ss');
					context.callState = context.callState ? '成功' : '失败';
					context.during = parseInt(context.during/1000) + 's';
					context._isCallee = isCallee;
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
		});
	};
	$('#back-btn').click(function(){
		window.history.back();
	});
	request();
});
</script>
<div class="panel panel-default x-panel">
<div class="panel-heading">
	<a href="javascript:;" id="back-btn" class="btn btn-primary" data-l10n-id="back"></a>
	<span>设备通话历史</span>
</div>
<div class="panel-body"><div class="table-responsive">
<table class="table table-hover x-table" id="the-table">
	<tr class="header">
		<th style="width:15%">时间</th>
		<th style="width:15%">主叫</th>
		<th style="width:10%">通话状态</th>
		<th style="width:15%">通话时间</th>
		<th style="width:15%">network/rtp</th>
		<th style="width:15%">natType</th>
		<th style="width:15%">详细</th>
	</tr>
	<tr class="template">
		<td>{{callDateTime}}</td>
		<td><a href="#/user/users?username={{callerId}}" target="_blank">{{callerId}}</a></td>
		<td>{{callState}}</td>
		<td>{{during}}</td>
		<td>{{network}}/{{audioRtpRelay}}/{{videoRtpRelay}}</td>
		<td>{{callerNatType}}/{{calleeNatType}}</td>
		<td><a href="#/mate/callrecord?id={{id}}&isCallee={{_isCallee}}">详细</a></td>
	</tr>
</table>
</div></div></div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>