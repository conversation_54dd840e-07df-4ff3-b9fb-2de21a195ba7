<script>
	App.ready(function() {
		var postOptions = {};
		var request = function() {
			App.scrollToTop();
			App.api('/mate/capgroups', null, postOptions).then(function(list) {
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					var _caps = '';
					if (context.capabilities) {
						for (var j = 0; j < context.capabilities.length; j++) {
							var cap = context.capabilities[j];
							if (j > 0) {
								_caps += '，';
							}
							_caps += cap.name;
						}
					}
					context._caps = _caps;
					var row = U.template(template, context);
					table.append(row);
				}
			});
		};
		var remove = function(id) {
			var got = prompt(App.text('prompt.input', [ 'delete' ]), '');
			if (!got || got != 'delete') {
				return;
			}
			App.api('/mate/removecapgroup', {
				'id' : id
			}, postOptions).then(function() {
				window.location.reload();
			});
		};
		var init = function() {
			window.remove = remove;
			request();
		};
		init();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
			<a href="#/mate/savecapgroup" class="btn btn-info" data-l10n-id="add"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 20%">SN</th>
				<th style="width: 20%">型号</th>
				<th style="width: 40%">功能列表</th>
				<th style="width: 20%" data-l10n-id="operations"></th>
			</tr>
			<tr class="template">
				<td>{{id}}</td>
				<td>{{style}}</td>
				<td>{{_caps}}</td>
				<td class="operations">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li>
								<a href="#/mate/savecapgroup?id={{id}}" data-l10n-id="edit"></a>
							</li>
							<li>
								<a href="javascript:remove('{{id}}')" data-l10n-id="delete"></a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>