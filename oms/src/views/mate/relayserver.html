<script>
App.ready(function(){
	var form = $('#save-form');
    var loadGroups = function() {
   	      return App.api('/mate/relayservergroups').then(function(groups) {
 	    	  var select = form.find('[name="group.name"]');
   	    	  $.each(groups, function(index, item) {
   	    		 select.append('<option value="' + item.name + '">'+ item.name +'</option>');
   	    	  });
          });
    };
    var loadModel = function () {
        App.menu.select('/mate/relayservers');
        var id = App.router.getParameter('id');
        if (!id) {
        	return $.when(null);
        }
        return App.api('/mate/relayserver', {'id': id}).then(function (model) {
        	form.find('[name=id]').attr('readonly', 'readonly');
        	form.renderModel(model);
        });
    };
    var save = function() {
    	var model = form.serializeObject();
    	App.api('/mate/saverelayserver', model).then(function() {
      		App.router.go('/mate/relayservers');
        });
    };
    var init = function() {
    	loadGroups().then(loadModel).then(function(){
	      	  $('#save-btn').click(save);
  		});
    };
    init();
});
</script>

<form id="save-form" role="form" onsubmit="return false;">
	<div class="form-group">
		<label>IP</label>
		<input type="text" class="form-control" name="id" ng-model="id"/>
	</div>
	<div class="form-group">
		<label>组</label>
      	<select name="group.name" class="form-control" ng-model="group.name"></select>
	</div>
	<div class="form-group">
		<label></label>
		<a href="javascript:;" id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
	</div>
</form>