<script>
	App.ready(function() {
		App.menu.select('/mate/capgroups');
		var form = $('#save-form');
		var postOptions = {};

		var _model = null;
		var loadModel = function() {
			var id = App.router.getParameter('id');
			if (!id) {
				return;
			}
			form.find('[name=id]').attr('readonly', 'readonly');
			var options = $.extend(postOptions, {
				success : function(model) {
					_model = model;
					if ($.isEmptyObject(model)) {
						return false;
					}
					$('#save-form').renderModel({
						model : model
					});
				}
			});
			return App.api('/mate/capgroup', {
				'id' : id
			}, options);
		};

		var initButtons = function() {
			$('#save-btn').click(
					function() {
						var data = form.serializeObject();
						var caps = data['caps'];
						delete data['caps'];
						if (caps) {
							if ($.type(caps) != 'array'){
								caps = [caps];
							}
							var capabilities = [];
							for (var i = 0; i<caps.length;i++){
								var cap = {'id':  caps[i]};
								capabilities.push(cap);
							}
							data['capabilities'] = capabilities;
						}
						App.api('/mate/savecapgroup', {'group': data}, postOptions).then(
								function() {
									App.router.go('/mate/capgroups');
								});
					});
		};

		var loadCaps = function() {
			var options = $.extend(postOptions, {
				success : function(caps) {
					var container = $('#cap-container');
					{
						for (var i = 0; i < caps.length; i++) {
							var cap = caps[i];
							var checkbox = '<input type="checkbox" name="caps" value="'+cap.id+'"/>' + '<span style="padding:0 10px 0 5px;">'+ cap.name + '</span>';
							container.append(checkbox);
						}
					}
	
					{
						var checkedCaps = _model ? _model.capabilities : null;
						if (checkedCaps) {
							for (var i = 0; i<checkedCaps.length;i++){
								container.find('[value='+checkedCaps[i].id+']').prop('checked', true);
							}
						}
					}
				}
			});
			return App.api('/mate/caps', null, options);
		};

		$.when(loadModel()).then(loadCaps).then(initButtons);
	});
</script>
<style>
#cover-preview img {
	width: 437px;
	height: 198px;
}
</style>
<form id="save-form" role="form" onsubmit="return false;">
	<div class="form-group">
		<label>SN：</label> <input type="text" name="id" class="form-control" ng-model="model.id" />
	</div>
	<div class="form-group">
		<label>型号：</label> <input type="text" name="style" class="form-control" ng-model="model.style" />
	</div>
	<div class="form-group">
		<label>功能：</label>
		<div id="cap-container"></div>
	</div>
	<div class="form-group">
		<label></label> <a id="save-btn" href="javascript:;" class="btn btn-primary">保存</a>
	</div>
</form>