
<script>
	App.ready(function() {
		App.menu.select('/mate/firmwarereleases');
		var firmwareId = App.router.getParameter('firmwareId');

		var request = function(offset) {
			var params = App.router.getParameters();
			params = $.extend({
				'offset' : U.isNull(offset) ? 0 : offset,
				'limit' : 20
			}, params);
			App.scrollToTop();
			App.api('/mate/firmwaredevices', params).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var deviceId = list[i];
					var context = {
						'deviceId' : deviceId
					};
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
			});
		};

		var save = function() {
			var data = $('#dialog-form').serializeObject();
			data['firmwareId'] = firmwareId;
			App.api('/mate/addfirmwaredevice', data).then(function() {
				window.location.reload();
			});
		};

		var add = function() {
			App.openDialog({
				url : App.router.getRealPath('/mate/addfirmwaredevice.html'),
				noHeader : true,
				primaryClick : save
			});
		};

		var remove = function(deviceId) {
			if (!confirm(App.text('confirm.operation'))) {
				return;
			}
			App.api('/mate/removefirmwaredevice', {
				'firmwareId' : firmwareId,
				'deviceId' : deviceId
			}).then(function() {
				window.location.reload();
			});
		};

		var loadFirmware = function() {
			var callback = function(firmware) {
				if ($.isEmptyObject(firmware)) {
					alert('The firmware does not exists or has been removed');
					return false;
				}
			};
			return App.api('/mate/firmwarerelease', {
				'id' : firmwareId
			}, {
				success : callback
			});
		};

		var init = function() {
			loadFirmware().then(function(firmware) {
				window.add = add;
				window.remove = remove;
				$('#back-btn').html('&lt;&lt;V' + firmware.version);
				request();
			});
		};

		init();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<a href="#/mate/firmwarereleases" class="btn btn-info" id="back-btn" data-l10n-id="back"></a>
		<a href="javascript:add()" class="btn btn-warning" data-l10n-id="add"></a>
		<div class="clear"></div>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 50%">设备ID</th>
				<th style="width: 50%" data-l10n-id="operations"></th>
			</tr>
			<tr class="template">
				<td>{{deviceId}}</td>
				<td class="operations"><a href="javascript:remove('{{deviceId}}')" data-l10n-id="delete"></a></td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>