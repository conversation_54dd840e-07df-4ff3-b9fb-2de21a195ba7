<script>
	App.ready(function() {
		App.menu.select('/mate/modules');
		var form = $('#save-form');
		var postOptions = {};

		var loadModel = function() {
			var id = App.router.getParameter('id');
			if (!id) {
				return;
			}
			var options = $.extend(postOptions, {
				success : function(model) {
					if ($.isEmptyObject(model)) {
						return;
					}
					$('#save-form').renderModel({
						model : model
					});
				}
			});
			return App.api('/mate/module', {
				'id' : id
			}, options);
		};

		var initButtons = function() {
			$('#save-btn').click(function() {
				var data = form.serializeObject();
				App.api('/mate/savemodule', data, postOptions).then(function() {
					App.router.go('/mate/modules');
				});
			});
		};

		$.when(loadModel()).then(initButtons);
	});
</script>
<style>
#cover-preview img {
	width: 437px;
	height: 198px;
}
</style>
<form id="save-form" role="form" onsubmit="return false;">
	<div class="form-group">
		<label>模块：</label> <input type="text" name="id" class="form-control" ng-model="model.id" />
	</div>
	<div class="form-group">
		<label>排序：</label> <input type="text" name="rank" class="form-control" ng-model="model.rank" />
	</div>
	<div class="form-group">
		<label>最低版本：</label> <input type="text" name="minVersion" class="form-control" ng-model="model.minVersion" />
	</div>
	<div class="form-group">
		<label>Log等级：</label> <input type="text" name="logLevel" class="form-control" ng-model="model.logLevel" />
	</div>
	<div class="form-group">
		<label>升级时间(秒)：</label> <input type="text" name="upgradeTime" class="form-control" ng-model="model.upgradeTime" />
	</div>
	<div class="form-group">
		<label></label> <a id="save-btn" href="javascript:;" class="btn btn-primary">保存</a>
	</div>
</form>