<link rel="stylesheet" type="text/css" href="vendor/select2/select2.css" />
<script src="vendor/select2/select2.min.js"></script>
<script src="js/region-select.js"></script>
<script>
    App.ready(function () {
        App.menu.select('/mate/configs');
        var _code = App.router.getParameter('code');
        var create = _code ? false : true;
        var configs = {};
        var select = null;
        var save = function (e) {
            var data = {};
            data.code = select.getValue();
            data.config = App.jsonPanel.val();
            if (!data.code) {
            	alert('no region');
                 return;
            }
            App.api('/mate/config_save', {
                model: JSON.stringify(data),
                create: create
            }).then(function (result) {
                App.router.go('/mate/configs');
            });
        };
        var getModel = function() {
        	if (_code) {
        		return App.api('/mate/config', {
                    code: _code
                }).then(function (result) {
            		select.setValue(_code);
                    App.jsonPanel.val(result.config);
                });
        	} else {
        		return $.when();
        	}
        };

        var init = function () {
            $('.btn-save').click(save);
            App.jsonPanel.init({
                context: $('#property')
            });
            select = new RegionSelect('regionselect');
            select.init().done(getModel);
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-body">
        <form id="save-form" class="form">
            <div class="form-group">
                <label>地区</label>
                 <select class="form-control" name="name" id="regionselect-s1"></select>
            </div>
            <div class="form-group">
                <label>参数</label>
                <div id="property"></div>
            </div>
            <a class="btn btn-primary btn-save" href="javascript:;">保存</a>
            <a class="btn btn-primary" href="javascript:window.history.back();">返回</a>
        </form>
    </div>
</div>