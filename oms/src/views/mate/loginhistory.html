<script>
App.ready(function(){
	App.menu.select('/mate/devices');

    var requestParams = $.extend({'limit': 20}, App.router.getParameters());
	var request = function(offset) {
		App.scrollToTop();
        if (!mars.isNull(offset)) {
         	requestParams.offset = offset;
        }
		App.api('/mate/pim_loginhistory', requestParams).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for ( var i = 0; i < list.length; i++) {
					var context = list[i];
					context._index = i + 1;
					context._t1 = U.date.format(U.date.parse(context.t1), 'yyyy-MM-dd HH:mm:ss');
					context._t2 = U.date.format(U.date.parse(context.t2), 'yyyy-MM-dd HH:mm:ss');
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
		});
	};
	var init = function() {
		request();
	};
	init();
});
</script>
<div class="panel panel-default x-panel">
<div class="panel-heading">
	<a href="javascript:window.history.back();" id="back-btn" class="btn btn-primary" data-l10n-id="back"></a>
</div>
<div class="panel-body"><div class="table-responsive">
<table class="table table-hover x-table" id="the-table">
	<tr class="header">
		<th style="width:20%">序号</th>
		<th style="width:30%">上线时间</th>
		<th style="width:30%">下线时间</th>
		<th style="width:20%">登录服务器</th>
	</tr>
	<tr class="template">
		<td>{{_index}}</td>
		<td>{{_t1}}</td>
		<td>{{_t2}}</td>
		<td>{{server}}</td>
	</tr>
</table>
</div></div></div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>