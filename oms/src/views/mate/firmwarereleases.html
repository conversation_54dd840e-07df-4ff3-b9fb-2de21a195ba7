<script>
	App.ready(function() {
		var request = function(offset) {
			App.scrollToTop();
			var params = $.extend({
				'offset' : offset,
				'limit' : 10
			}, App.router.getParameters());
			App.api('/mate/firmwarereleases', params).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					context._open = context.open ? '开放' : '封闭';
					context._releaseNotes = mars.l10n.getBy(context.releaseNotes);
					context._appAlert = mars.l10n.getBy(context.appAlert);
					var firmwares = context.firmwares;
					if (firmwares) {
						var s = '';
						for (var j = 0; j < firmwares.length; j++) {
							var f = firmwares[j];
							s += f.module + ': ' + f.version + '<br/>';
						}
						context._firmwares = s;
					}
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
			});
		};
		var remove = function(id) {
			var got = prompt(App.text('prompt.input', [ 'delete' ]), '');
			if (!got || got != 'delete') {
				return;
			}
			App.api('/mate/removefirmwarerelease', {
				'id' : id
			}).then(function() {
				window.location.reload();
			});
		};
		window.pushNotification = function(releaseId) {
			var deviceId = prompt('请输入设备ID', '');
			if (!deviceId) {
				return;
			}
			App.api('/mate/pushnotification', {
				'releaseId' : releaseId,
				'deviceId' : deviceId
			}).then(function() {
				alert('已发送');
			});
		};
		var init = function() {
			$('#search-btn').click(function() {
				App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
			});
			$('#create-btn').click(function() {
				var hardware = prompt('请输入硬件号');
				if (!hardware) {
					return;
				}
				App.router.go('/mate/firmwarerelease', {
					hardware : hardware
				});
			});
			window.remove = remove;
			request();
		};
		init();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
			硬件：
			<input type="text" name="hardware" class="form-control" ng-model="REQUEST.hardware" />
			<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
			<a href="javascript:;" class="btn btn-info" data-l10n-id="add" id="create-btn"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 5%">硬件</th>
				<th style="width: 10%">版本号</th>
				<th style="width: 20%">固件</th>
				<th style="width: 10%">开放状态</th>
				<th style="width: 25%">releaseNotes</th>
				<th style="width: 10%">App最低版本</th>
				<th style="width: 10%">App提示</th>
				<th style="width: 10%" data-l10n-id="operations"></th>
			</tr>
			<tr class="template">
				<td>{{hardware}}</td>
				<td>{{version}}</td>
				<td>{{_firmwares}}</td>
				<td>{{_open}}</td>
				<td>{{_releaseNotes}}</td>
				<td>{{appMinVersion}}</td>
				<td>{{_appAlert}}</td>
				<td class="operations status-{{open}}">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li>
								<a href="#/mate/firmwarerelease?id={{id}}" data-l10n-id="edit"></a>
							</li>
							<li>
								<a href="javascript:remove('{{id}}')" data-l10n-id="delete"></a>
							</li>
							<li>
								<a href="javascript:pushNotification('{{id}}')">发送通知</a>
							</li>
							<li>
								<a href="#/mate/firmwaredevices?firmwareId={{id}}">测试设备管理</a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>