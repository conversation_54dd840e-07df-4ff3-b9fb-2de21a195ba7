<script>
App.ready(function() {
	App.menu.select('/mate/devices');

	var deviceId = App.router.getParameter('deviceId');
	var input = $("#console-input");
	var console = null;
	var timer = null;
	var timerFlag = 0;
	var wrapParams = function(params) {
		var headers = {'consoleId': console.id, 'consoleSecret': console.secret};
		if (params) {
			return $.extend(headers, params);
		} else {
			return headers;
		}
	};
	var submitIt = function() {
		var cmd = input.val();
		if (cmd.length == 0) {
			return;
		}
		return App.api('mate/admin_console_input', wrapParams({'cmd': cmd})).done(function(){
			input.val('');
			refreshOutput();
		});
	};
	var handleError = function() {
	
	};
	var handleOutput = function(result) {
		if (result.length > 0) {
			var container = $('#console-output');
			$.each(result, function(index, item) {
				container.append('<p><font>'+ mars.date.format(new Date(item.t), 'HH:mm:ss') +':</font>'+ item.s +'</p>');
			});
			var elem = container[0];
			elem.scrollTop = elem.scrollHeight;
		}
	};
	var doRefreshOutput = function(flag, counter) {
		counter++;
		App.api('mate/admin_console_output', wrapParams(), {'loading': false, 'success': handleOutput, 'error': handleError}).always(function(){
			// a timer is set
			if (flag != timerFlag) {
				return;
			}
			// no more message
			if (counter >= 20) {
				return;
			}
			timer = setTimeout(function(){ doRefreshOutput(flag, counter); }, 2000);
		});
	};
	var refreshOutput = function() {
		//reset
		if (timer) {
			clearTimeout(timer);
			timer = null;
		}
		timerFlag = new Date().getTime();
		doRefreshOutput(timerFlag, 0);
	};
	var requestSession = function() {
		return App.api('mate/admin_console_create', {'deviceId': deviceId}, {
			success: function(result) {
				console = result;
			}
		});
	};
    var init = function() {
        $('#submit-btn').click(submitIt);
        input.keydown(function (e) {
            if (e.keyCode == 13) {
                submitIt();
            }
        });
        requestSession();
    };
    init();
});
</script>
<style type="text/css">
#console-output {
	padding: 1rem;
	max-height: 400px;
	overflow-y: scroll;
}
#console-output p {
	line-height: 1.5rem;
}
#console-output font {
	color: red;
	padding-right: 1rem;
}
</style>
<div id="console-output"></div>
<textarea id="console-input" style="width:100%;" rows="5" class="form-control"></textarea><br/>
<a href="javascript:;" id="submit-btn" class="btn btn-primary">执行</a>