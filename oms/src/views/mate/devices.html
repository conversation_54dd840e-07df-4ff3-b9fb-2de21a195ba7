<script>
    App.ready(function () {
        var pad = function (s) {
            s = '' + s;
            if (s.length == 1) {
                return '0' + s;
            }
            return s;
        }
        var formatMinutes = function (s) {
            var hours = parseInt(s / 60);
            var minutes = s - hours * 60;
            return pad(hours) + ':' + pad(minutes);
        };
        var getStatusText = function (v) {
            if (v == 2) {
                return '在线';
            } else if (v == 3) {
                return '忙碌';
            } else if (v == 4) {
                return '固件升级中';
            } else {
                return '离线';
            }
        };
        
        
		var showDeviceInfo = function(map) {
			var s = '';
			if (!map) {
				return s;
			}
			var v;
			for ( var key in map) {
				v = map[key];
				s += key + ': ' + v;
				s += '<br/>';
			}
			return s;
		};
        
        var request = function (offset) {
            App.scrollToTop();
            var params = $.extend({'limit': 10}, App.router.getParameters());
            params.offset = (!U.isNull(offset)) ? offset : params.offset;
            App.api('/mate/devices', params).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yy-MM-dd HH:mm');
                    context._firmware = JSON.stringify(context.firmware);
                    context._deviceInfo =showDeviceInfo(context.callInfo.deviceInfo);
                    var share = context.shareStatus;
                    var _share = '';
                    if (!share.open) {
                        _share = '不分享';
                    } else {
                        _share = formatMinutes(share.start) + '-' + formatMinutes(share.end) + '<br/>时长：' + share.limit + '分钟';
                    }
                    context._share = _share;
                    var callInfo = context.callInfo || {};
                    context._callInfo = getStatusText(callInfo.status);
                    context._callInfoDetail = 'Sip: ' + (callInfo.sipServer ? callInfo.sipServer : '');
                    var row = U.template(template, context);
                    row = $(row);
                    if (context.owner) {
                        row.find('li[data-bind]').addClass('none');
                    } else {
                        row.find('li[data-unbind]').addClass('none');
                    }
                    table.append(row);
                }
                initOperationButtons();
                App.pagination(ps, request);
            });
        };

        window.restart = function (deviceId) {
            if (!confirm('确定要重启吗？')) {
                return;
            }
            App.api('/mate/restart', {deviceId: deviceId}).then(function (done) {
                if (done === 1) {
                    alert('重启成功');
                } else {
                    alert('消息未能下发');
                }
            });
        };

        window.resetLogSet = function (deviceId) {
            if (!confirm('确定要重置Log设置吗？')) {
                return;
            }
            App.api('/mate/saveloglevel', {deviceId: deviceId}).then(function (done) {
                if (done === 1) {
                    alert('重置成功');
                } else {
                    alert('已重置，但消息未下发');
                }
            });
        };

        window.unbind = function (deviceId) {
            var s = prompt('请输入unbind', '');
            if (s !== 'unbind') {
                return;
            }
            App.api('/mate/unbind', {deviceId: deviceId}).then(function () {
                alert('解绑成功');
                window.location.reload();
            });
        };
        window.dobind = function (deviceId) {
            var userId = prompt('请输入用户ID', '');
            if (!userId) {
                return;
            }
            App.api('/mate/bindbyadmin', {deviceId: deviceId, userId: userId}).then(function () {
                alert('绑定成功');
                window.location.reload();
            });
        };
        var findPimClient = function(deviceId) {
        	  App.api('/mate/pim_findclient', {'deviceId': deviceId}).then(function(result) {
        		  if (result.length > 0) {
        			  var infos = [];
        			  var s = '当前设备在线.';
        			  for (var i = 0; i < result.length; i++) {
        				  s += '\n';
        				  s += '客户端'+ (i+1) +'：' + result[i][1];
        			  }
        			  alert(s);
        		  } else {
        			  alert('当前设备不在线');
        		  }
              });
        };
        var closePimClient = function(deviceId) {
        	  var secret = 'close_pim_client';
        	  var input = prompt('请输入: ' + secret);
	          if (input != secret) {
	        	  return;
	          }
	      	  App.api('/mate/pim_closeclient', {'deviceId': deviceId}).then(function(result) {
	      		 if (result) {
	      			 alert('操作成功，设备已被断开连接');
	      		 } else {
	      			 alert('设备不在线，请确认在线状态');
	      		 }
	          });
      };
        var initOperationButtons = function() {
        	$('[data-op-type=findPimClient]').click(function(){
        		findPimClient($(this).attr('data-op-key'));
        	});
          	$('[data-op-type=closePimClient]').click(function(){
          		closePimClient($(this).attr('data-op-key'));
        	});
        };
        var submitIt = function () {
            var data = $('#search-form').serializeObject();
            App.router.go(App.router.getCurrentPath(), data);
        };
        $('#search-btn').click(submitIt);
        $("#search-key").keydown(function (e) {
            if (e.keyCode == 13) {
                submitIt();
            }
        });
        request();
    });
</script>
<style>
    .table a[href=""] {
        color: #666;
    }
</style>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
            <select class="form-control" name="type" ng-model="REQUEST.type">
                <option value="1">设备ID</option>
                <option value="2">Mac</option>
                <option value="3">主人ID</option>
                <option value="4">SN</option>
            </select>
            <input id="search-key" type="text" class="form-control" name="s" ng-model="REQUEST.s"/>
            <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 10%">id</th>
                <th style="width: 10%">Mac</th>
                <th style="width: 10%">SN</th>
                <th style="width: 10%">注册时间</th>
                <th style="width: 12%">硬/固件</th>
                <th style="width: 10%">设备详细信息</th>
                <th style="width: 15%">固件版本</th>
                <th style="width: 13%">分享</th>
                <th style="width: 5%">状态</th>
                <th style="width: 10%">主人</th>
                <th style="width: 10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td><a href="{{cover}}" target="_blank">{{id}}</a></td>
                <td>{{mac}}</td>
                <td title="{{secret}}">{{sn}}</td>
                <td>{{_createdAt}}</td>
                <td>{{hardware}}/{{_firmware}}</td>
                 <td>{{_deviceInfo}}</td>
                <td>{{version}}</td>
                <td>{{_share}}</td>
                <td title="{{_callInfoDetail}}">{{_callInfo}}</td>
                <td><a href="#/user/users?username={{owner.id}}">{{owner.id}}</a></td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li data-unbind="1">
                                <a href="javascript:unbind('{{id}}')">解绑</a>
                            </li>
                            <li data-bind="1">
                                <a href="javascript:dobind('{{id}}')">绑定</a>
                            </li>
                            <li>
                                <a href="#/mate/logs?deviceId={{id}}">Log</a>
                            </li>
                            <li>
                                <a href="#/mate/logset?deviceId={{id}}">Log设置</a>
                            </li>
                            <li>
                                <a href="javascript:resetLogSet('{{id}}');">Log重置</a>
                            </li>
                            <li>
                                <a href="javascript:restart('{{id}}');">重启</a>
                            </li>
                            <li>
                                <a href="#/mate/console?deviceId={{id}}">控制台</a>
                            </li>
                            <!-- <li>
                                <a href="javascript:;" data-op-type="findPimClient" data-op-key="{{id}}"">PIM在线状态</a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="closePimClient" data-op-key="{{id}}">关闭PIM连接</a>
                            </li>
                            <li>
                                <a href="#/mate/loginhistory?deviceId={{id}}">PIM连接历史</a>
                            </li> -->
                            <li>
                                <a href="#/mate/callrecords?deviceId={{id}}&isCallee=0">主叫记录</a>
                            </li>
                            <li>
                                <a href="#/mate/callrecords?deviceId={{id}}&isCallee=1">被叫记录</a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>