<script>
	App.ready(function() {
		App.menu.select('/mate/logmodules');
		var form = $('#save-form');
		var postOptions = {};

		var loadModel = function() {
			var id = App.router.getParameter('id');
			if (!id) {
				return;
			}
			var options = $.extend(postOptions, {
				success : function(model) {
					if ($.isEmptyObject(model)) {
						return;
					}
					$('#save-form').renderModel({
						model : model
					});
				}
			});
			return App.api('/mate/logmodule', {
				'id' : id
			}, options);
		};

		var initButtons = function() {
			$('#save-btn').click(function() {
				var data = form.serializeObject();
				App.api('/mate/savelogmodule', data, postOptions).then(function() {
					App.router.go('/mate/logmodules');
				});
			});
		};

		$.when(loadModel()).then(initButtons);
	});
</script>
<form id="save-form" role="form" onsubmit="return false;">
	<div class="form-group">
		<label>模块：</label> <input type="text" name="id" class="form-control" ng-model="model.id" />
	</div>
	<div class="form-group">
		<label>Log等级：</label> <input type="text" name="level" class="form-control" ng-model="model.level" />
	</div>
	<div class="form-group">
		<label></label> <a id="save-btn" href="javascript:;" class="btn btn-primary">保存</a>
	</div>
</form>