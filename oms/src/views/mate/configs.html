<script>
    App.ready(function () {
        $('.table-main').on('click', '.btn-modify', function () {
            var _code = $(this).attr('code');
            App.router.go('/mate/config', {
                code: _code
            });
        });
        $('.table-main').on('click', '.btn-delete', function () {
        	if (!confirm('确定要删除吗？')) {
        		return;
        	}
            var _code = $(this).attr('code');
            App.api('/mate/config_remove', {code: _code}).then(function (result) {
                App.router.go('/mate/configs');
            });
        });
        var init = function () {
        	 App.api('/mate/configs').then(function (result) {
                 var table = $('.table-main');
                 var template = table.attr('data-template');
                 if (!template) {
                     template = '<tr>' + table.find('tr.template').html() + '</tr>';
                     table.attr('data-template', template);
                 }
                 table.find('tr:not(.header)').remove();
                 for (var i = 0; i < result.length; i++) {
                     var context = result[i];
                     context._number = i + 1;
                     context._config = JSON.stringify(context.config);
                     var row = U.template(template, context);
                     table.append(row);
                 }
             });
        };
        init();
    });
</script>
<style>
</style>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <a href="#/mate/config" class="btn btn-primary btn-add" role="button">添加</a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table x-table table-main">
            <tr class="header">
                <th style="width: 10%">编号</th>
                <th style="width: 20%">地区</th>
                <th style="width: 20%">参数</th>
                <th style="width: 10%">操作</th>
            </tr>
            <tr class="template">
                <td>{{_number}}</td>
                <td>{{code}}</td>
                <td>{{_config}}</td>
                <td>
                    <div class="dropdown" style="display: inline-block;">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:;" class="btn-modify" code="{{code}}">编辑</a>
                            </li>
                            <li>
                                <a href="javascript:;" class="btn-delete" code="{{code}}"> 删除</a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>