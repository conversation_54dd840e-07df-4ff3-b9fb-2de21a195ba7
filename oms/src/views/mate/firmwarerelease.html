<script src="js/localesaver.js"></script>
<script>
App.ready(function() {
   App.menu.select('/mate/firmwarereleases');
  var form = $('#save-form');
  var model = null;
  var localeSaver = new LocaleSaver({'dialogUrl': '/mate/firmwarereleaselocale.html'});

  var save = function() {
	  var data = form.serializeObject();
      var localData = localeSaver.getDataAsModel();
      for (var key in localData) {
    	  data[key] = localData[key];
      }
	  {
		 var firmwareSelects = $('[data-firmwareid]') || [];
		 var firmwares = [];
		 $.each(firmwareSelects, function(index, select) {
			 firmwares.push({'id': $(select).val()});
		 });
		 data['firmwares'] = firmwares;
	  }
	  App.api('/mate/savefirmwarerelease', data).then(function(){
	    	App.router.go('/mate/firmwarereleases');
	  });
  };
  
  var renderModel = function() {
	  form.renderModel({model: model});
      localeSaver.setDataByModel(model, ['releaseNotes','appAlert']);
  };
  
  var loadModel = function() {
	var id = App.router.getParameter('id');
	if (!id) {
		model = {'hardware': App.router.getParameter('hardware')};
		renderModel();
		return;
	}
	return App.api('/mate/firmwarerelease', {'id': id}).then(function(data){
		model = data;
		if ($.isEmptyObject(model)) {
			alert('The release doest not exists or has been removed')
			return false;
		}
		renderModel();
	});
  };
  
  var isFirmwareChecked = function(id) {
	  var fws = model.firmwares;
	  if (!fws) {
		  return;
	  }
	  for (var i=0;i<fws.length;i++) {
		  if (fws[i].id == id) {
			  return true;
		  }
	  }
	  return false;
  };
  
  var insertAtIndex = function(i, item, parent) {
	    if (i <= 0) {
	    	parent.prepend(item);        
	    } else {
	    	var li = parent.find("li:nth-child(" + i + ")");
	    	li.after(item);
	    }
  };
  
  window.moveUp = function(uid) {
	  var li = $('#li-' + uid);
	  var ul = li.parent();
	  var index = li.index();
	  if (index == 0) {
		  return;
	  }
	  li.remove();
	  insertAtIndex(index - 1, li, ul);
  };
  
  window.moveDown = function(uid) {
	  var li = $('#li-' + uid);
	  var ul = li.parent();
	  var index = li.index();
	  if (index == ul.children().length  - 1) {
		  return;
	  }
	  li.remove();
	  insertAtIndex(index + 1, li, ul);
  };
  
  var guid = function() {
	  function s4() {
	    return Math.floor((1 + Math.random()) * 0x10000)
	      .toString(16)
	      .substring(1);
	  }
	  return s4() + s4() + '-' + s4() + '-' + s4() + '-' +
	    s4() + '-' + s4() + s4() + s4();
	};
  
  var loadFirmwares = function() {
		return App.api('/mate/firmwaresbymodule', {'releaseId': model.id, 'hardware': model.hardware}).then(function(data){
			$.each(data, function(index, item) {
				var module = item.module;
				var firmwares = item.firmwares;
				var uid = guid();
				var li = '<li id="li-'+uid+'">';
				li += module + ': ' + '<a href="javascript:moveUp(\''+ uid +'\');">上移</a>' + '<a href="javascript:moveDown(\''+ uid +'\');" style="margin-left:10px;">下移</a>';
				li += '<select data-firmwareid="1" class="form-control">';
				$.each(firmwares, function(index, firmware) {
					li += '<option value="'+firmware.id+'"';
					if (isFirmwareChecked(firmware.id)) {
						li += ' selected="selected"';
					}
					var notes = firmware.notes ? '(' +firmware.notes + ')': '';
					li += '>版本'+ firmware.version + notes +'</option>';
				});
				li += '</select>';
				li += '</li>';
				li = $(li);
				$('#firmware-container').append(li);
			});
		});
  };

  //init
  var init = function() {
	  localeSaver.init().then(loadModel).then(loadFirmwares).then(function() {
		  //button
	      $('#save-btn').click(save);
      });
  };
  init();
});
</script>
<form id="save-form" role="form" onsubmit="return false;">
		<input type="hidden" name="id" ng-model="model.id"/>
		<div class="form-group">
	    	<label>硬件</label>
            <input type="text" name="hardware" class="form-control" ng-model="model.hardware" readonly="readonly"/>
	  	</div>
 	   <div class="form-group">
         	<label>版本号</label>
            <input type="text" name="version" class="form-control" ng-model="model.version" placeholder="格式如0.2, 1.0, 1.3.5"/>
       </div>
 	   <div class="form-group">
         	<label>App要求版本号</label>
            <input type="text" name="appMinVersion" class="form-control" ng-model="model.appMinVersion" placeholder="格式如0.2, 1.0, 1.3.5"/>
       </div>
 	   <div class="form-group">
         	<label>状态</label>
            <select name="open" ng-model="model.open" class="form-control">
            	<option value="0">封闭测试</option>
            	<option value="1">公开下载</option>
            </select>
       </div>
       <div class="form-group">
           <label>固件选择</label>
           <ul id="firmware-container">
           </ul>
       </div>
	    <div class="form-group">
	        <div class="controls">
	            <a href="javascript:;" id="local-add-btn">
	                <span>+</span>
	                <span data-l10n-id="localize"></span>
	            </a>
	            <div id="locale-spec-template">
	                <div class="locale-spec">
	                    <div class="locale-spec-item">
	                        <label data-l10n-id="locale"></label>:
	                        <span class="locale">{{locale.name}}</span>
	                        <a href="javascript:;" class="edit" data-l10n-id="edit" data-op-type="update" data-op-key="{{locale.key}}"></a>
	                        <a href="javascript:;" class="edit" data-l10n-id="delete" data-op-type="remove" data-op-key="{{locale.key}}"></a>
	                    </div>
	                    <div class="locale-spec-item">
	                    	<label>ReleaseNotes</label>:<span>{{spec.releaseNotes}}</span><br/>
	                    	<label>App版本过低提示</label>:<span>{{spec.appAlert}}</span>
	                    </div>
	                </div>
	            </div>
	            <div id="locale-specs"></div>
	        </div>
	    </div>
       <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
       <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
</form>