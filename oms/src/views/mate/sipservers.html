<script>
    App.ready(function () {
    	var searchForm = $('#search-form');
        var searchParams = searchForm.serializeObject();
        var remove = function (id) {
            if (!confirm(App.text('confirm.operation'))) {
                return;
            }
            var params = {'id': id};
            App.api('/mate/removesipserver', params).then(function () {
            	request(0);
            });
        };
        var request = function (offset) {
            App.scrollToTop();
        	var requestParams = $.extend(searchParams, {'offset': U.isNull(offset) ? 0 : offset});
            App.api('/mate/sipservers', requestParams).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._state = context.state == 1 ? '在线' : '离线';
                    var row = U.template(template, context);
                    table.append(row);
                }
                table.find('[data-op-type="remove"]').click(function(){
                	remove($(this).attr('data-op-key'));
                });
                App.pagination(ps, request);
            });
        };
        var submitIt = function () {
            App.router.go(App.router.getCurrentPath(), searchForm.serializeObject());
        };
        var init = function () {
           	$('#create-btn').click(function() {
           		App.router.go('/mate/sipserver');
           	});
    		request();
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
        	<input type="hidden" name="limit" value="20" ng-model="REQUEST.limit"/>
            <a href="javascript:;" id="create-btn" class="btn btn-success" data-l10n-id="add"></a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 30%">服务器</th>
                <th style="width: 20%">所在组</th>
                <th style="width: 15%">负载</th>
                <th style="width: 15%">状态</th>
                <th style="width: 20%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{id}}</td>
                <td>{{group.name}}</td>
                <td>{{loadbalance}}</td>
                <td>{{_state}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:;" data-op-type="remove" data-op-key="{{id}}" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>