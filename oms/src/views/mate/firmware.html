<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>

<script>
    App.ready(function () {
        var form = $('#save-form');
        var postOptions = {};
        var previewBinary = function (result) {
            var preview = $('#binary-preview');
            if (result) {
                preview.html('<a href="' + result.url + '" target="_blank">' + result.url + '</a>');
            } else {
                preview.html('');
            }
            form.find('input[name="file.url"]').val(result ? result.url : '');
            form.find('input[name="file.size"]').val(result ? result.size : '');
            form.find('input[name="file.digest"]').val(result ? result.digest : '');
        };
        App.upload.init({
            namespace: 'matefw',
            type: 'file',
            id: 'upload-btn',
            fileUploaded: function (info) {
                if (typeof info == 'string') {
                    info = JSON.parse(info);
                }
                $.ajax({
                    url: info.url + '?hash/md5',
                    dataType: 'json',
                    success: function (ret) {
                        info.digest = ret.md5;
                        previewBinary(info);
                    }
                });
            },
            error: function (errTip) {
                alert('文件上传出错' + errTip);
            }
        });
        var save = function () {
            var data = form.serializeObject();
            App.api('/mate/savefirmware', data, postOptions).then(function () {
                App.router.go('/mate/firmwares');
            });
        };
        var loadModules = function () {
            return App.api('/mate/modules', null, postOptions).then(function (list) {
                var select = form.find('[name="module"]')
                $.each(list, function (index, item) {
                    select.append('<option value="' + item.id + '">' + item.id + '</option>');
                });
            });
        };
        var loadModel = function () {
            var id = App.router.getParameter('id');
            if (!id) {
                return;
            }
            return App.api('/mate/firmware', {'id': id}, postOptions).then(function (data) {
                if ($.isEmptyObject(data)) {
                    alert('The firmware doest not exists or has been removed')
                    return false;
                }
                $('#save-form').renderModel({model: data});
                previewBinary(data.file);
            });
        };
        //menu
        App.menu.select('/mate/firmwares');
        //init
        loadModules().then(loadModel).then(function () {
            //init buttons
            $('#save-btn').click(save);
        });
    });
</script>
<form id="save-form" role="form" onsubmit="return false;">
    <input type="hidden" name="id" ng-model="model.id"/>
    <input type="hidden" name="file.url" ng-model="model.file.url"/>
    <input type="hidden" name="file.size" ng-model="model.file.size"/>
    <input type="hidden" name="file.digest" ng-model="model.file.digest"/>

    <div class="form-group">
        <label>硬件</label>
        <input type="text" name="hardware" class="form-control" ng-model="model.hardware"/>
    </div>
    <div class="form-group">
        <label>版本号</label>
        <input type="text" name="version" class="form-control" ng-model="model.version"/>
    </div>
    <div class="form-group">
        <label>模块</label>
        <select name="module" class="form-control" ng-model="model.module">
        </select>
    </div>
    <div class="form-group">
        <label data-l10n-id="file"></label>
        <div>
            <a href="javascript:;" id="upload-btn">+选择上传文件</a>
        </div>
        <div id="binary-preview"></div>
    </div>
    <div class="form-group">
        <label data-l10n-id="detail"></label>
        <textarea name="notes" class="form-control" rows="5" ng-model="model.notes"></textarea>
    </div>
    <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
    <a href="#/mate/firmwares" class="btn btn-info" data-l10n-id="cancel"></a>
</form>
