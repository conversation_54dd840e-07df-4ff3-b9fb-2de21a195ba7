<style type="text/css">
.items {
	margin: 0 20px;
}
.items h1 {
	display: block;
}
.item {
	clear:both;
	padding: 10px 0;
}
.item label {
	display: block;
	float: left;
	width: 20%;
}
.item span {
	display: block;
	float: right;
	width: 80%;
	word-wrap: break-word;
}
</style>
<script>
App.ready(function() {
	App.menu.select('/mate/devices');
	
	var id = App.router.getParameter('id');
	var isCallee = App.router.getParameter('isCallee');
	var template = '<div class="item"><label>{{key}}</label><span>{{value}}</span></div>';
	var isInArray = function(array, obj) {
		for (var i = 0; i < array.length; i++) {
			if (array[i] === obj) {
				return true;
			}
		}
		return false;
	};
	var getKeys = function(items) {
		var keys = [];
		for (var i = 0; i<items.length; i++) {
			var item = items[i];
			if (!item) {
				continue;
			}
			for(var k in item) {
				if (k == 'id') {
					continue;
				}
				if (!isInArray(keys, k)) {
					keys.push(k);
				}
			}
		}
		return keys;
	};
	var render = function(result, container) {
		if (!result || result.length == 0) {
			container.html('<h1>没有记录</h1>');
			return;
		}
		var keys = getKeys(result);
		for(var i = 0; i < keys.length; i++) {
			var k = keys[i];
			var s = '';
			$.each(result, function(index, info){
				if (!info) {
					return;
				}
				var v = info[k];
				if (mars.isNull(v)) {
					return;
				}
				if (k == 'callDateTime') {
					v = U.date.format(U.date.parse(v), 'yyyy-MM-dd HH:mm:ss');
				} else if (k == 'callState') {
					v = v ? '成功' : '失败';
				} else if (k == 'during') {
					v = parseInt(v/1000) + 's';
				}
				v = (index ===0 ? '主: ' : '被: ') + JSON.stringify(v);
				if (s.length > 0) {
					s += '<br/>';
				}
				s += v;
			});
			container.append(mars.template(template, {'key':k, 'value':s}));
		}
	};
	var request = function() {
		App.api('/mate/callrecord',	{
			'id' : id,
			'isCallee': isCallee
		}).then(function(result) {
			render(result, $('#caller-record'));
		});
	};
	$('#back-btn').click(function(){
		window.history.back();
	});
	request();
});
</script>

<div class="panel panel-default x-panel">
<div class="panel-heading">
	<a href="javascript:;" id="back-btn" class="btn btn-primary" data-l10n-id="back"></a>
	<span>通话详细</span>
</div>
<div class="panel-body">
	<div id="caller-record" class="items">
		<!-- <div class="item">
			<div class="item-label">matchId</div>
			<div class="item-content">{{matchId}}</div>
		</div> -->
	</div>
</div>
</div>
