<script>
	App.ready(function() {
		App.menu.select('/mate/publicmates');
		var form = $('#save-form');

		var loadModel = function() {
			var id = App.router.getParameter('id');
			if (!id) {
				return;
			}
			form.find('[name=id]').attr('readonly', 'readonly');
			return App.api('/mate/findpublicmate', {
				'id' : id
			}, {
				success : function(model) {
					if ($.isEmptyObject(model)) {
						return;
					}
					$('#save-form').renderModel({
						model : model
					});
				}
			});
		};

		var initButtons = function() {
			$('#save-btn').click(function() {
				var data = form.serializeObject();
				App.api('/mate/savepublicmate', data).then(function() {
					App.router.go('/mate/publicmates');
				});
			});
		};

		$.when(loadModel()).then(initButtons);
	});
</script>
<style>
#cover-preview img {
	width: 437px;
	height: 198px;
}
</style>
<form id="save-form" role="form" onsubmit="return false;">
	<div class="form-group">
		<label>设备ID：</label> <input type="text" name="id" class="form-control" ng-model="model.id"/>
	</div>
	<div class="form-group">
		<label>排序：</label> <input type="text" name="rank" class="form-control" ng-model="model.rank" />
	</div>
	<div class="form-group">
		<label></label> <a id="save-btn" href="javascript:;" class="btn btn-primary">保存</a>
	</div>
</form>