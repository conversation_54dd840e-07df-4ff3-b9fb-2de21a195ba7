<script>
	App.ready(function() {
		App.menu.select('/mate/devices');
		var form = $('#save-form');
		var template = $('#module-container-template').html();
		var deviceId = App.router.getParameter('deviceId');
		
		var loadModel = function() {
			if (!deviceId) {
				return;
			}
			return App.api('/mate/loglevel', {'deviceId' : deviceId}).then(function(settings){
				var container = $('#module-container');
				console.log(settings);
				for (var key in settings) {
					var html = U.template(template, {'module': key, 'level': settings[key]});
					container.append(html);
				}
			});
		};

		var initButtons = function() {
			$('#save-btn').click(function() {
				var settings = form.serializeObject();
				var data = {'deviceId': deviceId, 'settings': JSON.stringify(settings)};
				App.api('/mate/saveloglevel', data).then(function(done) {
					if (done === 1) {
						alert('已保存');
						window.history.back();
					} else {
						alert('已保存，但消息未下发');
					}
				});
			});
		};

		$.when(loadModel()).then(initButtons);
	});
</script>

<div id="module-container-template" class="none">
	<div class="form-group">
		<label>{{module}}</label> <input type="text" name="{{module}}" class="form-control" value="{{level}}" />
	</div>
</div>
<form id="save-form" role="form" onsubmit="return false;">
	<div id="module-container"></div>
	<div class="form-group">
		<label></label> 
		<a id="save-btn" href="javascript:;" class="btn btn-primary">保存</a>
	</div>
</form>