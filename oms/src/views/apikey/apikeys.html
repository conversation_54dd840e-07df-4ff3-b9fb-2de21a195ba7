<script>
	App.ready(function() {
		var openDialog = function(model) {
			App.openDialog({
				url : App.router.getRealPath('/apikey/apikey.html'),
				context : model,
				noHeader : true,
				primaryClick : save
			});
		};
		var cu = function(key) {
			var model = null;
			if (key) {
				App.api('/apikey/find',  {
					key : key
				}, {
					success : function(model) {
						openDialog(model);
					}
				});
			} else {
				openDialog(model);
			}
		};
		var save = function() {
			App.api('/apikey/save', $('#dialog-form').serializeObject(), {
				success : function() {
					window.location.reload();
				}
			});
		};
		var remove = function(key) {
			if (!confirm(App.text('confirm.delete'))) {
				return;
			}
			App.api('/apikey/remove',  {
				key : key
			}, {
				success : function() {
					window.location.reload();
				}
			});
		};
		var showSecret = function(key) {
			var container = $('#key-' + key);
			container.find('span').html(container.find('input').val());
		};
		
		var request = function(offset) {
			App.scrollToTop();
			var params = App.router.getParameters();
			params = $.extend({'limit' : 10}, params);
			params.offset = (!U.isNull(offset)) ? offset : params.offset;
			
			App.api('/apikey/apikeys', params, {
				success : function(ps) {
					var users = ps.items || [];
					var table = $('#the-table');
					var template = table.attr('data-template');
					if (!template) {
						template = '<tr>' + table.find('tr.template').html()
								+ '</tr>';
						table.attr('data-template', template);
					}
					table.find('tr:not(.header)').remove();
					for ( var i = 0; i < users.length; i++) {
						var context = users[i];
						context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
						var row = U.template(template, context);
						table.append(row);
					}
					App.pagination(ps, request);
				}
			});
		};
		var submitIt = function() {
			App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
		};
		$('#search-btn').click(submitIt);
		$("#search-key").keydown(function(e){
		    　if(e.keyCode == 13) {
		    	submitIt();
		    　}
		});
		request();
		
		window.cu = cu;
		window.remove = remove;
		window.showSecret = showSecret;
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
			<input id="search-key" type="text" class="form-control" name="key" placeholder="key" ng-model="REQUEST.key" />
			<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
			<a href="javascript:cu()" class="btn btn-success" style="margin-left: 5px;" data-l10n-id="create"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 15%">Key</th>
				<th style="width: 15%">Secret</th>
				<th style="width: 15%">创建时间</th>
				<th style="width: 20%">备注</th>
				<th style="width: 20%">权限</th>
				<th style="width: 15%">操作</th>
			</tr>
			<tr class="template">
				<td>{{key}}</td>
				<td id="key-{{key}}"><span>******</span>
					<input type="hidden" value="{{secret}}" /></td>
				<td>{{_createdAt}}</td>
				<td>{{notes}}</td>
				<td>{{apis}}</td>
				<td class="operations">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li>
								<a href="javascript:showSecret('{{key}}')">获取Secret</a>
							</li>
							<li>
								<a href="javascript:cu('{{key}}')">修改</a>
							</li>
							<li>
								<a href="javascript:remove('{{key}}')">删除</a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>