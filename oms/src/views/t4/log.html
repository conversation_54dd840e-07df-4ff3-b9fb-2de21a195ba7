<script>
  App.ready(function () {
    var requestParams = $.extend({'limit': 10}, App.router.getParameters());
    var request = function (offset) {
      App.menu.select('/t4/devices');
      App.scrollToTop();
      if (!U.isNull(offset)) {
        requestParams.offset = offset;
      }
      requestParams.sn = '1';
      // App.api('/center/t4/logs', requestParams).then(function (ps) {
      App.api('/t4/logs', requestParams).then(function (ps) {
        var list = ps.items || [];
        var table = $('#the-table');
        var template = table.attr('data-template');
        if (!template) {
          template = '<tr>'
                  + table.find('tr.template')
                          .html() + '</tr>';
          table.attr('data-template', template);
        }
        table.find('tr:not(.header)').remove();
        for (var i = 0; i < list.length; i++) {
          // var context = list[i];
          // context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
          // var row = U.template(template, context);
          table.append(row);
        }
        App.pagination(ps, request);
      });
    };
    request();

    var submitIt = function () {
      var data = $('#search-form').serializeObject();
      App.router.go(App.router.getCurrentPath(), data);
    };
    var init = function () {
      $('#search-btn').click(submitIt);
      $("#search-key").keydown(function (e) {
        if (e.keyCode == 13) {
          submitIt();
        }
      });
      request();
    };
    init();
  });
</script>
<div className="panel panel-default x-panel">
  <div class="panel-heading">
    <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
      <select class="form-control" name="type" ng-model="REQUEST.type">
        <option value="type">设备id</option>
      </select>
      <input id="search-key" type="text" class="form-control" name="id" ng-model="REQUEST.s"/>
      <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
    </form>
  </div>
  <div className="panel-body">
    <div className="table-responsive">
      <table className="table table-hover x-table" id="the-table">
        <tr className="header">
          <th style="width: 35%">上传日期</th>
          <th style="width: 35%">来自</th>
          <th style="width: 30%">内容</th>
        </tr>
        <tr className="template">
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </table>
    </div>
  </div>
</div>
<div id="pagination-wrap"></div>