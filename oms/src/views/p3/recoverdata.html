<script>
	App.ready(function () {
		App.menu.select('/p3/data');
		var form = $('#save-form');
		var save = function () {
			App.api('/p3/recoverdata', form.serializeObject()).then(function (days) {
				if (days.length == 0) {
					alert('数据未恢复，请检查数据格式');
				} else {
					form.find('[name="data"]').val('');
					alert('数据已恢复，请到活动详细界面查看相关数据');
					App.router.go('/p3/data', { 'petId': form.find('[name="petId"]').val(), 'day': days[0] });
				}
			});
		};
		$('#submit-btn').click(save);
	});
</script>
<form id="save-form" role="form" onsubmit="return false;">
	<div class="form-group">
		<label>宠物ID</label>
		<input type="text" class="form-control" name="petId" ng-model="REQUEST.petId" readonly="readonly" />
	</div>
	<div class="form-group">
		<label>数据</label>
		<textarea name="data" class="form-control" rows="8"></textarea>
	</div>
	<a href="javascript:;" id="submit-btn" class="btn btn-primary" data-l10n-id="save"></a>
	<a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
</form>