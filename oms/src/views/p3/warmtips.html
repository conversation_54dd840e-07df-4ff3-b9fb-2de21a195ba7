<script>
    App.ready(function () {
        var form = $('#form1');
        var save = function () {
            App.api('/p3/savewarmtips', form.serializeObject()).then(
                function () {
                    alert('已保存');
                });
        };
        var init = function () {
            App.api('/p3/findwarmtips').then(function (result) {
                form.renderModel({ 'warmtips': result });
            });
            $('#save-btn').click(save);
        };
        init();
    });
</script>
<div>温馨提示：</div>
<form id="form1" role="form" onsubmit="return false;">
    <div class="form-group">
        <label></label>
        <textarea name="warmtips" ng-model="warmtips" rows="10" class="form-control"></textarea>
    </div>
    <div class="form-group">
        <label></label>
        <a href="javascript:;" id="save-btn" class="btn btn-primary fr" data-l10n-id="save"></a>
    </div>
</form>