<script>
	App.ready(function () {
		var FIXED_STATE = 2;
		var requestParams = $.extend({ 'limit': 10 }, App.router.getParameters());
		var request = function (offset) {
			App.scrollToTop();
			if (!U.isNull(offset)) {
				requestParams.offset = offset;
			}
			App.api('/p3/issues', requestParams).then(function (ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
						+ table.find('tr.template')
							.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					var t1 = U.date.parse(context.t1);
					context._day = U.date.format(t1, 'yyyyMMdd');
					context._createdAt = U.date.format(U.date.parse(context.createdAt), 'MM-dd HH:mm');
					context._state = (context.state == FIXED_STATE) ? '已处理' : '未处理';
					context._problem = '连续性错误<br/>' + U.date.format(t1, 'MM-dd HH:mm:ss')
						+ '到' + U.date.format(U.date.parse(context.t2), 'MM-dd HH:mm:ss')
						+ '，时区: ' + context.timezone;
					var row = U.template(template, context);
					table.append(row);
				}
				initOperations();
				App.pagination(ps, request);
			});
		};
		var initOperations = function () {
			var hideKeys = ['fix2', 'unfix1'];
			$.each(hideKeys, function (index, item) {
				$('[data-op-display=' + item + ']').addClass('none');
			});
			$('[data-op-type=fix]').click(function () {
				markFixed($(this).attr('data-op-key'), true);
			});
			$('[data-op-type=unfix]').click(function () {
				markFixed($(this).attr('data-op-key'), false);
			});
		};
		var markFixed = function (id, fixed) {
			var to = fixed ? '已处理' : '未处理';
			if (!confirm('确定标记为：' + to)) {
				return;
			}
			App.api('/p3/fixissue', { 'id': id, 'fixed': fixed }).then(function () {
				window.location.reload();
			});
		};
		var submitIt = function () {
			App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
		};
		var init = function () {
			$('#search-btn').click(submitIt);
			$("#search-key").keydown(function (e) {
				if (e.keyCode == 13) {
					submitIt();
				}
			});
			request();
		};
		init();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
			设备ID：
			<input id="search-key" type="text" class="form-control" name="deviceId" ng-model="REQUEST.deviceId" />
			状态：<select name="state" class="form-control" ng-model="REQUEST.state">
				<option value="0">请选择</option>
				<option value="1">未处理</option>
				<option value="2">已处理</option>
			</select>
			<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 10%">时间</th>
				<th style="width: 10%">用户</th>
				<th style="width: 10%">宠物</th>
				<th style="width: 15%">设备</th>
				<th style="width: 15%">App</th>
				<th style="width: 20%">问题</th>
				<th style="width: 10%">状态</th>
				<th style="width: 10%" data-l10n-id="operations"></th>
			</tr>
			<tr class="template">
				<td>{{_createdAt}}</td>
				<td>{{userId}}</td>
				<td><a href="#/p3/data?petId={{petId}}&day={{_day}}">{{petId}}</a></td>
				<td>{{deviceId}}&nbsp;&nbsp;H{{hardware}}，F{{firmware}}，B{{battery}}</td>
				<td>{{client.platform}},{{client.name}}</td>
				<td>{{_problem}}</td>
				<td>{{_state}}</td>
				<td class="operations">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li data-op-display="fix{{state}}">
								<a data-op-type="fix" data-op-key="{{id}}">标记已处理</a>
							</li>
							<li data-op-display="unfix{{state}}">
								<a data-op-type="unfix" data-op-key="{{id}}">标记未处理</a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>