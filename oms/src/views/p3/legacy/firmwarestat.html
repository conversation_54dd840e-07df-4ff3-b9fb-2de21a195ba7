<script src="vendor/highchart/highcharts.min.js"></script>
<script>
App.ready(function(){
	var form = $('#form1');
	var loadHardwares = function() {
		return App.api('/firmware/hardwares', null, {
			success: function(hws) {
				var select = form.find('[name="hardware"]');
				select.empty();
				$.each(hws, function(index, item) {
					select.append('<option value="'+item.id+'">'+item.name+'</option>');
				});
				var hardwareId = App.router.getParameter('hardware');
				if (!hardwareId) {
					hardwareId = '';
				}
				select.val(hardwareId);
			}
		});
	};
	var stat = function(){
		App.router.go(App.router.getCurrentPath(), form.serializeObject());
	};
	
	var doStat = function() {
		var params = $.extend(form.serializeObject(), App.router.getParameters());
		App.api('/firmware/firmwarestat', params).then(function(data){
			var values = [];
			var totalCount = 0;
			for(var i=0;i<data.length;i++){
				var item = data[i];
				values.push(['固件'+item.version,item.count]);
				totalCount+=item.count;
			}
			var options = {
			        chart: {
			            type: 'pie'
			        },
			        title: {
			            text: '固件报表（设备总数：' + totalCount + '）',
			        },
			        series: [{
			            name: '总数',
			            data: values
			        }],
			        tooltip: {
			        	formatter: function () {
			                //if (this.point.options.extraValue) return 'name: ' + this.key + ' <br/>y:' + this.y + '<br/>Extra value' + this.point.options.extraValue;
			                return this.key + ': 总数<b>' + this.y + '</b>' + '，占比：' + this.point.percentage.toFixed(2) + '%';
			            }
				    	//pointFormat: '固件{series.name}: <b>{point.percentage:.1f}%</b>'
				    }
			};
			$('#chart1').highcharts(options);
		});
	};
	
	loadHardwares().then(function(){
		$('#stat-btn').click(stat);
		doStat();
	});
});
</script>

<form id="form1" class="form-inline">
		硬件：<select name="hardware" class="form-control"></select>
		<a href="javascript:;" id="stat-btn" class="btn btn-primary">查看报表</a>
</form>
<div id="chart-wrap">
	<div id="chart1"></div>
</div>