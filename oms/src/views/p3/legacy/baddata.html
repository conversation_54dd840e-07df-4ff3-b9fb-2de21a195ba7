<script>
	App.ready(function () {
		App.menu.select('/pet/pets');
		var dogId = App.router.getParameter('dogId');
		var request = function (offset) {
			App.scrollToTop();
			var params = App.router.getParameters();
			params = $.extend({
				offset: U.isNull(offset) ? 0 : offset,
				limit: 5
			}, params);
			App.api('/p3/baddata', params).then(function (ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
						+ table.find('tr.template')
							.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
			});
		};
		var init = function () {
			$('#back-btn').html('&lt;&lt;' + dogId);
			$('#back-btn').click(function () {
				App.router.go('/pet/pets', { 'id': dogId });
			});
			request();
		};
		init();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<a href="javascript:;" id="back-btn" class="btn btn-primary" data-l10n-id="back"></a>
	</div>
	<div class="panel-body">
		<div class="table-responsive">
			<table class="table table-hover x-table" id="the-table">
				<tr class="header">
					<th style="width: 50%">时间</th>
					<th style="width: 50%">数据</th>
				</tr>
				<tr class="template">
					<td>{{_createdAt}}</td>
					<td><textarea>{{data}}</textarea></td>
				</tr>
			</table>
		</div>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>