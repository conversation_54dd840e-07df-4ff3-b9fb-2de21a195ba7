<script>
    App.ready(function () {
        var requestParams = $.extend({'limit': 10}, App.router.getParameters());
        var form = $('#search-form');
        var request = function (offset) {
            App.scrollToTop();
            if (!U.isNull(offset)) {
                requestParams.offset = offset;
            }
            App.api('/lock/types', requestParams).then(
                function (ps) {
                    var list = ps.items || [];
                    var table = $('#the-table');
                    var template = table
                        .attr('data-template');
                    if (!template) {
                        template = '<tr>'
                            + table.find('tr.template')
                                .html() + '</tr>';
                        table.attr('data-template',
                            template);
                    }
                    table.find('tr:not(.header)').remove();
                    for (var i = 0; i < list.length; i++) {
                        var context = list[i];
                        var row = U.template(template, context);
                        table.append(row);
                    }
                    initOperations();
                    App.pagination(ps, request);
                });
        };
        var init = function () {
            $('#export-btn').attr('href', App.apiPath('/schedule/type_string_out', App.specialApiParams()));
            $('#export-btn').attr('target', '_blank');
            request();
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <a href="#/lock/lockarea_import" id="import-btn" class="btn btn-warning">导入出库记录</a>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 30%">导入日期</th>
                <th style="width: 70%">出库记录</th>
            </tr>
            <tr class="template">
                <td>{{createdAt}}</td>
                <td>{{importResult}}</td>
            </tr>
        </table>
    </div>
</div>