<script src="js/miscuploader.js"></script>
<script>
App.ready(function(){
	// App.menu.select('/system/lockarea');
	var params = App.router.getParameters();
	var submit = function() {
		App.uploadFile({
			fileElement: $('#file')[0],
			url: '/lock/lock_area',
			data: params,
			success: function (result){
				alert(result);
			}
		});
	};
	$('#submit-btn').click(submit);
});
</script>

<form id="form1" role="form" onsubmit="return false;">
	<div class="form-group">
		<label>选择文件：（*仅支持Excel2003及以前版本的.xls文件格式）</label>
		<input type="file" id="file" name="file" class="form-control" />
	</div>
	<a id="submit-btn" class="btn btn-primary">导入</a>
	<a href="javascript:window.history.back()" class="btn btn-info btn-back">返回</a>
</form>