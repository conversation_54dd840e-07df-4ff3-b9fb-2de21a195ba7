<script>
    App.ready(function () {
        var requestParams = $.extend({'limit': 20, 'state': 2}, App.router.getParameters());
        var searchForm = $('#search-form');
        var request = function (offset) {
            App.scrollToTop();
            if (!mars.isNull(offset)) {
            	requestParams.offset = offset;
            }
            App.api('/coin/gifts', requestParams).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._img = context.img ? '<a href="'+context.img+'" target="_blank"><img src="' +
                    			App.previewImgUrl(context.img, 120, 120) + '"/></a>':  null;
                    var _validFromThru = '';
                    if (context.validFrom) {
                    	_validFromThru += '从';
                    	_validFromThru += U.date.format(U.date.parse(context.validFrom), 'yyyy-MM-dd');
                    }
                    if (context.validThru) {
                        _validFromThru += '到';
                        _validFromThru += U.date.format(U.date.parse(context.validThru), 'yyyy-MM-dd');
                    }
                    var _chargeLimit = '';
                    if (context.limitDays) {
                        _chargeLimit =context.limitDays+ '天';
                    }
                    if (context.limitNum) {
                        _chargeLimit +=context.limitNum+ '件';
                    }
                    context._chargeLimit=_chargeLimit;
                    context._validFromThru = _validFromThru;
                    context._updateStateOp = context.state > 1 ? '下架' : '上架';
                    var row = U.template(template, context);
                    table.append(row);
                }
                initOperationButtons();
                App.pagination(ps, request);
            });
        };
        var initOperationButtons = function() {
        	$('[data-op-type=remove]').click(function(){
        		remove($(this).attr('data-op-key'));
        	});
        	$('[data-op-type=updateStock]').click(function(){
        		updateStock($(this).attr('data-op-key'));
        	});
          	$('[data-op-type=updateState]').click(function(){
          		updateState($(this).attr('data-op-key'), $(this).attr('data-op-extra'));
        	});
        };
        var updateStock = function(id) {
        	var amount = prompt('请输入新库存', '');
        	amount = parseInt(amount);
        	if (isNaN(amount)) {
        		return;
        	}
            App.api('/coin/gift_update_stock', {'id': id, 'amount': amount}).then(function () {
                alert('操作成功');
                request();
            });
        };
        var updateState = function (id, currentState) {
        	currentState = parseInt(currentState);
        	var tips = currentState > 1 ? '确定要下架这个礼品券吗？' : '确定要上架这个礼品券吗？';
        	if (!confirm(tips)) {
        		return;
        	}
        	var newState = currentState + 1;
        	if (newState > 2) {
        		newState = 1;
        	}
            App.api('/coin/gift_sale', {'id': id, 'state': newState}).then(function () {
                alert('操作成功');
                request();
            });
        };
        var remove = function (id) {
        	if (!confirm('确定要删除这个礼品券吗？')) {
        		return;
        	}
            App.api('/coin/gift_remove', {'id': id}).then(function () {
                alert('已删除');
                request();
            });
        };
        var init = function() {
            searchForm.renderModel(requestParams);
            searchForm.find('[name=state]').change(function(){
            	requestParams = $.extend(requestParams, searchForm.serializeObject());
            	request(0);
            });
            request();
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
    	 <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
            <select name="state" ng-model="state" class="form-control">
            	<option value="1">未上架</option>
            	<option value="2">已上架</option>
            </select>
       	    <a href="#/coin/gift" id="create-btn" class="btn btn-warning fr">+添加礼品券</a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th width="10%">图片</th>
                <th width="10%">名称</th>
                <th width="10%">概要</th>
                <th width="10%">优先级</th>
                <th width="10%">消耗积分</th>
                <th width="5%">过期天数</th>
                <th width="20%">有效期</th>
                <th width="10%">兑换限制</th>
                <th width="5%">库存</th>
                <th width="10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{_img}}</td>
                <td>{{title}}</td>
                <td>{{summary}}</td>
                <td>{{priority}}</td>
                <td>{{coin}}</td>
                <td>{{expireDays}}</td>
                <td>{{_validFromThru}}</td>
                <td>{{_chargeLimit}}</td>
                <td>{{amount}}</td>
				<td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu  dropdown-menu-right">
                            <li>
                                <a href="#/coin/gift?id={{id}}">编辑</a>
                            </li>
                            <li>
                            	<a href="javascript:;" data-op-type="updateStock" data-op-key="{{id}}">修改库存</a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="updateState" data-op-key="{{id}}" data-op-extra="{{state}}">{{_updateStateOp}}</a>
                            </li>
                           <!--  <li>
                                <a href="javascript:;" data-op-type="remove" data-op-key="{{id}}">删除</a>
                            </li> -->
                        </ul>
                    </div>
				  </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>