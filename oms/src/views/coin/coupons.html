<script>
    App.ready(function () {
        var requestParams = $.extend({'limit': 20}, App.router.getParameters());
        var splitCode = function(code) {
        	var s = '';
        	for (var i = 0; i<code.length; i++) {
        		var c = code.charAt(i);
        		if (i == 4) {
        			s += '-';
        		}
        		s += c;
        	}
        	return s;
        };
        var request = function (offset) {
            App.scrollToTop();
            if (!mars.isNull(offset)) {
            	requestParams.offset = offset;
            }
            App.api('/coin/coupons', requestParams).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd');
                    context._expire = U.date.format(U.date.parse(context.expire), 'yyyy-MM-dd');
                    if (context.state == 3) {
                    	 context._expire = '<font color="red">' + context._expire + '</font>';
                    }
                    context._used = context.used > 0 ? '<font color="red">已使用</font>' : '<font color="blue">未使用</font>';
                    context._code = splitCode(context.code);
                    var row = U.template(template, context);
                    table.append(row);
                }
                initOperationButtons();
                App.pagination(ps, request);
            });
        };
        var initOperationButtons = function() {
        	$('[data-op-type=use]').click(function(){
        		useCoupon($(this).attr('data-op-key'));
        	});
        };
        var useCoupon = function (id) {
        	if (!confirm('确定要使用这个优惠券吗？')) {
        		return;
        	}
            App.api('/coin/coupon_use', {'id': id}).then(function () {
                alert('操作成功');
                request();
            });
        };
        var submitIt = function () {
        	requestParams = $.extend(requestParams, $('#search-form').serializeObject());
        	request(0);
        };
        var init = function() {
        	  $('#search-btn').click(submitIt);
              $("#search-key").keydown(function (e) {
                  if (e.keyCode == 13) {
                      submitIt();
                  }
              });
        };
        init();
    });
</script>
<style>
li[data-used=1] {
	display: none;
}
#search-key {
	width: 300px;
}
</style>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
    	 <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
            <select name="type" ng-model="REQUEST.type" class="form-control">
            	<option value="0">券号</option>
            	<option value="1">绑定用户</option>
            </select>
            <input name="s" ng-model="REQUEST.s" type="text" class="form-control" id="search-key"/>
       	    <a href="javascript:;" id="search-btn" class="btn btn-primary">搜索</a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th>券号</th>
                <th>绑定用户</th>
                <th>礼品</th>
                <th>过期时间</th>
                <th>生成时间</th>
                <th>状态</th>
                <th data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{_code}}</td>
                <td><a href="#/user/users?username={{owner.id}}">{{owner.id}}</a></td>
                <td>{{gift.title}}/{{gift.summary}}</td>
                <td>{{_expire}}</td>
                <td>{{_createdAt}}</td>
                <td>{{_used}}</td>
				<td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li data-used="{{used}}">
                                <a href="javascript:;" data-op-type="use" data-op-key="{{code}}">使用</a>
                            </li>
                        </ul>
                    </div>
				  </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>