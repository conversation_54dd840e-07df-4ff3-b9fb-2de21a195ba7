<link rel="stylesheet" type="text/css" href="vendor/datetimepicker/bootstrap-datetimepicker.css" />
<script src="vendor/moment.js"></script>
<script src="vendor/datetimepicker/bootstrap-datetimepicker.js"></script>
<script src="vendor/datetimepicker/locales/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<style type="text/css">
#imgs-preview .item {
	position: relative;
	width: 320px;
	margin: 10px 0 0 0;
}
#imgs-preview .item .tclose {
	top: -15px;
	right: -15px;
}
#imgs-preview .item img {
	width: 320px;
}
</style>
<script>
	App.ready(function() {
		//menu
		App.menu.select('/coin/gifts');
		var dateKeys = ['validFrom', 'validThru'];
		var form = $('#save-form');
		var addImg = function(url) {
			var preview = $('<img/>');
			preview.attr('src', url);
			var wrap = $('<div class="item"/>');
			var close = $('<div class="tclose"/>');
			close.click(function() {
				$(this).parent().remove();
			});
			wrap.append(close);
			wrap.append(preview);
			$('#imgs-preview').empty().append(wrap);
		};
		var save = function() {
			var imgs = [];
			$('#imgs-preview').find('img').each(function() {
				imgs.push(this.src);
			});
			form.find('[name=img]').val(imgs.join(','));
			var data = form.serializeObject();
			$.each(dateKeys, function(index, key) {
				var raw = form.find('[name="' + key + '"]').val();
				data[key] = U.date.formatISO8601(U.date.parse(raw));
			});
			App.api('/coin/gift_save', data, {
				success : function(model) {
					App.router.go('/coin/gifts', {'state': model.state});
				}
			});
		};
		var renderModel = function(model) {
			$.each(dateKeys, function(index, key) {
				model[key] = U.date.format(U.date.parse(model[key]), 'yyyy-MM-dd');
			});
			form.renderModel(model);
			var imgs = model.img ? model.img.split(',') : [];
			for (var i = 0; i < imgs.length; i++) {
				addImg(imgs[i]);
			}
		};
		var findModel = function() {
			var id = App.router.getParameter("id");
			if (id) {
				return App.api('/coin/gift', {'id': id}, {
					success : function(model) {
						renderModel(model);
					}
				});
			} else {
				return $.when(function(){
					renderModel({'priority':0});
				});
			}
		};
		var init = function() {
			findModel().then(function(){
				App.upload.init({
					namespace:'coin-gift',
					type:'image',
					id: 'upload-btn',
					fileUploaded: function (info) {
						if (typeof info == 'string') {
							info = JSON.parse(info);
						}
						addImg(info.url);
					},
					error: function (errTip) {
						alert('文件上传出错: ' + errTip);
					}
				});
				$.each(dateKeys, function(index, key) {
					form.find('[name="' + key + '"]').datetimepicker({
						'language' : 'zh-CN',
						'autoclose': true,
						'minView': 'day'
					});
				});
				$('#save-btn').click(save);
			});
		};
		
	    init();
	});
</script>

<div class="panel panel-default x-panel">
	<div class="panel-body">
		<form id="save-form" role="form" onsubmit="return false;">
			<input type="hidden" name="id" ng-model="id" />
			<input type="hidden" name="state" ng-model="state" />
			<div class="form-group">
				<label>名称*：</label>
				<div class="controls">
					<input name="title" ng-model="title" type="text" class="form-control" />
				</div>
			</div>
			<div class="form-group">
				<label>概要*：</label>
				<div class="controls">
					<input name="summary" ng-model="summary" type="text" class="form-control" />
				</div>
			</div>
			<div class="form-group">
				<label>详细：</label>
				<div class="controls">
					<textarea name="detail" ng-model="detail" class="form-control" rows="5"></textarea>
				</div>
			</div>
			<div class="form-group">
				<label>图片*：</label>
				<div>
					<a href="javascript:;" id="upload-btn">+选择上传文件</a>
				</div>
				<div class="controls postimgs">
					<input name="img" ng-model="img" type="hidden"/>
					<div id="imgs-preview"></div>
				</div>
			</div>
			<div class="form-group">
				<label>优先级*(越高显示在商城的越前面)：</label>
				<div class="controls">
					<input name="priority" ng-model="priority" type="text" class="form-control" />
				</div>
			</div>
			<div class="form-group">
				<label>需消耗积分*：</label>
				<div class="controls">
					<input name="coin" ng-model="coin" type="text" class="form-control" />
				</div>
			</div>
			<div class="form-group form-inline">
				<label>过期天数*：</label>
				<div class="controls">
					<input name="expireDays" ng-model="expireDays" type="text" class="form-control" />天
				</div>
			</div>
			<div class="form-group form-inline">
				<label>有效期：</label>
				<div class="controls">
					<input name="validFrom" ng-model="validFrom" type="text" class="form-control" data-date-format="YYYY-MM-DD"/>
					&nbsp;-&nbsp;
					<input name="validThru" ng-model="validThru" type="text" class="form-control" data-date-format="YYYY-MM-DD"/>
				</div>
			</div>
			<div class="form-group form-inline">
				<label>兑换间隔天数/单个用户兑换上限（不填则不限制）：</label>
				<div class="controls">
					<input name="limitDays" ng-model="limitDays" type="text" class="form-control"/>
					天&nbsp;-&nbsp;
					<input name="limitNum" ng-model="limitNum" type="text" class="form-control"/>件
				</div>
			</div>
			<div class="form-group">
				<label></label>
				<div class="controls">
					<a href="javascript:;" id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
				</div>
			</div>
		</form>
	</div>
</div>