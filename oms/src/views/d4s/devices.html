<script>
  App.ready(function () {
    var requestParams = $.extend({ limit: 10 }, App.router.getParameters());
    var request = function (offset) {
      App.scrollToTop();
      if (mars.isEmpty(requestParams.type) || mars.isEmpty(requestParams.s)) {
        return;
      }
      if (!U.isNull(offset)) {
        requestParams.offset = offset;
      }
      // App.api('/center/d4s/devices', requestParams).then(
      App.api("/d4s/devices", requestParams).then(function (ps) {
        var list = ps.items || [];
        var table = $("#the-table");
        var template = table.attr("data-template");
        if (!template) {
          template = "<tr>" + table.find("tr.template").html() + "</tr>";
          table.attr("data-template", template);
        }
        table.find("tr:not(.header)").remove();
        for (var i = 0; i < list.length; i++) {
          var context = list[i];
          context._createdAt = U.date.format(
            U.date.parse(context.createdAt),
            "yyyy-MM-dd HH:mm"
          );
          context._name = context.name ? context.name : "";
          context._pim =
            context.state && context.state.pim === 0 ? "离线" : "在线";
          context.result = JSON.stringify(list[i]).replaceAll('"', "$");
          var newTemplate = null;
          if (context.relation && context.relation.petIds) {
            var htmlPets = "";
            for (var pet in context.relation.petIds) {
              htmlPets +=
                "<a href='#/pet/pets?id=" +
                context.relation.petIds[pet] +
                "'>" +
                context.relation.petIds[pet] +
                "</a><br/>";
            }
            newTemplate =
              template.substring(0, template.indexOf("petId") + 7) +
              htmlPets +
              template.substring(template.indexOf("petId") + 7);
          } else {
            newTemplate = template;
          }

          var row = U.template(newTemplate, context);
          table.append(row);
        }
        initOperations();
        App.pagination(ps, request);
      });
    };
    var initOperations = function () {
      //解绑 or 绑定功能
      $("[data-op-type=link]").click(function () {
        link($(this).attr("data-op-key"), $(this).attr("data-op-user"));
      });

      //重启功能
      $("[data-op-type=rebot]").click(function () {
        rebot($(this).attr("data-op-key"));
      });

      //设置log等级setLog
      $("[data-op-type=setLog]").click(function () {
        setLog($(this).attr("data-op-key"));
      });

      //显示密钥
      $("[data-op-type=showSecret]").click(function () {
        showSecret($(this).attr("data-op-key"));
      });

      //显示详情
      $("[data-op-type=detail]").click(function () {
        detail($(this).attr("data-op-key"));
      });
    };
    var link = function (id, user) {
      if (!user) {
        var groupId = prompt("请输入家庭组ID", "");
        if (!groupId) {
          return;
        }
        if (!confirm(App.text("确定绑定此家庭到此设备？"))) {
          return;
        }
        // return App.api('/center/d4s/link', {id, groupId}).done(function () {
        return App.api("/d4s/link", { id, groupId }).done(function () {
          alert("绑定成功");
          request();
        });
      } else {
        if (!confirm(App.text("确定要解绑此设备？"))) {
          return;
        }
        // return App.api('/center/d4s/unlink', {'id': id}).done(function () {
        return App.api("/d4s/unlink", { id: id }).done(function () {
          alert("解绑成功");
          request();
        });
      }
    };

    var rebot = function (id) {
      if (!confirm(App.text("确定要重启设备？"))) {
        return;
      }
      // return App.api('/center/d4s/reboot', {'id': id}).done(function () {
      return App.api("/d4s/reboot", { id: id }).done(function () {
        alert("重启成功");
        request();
      });
    };

    var setLog = function (id) {
      var logId = prompt("设置Log等级", "");
      if (!logId) {
        return;
      }
      // return App.api('/center/d4s/log_level', {'id': id, 'level': logId}).done(function () {
      return App.api("/d4s/log_level", { id: id, level: logId }).done(
        function () {
          alert("设置成功");
          request();
        }
      );
    };

    var showSecret = function (secret) {
      alert(secret);
    };

    var detail = function (object) {
      var result = JSON.parse(object.replaceAll("$", '"'));
      result._createdAt = U.date.format(
        U.date.parse(result.createdAt),
        "yyyy-MM-dd HH:mm"
      );
      result._name = result.name ? result.name : "";
      result._pim = result.state.pim === 0 ? "离线" : "在线";
      result._manualLock = result.settings.manualLock === 0 ? "关闭" : "开启";
      var table = $("#example-table").html();
      var petIds = "";
      if (result.relation && result.relation.petIds) {
        for (var pet in result.relation.petIds) {
          petIds += result.relation.petIds[pet] + "<br/>";
        }
      }
      if (result.firmwareDetails) {
        console.log(result.firmwareDetails);
        result._module = result.firmwareDetails[0].module;
        result._version = result.firmwareDetails[0].version;
      }
      result.petIds = petIds;
      switch (result.state.ota) {
        case 1: {
          result._ota = "准备升级";
          break;
        }
        case 2: {
          result._ota = "升级中";
          break;
        }
        case 3: {
          result._ota = "升级完成";
          break;
        }
        case 4: {
          result._ota = "升级失败";
          break;
        }
        case 5: {
          result._ota = "设备离线未开始升级";
          break;
        }
        default: {
          result._ota = "未知";
          break;
        }
      }
      console.log(result);
      var row = U.template(table, result);
      $("#detail-table").html("");
      $("#detail-table").append(row);

      $("#myModal").modal("show");
    };

    var submitIt = function () {
      var data = $("#search-form").serializeObject();
      App.router.go(App.router.getCurrentPath(), data);
    };
    var init = function () {
      $("#search-btn").click(submitIt);
      $("#search-key").keydown(function (e) {
        if (e.keyCode == 13) {
          submitIt();
        }
      });
      request();
    };
    init();
  });
</script>

<div class="panel panel-default x-panel">
  <div class="panel-heading">
    <form
      id="search-form"
      class="form-inline"
      action="#"
      method="get"
      onsubmit="return false;"
    >
      <select class="form-control" name="type" ng-model="REQUEST.type">
        <option value="id">设备ID</option>
        <option value="mac">Mac</option>
        <option value="sn">SN</option>
      </select>
      <input
        id="search-key"
        type="text"
        class="form-control"
        name="s"
        ng-model="REQUEST.s"
      />
      <a
        href="javascript:;"
        id="search-btn"
        class="btn btn-primary"
        data-l10n-id="search"
      ></a>
    </form>
  </div>
  <div class="panel-body">
    <table class="table table-hover x-table" id="the-table">
      <tr class="header">
        <th style="width: 8%">id</th>
        <th style="width: 10%">MAC</th>
        <th style="width: 10%">SN</th>
        <th style="width: 10%">注册时间</th>
        <th style="width: 8%">硬/固件</th>
        <th style="width: 5%">时区</th>
        <th style="width: 8%">区域</th>
        <th style="width: 8%">主人ID</th>
        <th style="width: 8%">所属家庭</th>
        <th style="width: 10%">状态</th>
        <th style="width: 5%" data-l10n-id="operations">操作</th>
      </tr>
      <tr class="template">
        <td>{{id}}</td>
        <td>{{mac}}</td>
        <td>{{sn}}</td>
        <td>{{_createdAt}}</td>
        <td>{{hardware}}/{{firmware}}</td>
        <td>{{timezone}}</td>
        <td>{{locale}}</td>
        <td>
          <a href="#/user/users?username={{relation.userId}}"
            >{{relation.userId}}</a
          >
        </td>
        <td><a href="#/user/family?groupId={{familyId}}">{{familyId}}</a></td>
        <td>{{_pim}}</td>
        <td class="operations">
          <div class="dropdown">
            <a
              class="dropdown-toggle"
              data-toggle="dropdown"
              href="javascript:;"
            >
              <span data-l10n-id="operations"></span>
              <b class="caret"></b>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
              <li>
                <a href="javascript:;" data-op-type="rebot" data-op-key="{{id}}"
                  >重启设备</a
                >
              </li>
              <li>
                <a
                  href="javascript:;"
                  data-op-type="setLog"
                  data-op-key="{{id}}"
                  >设置Log等级</a
                >
              </li>
              <li>
                <a
                  href="javascript:;"
                  data-op-type="detail"
                  data-op-key="{{result}}"
                  >设备详情</a
                >
              </li>
              <li>
                <a
                  href="javascript:;"
                  data-op-type="showSecret"
                  data-op-key="{{secret}}"
                  >显示秘钥</a
                >
              </li>
              <li>
                <a
                  href="javascript:;"
                  data-op-type="link"
                  data-op-key="{{id}}"
                  data-op-user="{{relation}}"
                  >解绑/绑定</a
                >
              </li>
              <li><a href="#/d4s/link_history?id={{id}}">绑定历史</a></li>
              <!--                            <li><a href="javascript:;" data-op-type="log" data-op-key="{{id}}">Log</a></li>li-->
            </ul>
          </div>
        </td>
      </tr>
    </table>
  </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>

<div
  class="modal fade"
  tabindex="-1"
  role="dialog"
  style="display: none"
  id="myModal"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button
          type="button"
          class="close"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">设备详情</h4>
      </div>
      <div class="modal-body">
        <table class="table table-hover x-table" id="detail-table"></table>
        <table style="display: none" id="example-table">
          <tr>
            <td>ID</td>
            <td>{{id}}</td>
          </tr>
          <tr>
            <td>MAC</td>
            <td>{{mac}}</td>
          </tr>
          <tr>
            <td>SN</td>
            <td>{{sn}}</td>
          </tr>
          <tr>
            <td>注册时间</td>
            <td>{{_createdAt}}</td>
          </tr>
          <tr>
            <td>硬/固件</td>
            <td>{{hardware}}/{{firmware}}</td>
          </tr>
          <tr>
            <td>模块</td>
            <td>{{_module}}:{{_version}}</td>
          </tr>
          <tr>
            <td>时区</td>
            <td>{{timezone}}</td>
          </tr>
          <tr>
            <td>主人ID</td>
            <td>{{relation.userId}}</td>
          </tr>
          <tr>
            <td>宠物ID</td>
            <td>{{petIds}}</td>
          </tr>
          <tr>
            <td>状态</td>
            <td>{{_pim}}</td>
          </tr>
          <tr>
            <td>OTA状态</td>
            <td>{{_ota}}</td>
          </tr>
          <tr>
            <td>密钥</td>
            <td>{{secret}}</td>
          </tr>
          <tr>
            <td>区域</td>
            <td>{{locale}}</td>
          </tr>
          <tr>
            <td>儿童锁开关</td>
            <td>{{_manualLock}}</td>
          </tr>
          <tr>
            <td>ssid</td>
            <td>{{state.wifi.ssid}}</td>
          </tr>
          <tr>
            <td>信号强度</td>
            <td>{{state.wifi.rsq}}</td>
          </tr>
        </table>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">
          关闭
        </button>
      </div>
    </div>
    <!-- /.modal-content -->
  </div>
  <!-- /.modal-dialog -->
</div>
