<script>
  App.ready(function () {
    var requestParams = $.extend({'limit': 10}, App.router.getParameters());

    //弹出模态框
    $("#create").click(function(){
      console.log(222)
      $("#myModal").modal('show')
    })

    //创建SN
    $("#snCreate").click(function (){
      console.log($("#sn").val());
      // return App.api('/center/d4s/sn_save', {'sn': $("#sn").val(), 'mac': $("#MAC").val(),'chipId':$("CHIPID").val()}).done(function() {
      return App.api('/d4s/sn_save', {'sn': $("#sn").val(), 'mac': $("#MAC").val(),'chipId':$("CHIPID").val()}).done(function() {
        alert('创建成功');
        $("#myModal").modal('hide')
        $("#sn").val('')
        $("#MAC").val('')
        $("#CHIPID").val('')
      });
    })

    var request = function (offset) {
      App.scrollToTop();
      if (mars.isEmpty(requestParams.type) || mars.isEmpty(requestParams.s)) {
        return;
      }
      if (!U.isNull(offset)) {
        requestParams.offset = offset;
      }
      // App.api('/center/d4s/sn_list', requestParams).then(
      App.api('/d4s/sn_list', requestParams).then(
              function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table
                        .attr('data-template');
                if (!template) {
                  template = '<tr>'
                          + table.find('tr.template')
                                  .html() + '</tr>';
                  table.attr('data-template',
                          template);
                }
                table.find('tr:not(.header)').remove();

                for (var i = 0; i < list.length; i++) {
                  var context = list[i];
                  context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
                  var row = U.template(template, context);
                  table.append(row);
                }
                initOperations();
                App.pagination(ps, request);
              });
    };
    var initOperations = function () {
      //删除功能
      $('[data-op-type=delete]').click(function () {
        deleteSn($(this).attr('data-op-key'));
      });

    };
    var deleteSn = function (sn) {
        if (!confirm(App.text('确定要删除序列号？'))) {
          return;
        }
        // return App.api('/center/d4s/sn_remove', {'sn': sn}).done(function () {
        return App.api('/d4s/sn_remove', {'sn': sn}).done(function () {
          alert('删除成功');
          request();
        });
    };



    var submitIt = function () {
      var data = $('#search-form').serializeObject();
      App.router.go(App.router.getCurrentPath(), data);
    };
    var init = function () {
      $('#search-btn').click(submitIt);
      $("#search-key").keydown(function (e) {
        if (e.keyCode == 13) {
          submitIt();
        }
      });
      request();
    };
    init();
  });
</script>

<div class="panel panel-default x-panel">
  <div class="panel-heading">
    <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
      <select class="form-control" name="type" ng-model="REQUEST.type">
        <option value="mac">Mac</option>
        <option value="sn">SN</option>
      </select>
      <input id="search-key" type="text" class="form-control" name="s" ng-model="REQUEST.s"/>
      <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
      <a href="javascript:;" id="create" class="btn btn-danger" data-l10n-id="create"></a>
    </form>
  </div>
  <div class="panel-body">
    <table class="table table-hover x-table" id="the-table">
      <tr class="header">
        <th style="width: 30%">SN</th>
        <th style="width: 30%">MAC</th>
        <th style="width: 30%">CHIPID</th>
        <th style="width: 10%" data-l10n-id="operations">操作</th>
      </tr>
      <tr class="template">
        <td>{{sn}}</td>
        <td>{{mac}}</td>
        <td>{{chipId}}</td>

        <td class="operations">
          <div class="dropdown">
            <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;"> <span
                    data-l10n-id="operations"></span>
              <b class="caret"></b>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
              <li><a href="javascript:;" data-op-type="delete" data-op-key="{{sn}}">删除</a></li>
            </ul>
          </div>
        </td>
      </tr>
    </table>
  </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>

<div class="modal fade" tabindex="-1" role="dialog" style="display: none" id="myModal">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">创建序列号</h4>
      </div>
      <div class="modal-body">
        <form>
          <div class="form-group">
            <label for="sn">SN</label>
            <input type="text" class="form-control" id="sn" placeholder="SN">
          </div>
          <div class="form-group">
            <label for="MAC">MAC</label>
            <input type="text" class="form-control" id="MAC" placeholder="MAC">
          </div>

          <div class="form-group">
            <label for="CHIPID">CHIPID</label>
            <input type="text" class="form-control" id="CHIPID" placeholder="CHIPID">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-danger" id="snCreate">创建</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>