<script>
App.ready(function(){
	var remove = function(id) {
		if (!confirm(App.text('confirm.delete'))) {
			return;
		}
		App.api('/app/apiuser_remove', {'id': id}).then(function(){
			window.location.reload();
		});
	};
	var request = function(offset) {
		App.scrollToTop();
		App.api('/app/apiusers',	{
			's' : App.router.getParameter('s'),
			'offset' : U.isNull(offset) ? 0 : offset,
			'limit' : 10
		}).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for ( var i = 0; i < list.length; i++) {
					var context = list[i];
					var avatar = App.previewImgUrl(context.avatar, 120, 120);
					context._avatar = avatar ? U.template('<a href="{{avatar}}"><img src="'+avatar+'"/></a>', context) : null;
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
		});
	};
	var submitIt = function() {
		App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
	};
	var add = function() {
		var userId = prompt('请输入用户ID', '');
		if (!userId) {
			return;
		}
		App.api('/app/apiuser_save', {'userId': userId}).then(function(){
			window.location.reload();
		});
	};
	var init = function() {
		window.remove = remove;
		$('#search-btn').click(submitIt);
		$('#add-btn').click(add);
		$("#search-key").keydown(function(e){
		    　if(e.keyCode == 13) {
		    	submitIt();
		    　}
		});
		request();
	};
	
	init();
});
</script>
<div class="panel panel-default x-panel">
<div class="panel-heading">
	<form id="search-form" class="form-inline" action="#" onsubmit="return false;">
		<input type="text" id="search-key" class="form-control" name="s" ng-model="REQUEST.s"/>
		<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
		<a href="javascript:;" id="add-btn" class="btn btn-success" data-l10n-id="add"></a>
	</form>
</div>
<div class="panel-body"><div class="table-responsive">
<table class="table table-hover x-table" id="the-table">
	<tr class="header">
		<th style="width: 20%">ID</th>
		<th style="width: 30%">头像</th>
		<th style="width: 30%">昵称</th>
		<th style="width: 20%">操作</th>
	</tr>
	<tr class="template">
		<td>{{id}}</td>
		<td>{{_avatar}}</td>
		<td>{{nick}}</td>
		<td class="operations">
			<div class="dropdown">
				<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;"><span data-l10n-id="operations"></span><b class="caret"></b></a>
				<ul class="dropdown-menu dropdown-menu-right">
                     <li><a href="javascript:remove('{{id}}');" data-l10n-id="delete"></a></li>
				</ul>
			</div>
		</td>
	</tr>
</table>
</div></div></div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>