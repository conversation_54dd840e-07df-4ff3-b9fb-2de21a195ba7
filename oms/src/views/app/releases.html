<script>
	App.ready(function() {
		App.menu.select('/app/apps');
		var appId = App.router.getParameter('appId');

		var review = function(id, approved) {
			var declineNotes = null;
			if (approved) {
				if (!confirm('确定审核通过吗？')) {
					return;
				}
			} else {
				if (!confirm('确定要拒绝吗？')) {
					return;
				}
				declineNotes = prompt('请输入拒绝理由', '');
				if (!declineNotes) {
					return;
				}
			}
			App.api('/app/reviewrelease', {
				'id' : id,
				'approved' : approved,
				'declineNotes' : declineNotes
			}).then(function() {
				window.location.reload();
			});
		};

		var publish = function(id) {
			if (!confirm('确定要发布吗？')) {
				return;
			}
			App.api('/app/salerelease', {
				'id' : id
			}).then(function() {
				window.location.reload();
			});
		};

		var remove = function(id) {
	        if (!confirm(App.text('confirm.delete'))) {
	            return;
	        }
			App.api('/app/removerelease', {
				'id' : id
			}).then(function() {
				window.location.reload();
			});
		};
		
		var releaseStatus = function(state) {
			if (state == 1) {
				return '提交审核';
			} else if (state == 2) {
				return '审核通过';
			} else if (state == 3) {
				return '已上线';
			} else if (state == 4) {
				return '审核不通过';
			} else {
				return '';
			}
		};

		var request = function(offset) {
			App.scrollToTop();
			App.api('/app/releases', {
				'appId' : App.router.getParameter('appId'),
				'offset' : U.isNull(offset) ? 0 : offset,
				'limit' : 10
			}).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					context._status = releaseStatus(context.status);
					var notes = context.releaseNotes;
					var note = null;
					for ( var locale in notes) {
						note = notes[locale];
						break;
					}
					context._releaseNotes = note;
					context._popupPrompt = context.popupPrompt === 0 ? "否" : "是";
					var row = U.template(template, context);
					if(appId.indexOf("android") > -1) {
						row = row.replace("hidden", "");
					}
					table.append(row);
				}
				toggleDisplay();
				if(appId.indexOf("android") === -1) {
					document.getElementById("popup_prompt").style.display='none';
				}
				App.pagination(ps, request);
			});
		};

		var toggleDisplay = function() {
			$('[data-status-wrap]').each(function() {
				var parent = $(this);
				var status = parent.attr('data-status-wrap');
				parent.find('[data-status]').each(function() {
					var child = $(this);
					var supportedStatusList = child.attr('data-status').split(',');
					for (var i = 0; i < supportedStatusList.length; i++) {
						var supportedStatus = supportedStatusList[i];
						if (supportedStatus == status) {
							child.removeClass('none');
							break;
						}
					}
				});
			});
		};

		var init = function() {
			request();
			$('#back-btn').click(function() {
				App.router.go('/app/apps', {
					's' : appId
				});
			});
			$('#create-btn').click(function() {
				App.router.go('/app/release', {
					'appId' : appId
				});
			});
			window.publish = publish;
			window.review = review;
			window.remove = remove;
		};

		init();
	});
</script>
<style>
.declinenotes {
	padding-left: 5px;
}
</style>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<a href="javascript:;" class="btn btn-primary" id="back-btn">Back</a>
		<a href="javascript:;" class="btn btn-success" id="create-btn">发布新版本</a>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 15%">版本号</th>
				<th style="width: 15%">状态</th>
				<th style="width: 40%">Release Notes</th>
				<th id="popup_prompt" style="width: 15%">是否弹窗提示</th>
				<th style="width: 15%">操作</th>
			</tr>
			<tr class="template">
				<td>{{version}}</td>
				<td>{{_status}}<span class="declinenotes">{{declineNotes}}</span></td>
				<td>{{_releaseNotes}}</td>
				<td hidden>{{_popupPrompt}}</td>
				<td class="operations">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu" data-status-wrap="{{status}}">
							<li>
								<a href="{{file.url}}" target="_blank">下载</a>
							</li>
							<li>
								<a href="#/app/release?id={{id}}" data-l10n-id="edit"></a>
							</li>
							<li>
								<a href="javascript:remove('{{id}}');" data-l10n-id="delete"></a>
							</li>
							<li data-status="1" class="none">
								<a href="javascript:review('{{id}}',true);">审核通过</a>
							</li>
							<li data-status="1" class="none">
								<a href="javascript:review('{{id}}',false);">审核不通过</a>
							</li>
							<li data-status="2" class="none">
								<a href="javascript:publish('{{id}}');">发布</a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>
