
<script>
	App.ready(function() {
		var form = $('#search-form');
		var request = function(offset) {
			App.scrollToTop();
			var params = App.router.getParameters();
			var type = params['type'];
			var u = params['u'];
			var api = params['api'];
			if (!u && !api) {
				return;
			}
			if (type == 'userId') {
				params['userId'] = u;
			} else if (type == 'username') {
				params['username'] = u;
			}
			delete params['type'];
			delete params['u'];
			params = $.extend({'limit' : 10}, params);
			params.offset = U.isNull(offset) ? params.offset : offset;
			App.api('/app/apilogs', params).then(function(ps){
				var users = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for ( var i = 0; i < users.length; i++) {
					var context = users[i];
					context._createdAt = U.date.format(U.date.parse(context.createdAt), 'MM-dd HH:mm');
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
			});
		};
		
		var submitIt = function() {
			var params = form.serializeObject();
			App.router.go(App.router.getCurrentPath(), params);
		};
		$('#search-btn').click(submitIt);
		$("#search-key").keydown(function(e){
		    　if(e.keyCode == 13) {
		    	submitIt();
		    　}
		});
		request();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
			<select name="type" class="form-control" ng-model="REQUEST.type">
				<option value="userId">用户ID</option>
				<option value="username">用户名</option>
			</select>
			<input type="text" class="form-control" name="u" ng-model="REQUEST.u" placeholder="用户ID/用户名" />
			<input type="text" class="form-control" name="api" ng-model="REQUEST.api" placeholder="api" />
			<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 10%">用户ID</th>
				<th style="width: 10%">用户名</th>
				<th style="width: 10%">Api</th>
				<th style="width: 10%">时间</th>
				<th style="width: 60%">内容</th>
			</tr>
			<tr class="template">
				<td>{{userId}}</td>
				<td>{{username}}</td>
				<td>{{api}}</td>
				<td>{{_createdAt}}</td>
				<td>{{message}}</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>