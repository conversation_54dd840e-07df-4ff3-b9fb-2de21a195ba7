<script>
	App.ready(function() {
		var request = function(offset) {
			App.scrollToTop();
			var params = $.extend({'limit' : 10}, App.router.getParameters());
			params.offset = (!U.isNull(offset)) ? offset : params.offset;
			App.api('/app/logs', params).then(function(ps){
				var users = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for ( var i = 0; i < users.length; i++) {
					var context = users[i];
					context.createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
					var row = U.template(template, context);
					table.append(row);
				}
				App.pagination(ps, request);
			});
		};
		
		var submitIt = function() {
			App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
		};
		$('#search-btn').click(submitIt);
		$("#search-key").keydown(function(e){
		    　if(e.keyCode == 13) {
		    	submitIt();
		    　}
		});
		request();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
			<input id="search-key" type="text" class="form-control" name="userId" ng-model="REQUEST.userId" placeholder="userId"/>
			<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 20%">用户</th>
				<th style="width: 20%">上传日期</th>
				<th style="width: 20%">文件大小</th>
				<th style="width: 40%">操作</th>
			</tr>
			<tr class="template">
				<td>{{userId}}</td>
				<td>{{createdAt}}</td>
				<td>{{file.size}}</td>
				<td class="operations"><a href="{{file.url}}" target="_blank">下载Log</a></td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>