<script>
App.ready(function() {
	App.menu.select('/app/apps');
	var form = $('#save-form');
	  var loadModel = function() {
		var id = App.router.getParameter('id');
		if (!id) {
			return;
		}
		return App.api('/app/app', {'id': id}, {
			success: function(model) {
				if ($.isEmptyObject(model)) {
					alert('The app does not exists or has been removed.');
					return false;
				}
				form.find('[name="id"]').attr('readonly', 'readonly');
				form.find('[name="platform"]').attr('readonly', 'readonly');
				form.renderModel({model: model});
			}
		});
	};
	var save = function() {
		var data = $('#save-form').serializeObject();
		var formData = {'app': JSON.stringify(data)};
 		App.api('/app/save', formData).then(function(){
 			App.router.go('/app/apps');
 		});
	};
	
	$.when(loadModel()).then(function(){
		$('#save-btn').click(save);
	});
});
</script>
<form id="save-form" role="form" onsubmit="return false;">
		<div class="form-group">
         	<label>ID：</label>
            <input type="text" name="id" class="form-control" ng-model="model.id"/>
            <p class="help-block">小写字母、数字、下划线组成</p>
        </div>
		<div class="form-group">
	    	<label>平台：</label>
	    	<select name="platform" class="form-control" ng-model="model.platform">
       			<option value="ios">iOS</option>
       			<option value="android">Android</option>
            </select>
	  	</div>
 	    <div class="form-group">
         	<label>最小版本：</label>
            <input type="text" name="minVersion" class="form-control" ng-model="model.minVersion"/>
            <p class="help-block">客户端版本小于此版本会要求强制升级</p>
       </div>
 	   <div class="form-group">
         	<label>外部app ID：</label>
            <input type="text" name="externalId" class="form-control" ng-model="model.externalId"/>
            <p class="help-block">iOS平台需要填写</p>
       </div>
        <div class="form-group">
         	<label>备注：</label>
            <textarea name="notes" class="form-control" rows="3" ng-model="model.notes"></textarea>
       </div>
       <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
       <a href="#/app/apps" class="btn btn-info" data-l10n-id="cancel"></a>
</form>