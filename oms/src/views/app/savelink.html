<script src="js/localesaver.js"></script>
<script>
	App.ready(function() {
		App.menu.select('/app/links');
		var form = $('#save-form');
		var id = App.router.getParameter('id');
        var localeSaver = new LocaleSaver({'dialogUrl': '/app/savelink_locale.html'});

		var loadModel = function() {
			if (!id) {
				return;
			}
			return App.api('/app/findlink', {'id' : id}).then(function(model){
				if ($.isEmptyObject(model)) {
					return;
				}
				$('#save-form').renderModel(model);
                localeSaver.setDataByModel(model, ['links']);
			});
		};
		
		var save = function() {
			var data = form.serializeObject();
            var localSpecs = localeSaver.getDataAsModel();
            for (var key in localSpecs) {
            	data[key] = localSpecs[key];
            }
			App.api('/app/savelink', data).then(function() {
				App.router.go('/app/links');
			});
		};

		localeSaver.init().then(loadModel).then(function() {
			if (id) {
				form.find('[name=id]').attr('readonly', 'readonly');
			}
			$('#save-btn').click(save);
		});
	});
</script>
<form id="save-form" role="form" onsubmit="return false;">
	<div class="form-group">
		<label>ID：</label> <input type="text" name="id" class="form-control" ng-model="id" />
	</div>
	<div class="form-group">
		<label>默认链接：</label> <input type="text" name="defaultLink" class="form-control" ng-model="defaultLink" />
	</div>
	<div class="form-group">
        <div class="controls">
            <a href="javascript:;" id="local-add-btn">
                <span>+添加其他语言链接</span>
            </a>
            <div id="locale-spec-template">
                <div class="locale-spec">
                    <div class="locale-spec-item">
                        <label data-l10n-id="locale"></label>:
                        <span class="locale">{{locale.name}}</span>
                        <a href="javascript:;" class="edit" data-l10n-id="edit" data-op-type="update" data-op-key="{{locale.key}}"></a>
                        <a href="javascript:;" class="edit" data-l10n-id="delete" data-op-type="remove" data-op-key="{{locale.key}}"></a>
                    </div>
                    <div class="locale-spec-item">
                    	<label>链接</label>:<span>{{spec.links}}</span>
                    </div>
                </div>
            </div>
            <div id="locale-specs"></div>
        </div>
    </div>
	<div class="form-group">
		<label>备注：</label> <input type="text" name="notes" class="form-control" ng-model="notes" />
	</div>
	<div class="form-group">
		 <a data-l10n-id="cancel" href="javascript:window.history.back();" class="btn"></a>
		 <a id="save-btn" data-l10n-id="save" href="javascript:;" class="btn btn-primary"></a>
	</div>
</form>