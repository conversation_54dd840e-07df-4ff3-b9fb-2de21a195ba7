<script src="js/localespec.js"></script>
<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>

<script>
    App.ready(function () {
        //menu
        App.menu.select('/app/apps');
        var form = $('#save-form');
        var addLocaleSpec = function (locale) {
            var spec = LocaleSpec.get(locale);
            App.openDialog({
                url: App.router.getRealPath('/app/releaselocalespec.html'),
                context: {spec: spec},
                noHeader: true,
                primaryClick: LocaleSpec.save,
                beforeRender: function (body) {
                    var select = body.find('[name="locale"]');
                    $.each(LocaleSpec.getLocaleList(), function (index, item) {
                        select.append('<option value="' + item.key + '">' + item.name + '</option>');
                    });
                }
            });
        };
        var previewBinary = function (result) {
            var preview = $('#binary-preview');
            if (result) {
                preview.html('<a href="' + result.url + '" target="_blank">' + App.text('download') + '(' + result.size + ' bytes)</a>');
            } else {
                preview.html('');
            }
            form.find('input[name="file.url"]').val(result ? result.url : '');
            form.find('input[name="file.size"]').val(result ? result.size : '');
        };
        var save = function () {
            var specs = LocaleSpec.getAll();
            var notes = {};
            for (var locale in specs) {
                var spec = specs[locale];
                var note = spec['releaseNotes'];
                notes[locale] = note;
            }
            var data = form.serializeObject();
            data['releaseNotes'] = notes;
            var formData = {'release': JSON.stringify(data)};
            App.api('/app/saverelease', formData).then(function () {
                App.router.go('/app/releases', {'appId': data['app']['id']});
            });
        };

        var requestToken = function (model) {
            App.upload.init({
                url: '/app/uploadreleasetoken',
                params:{
                    appId:model.app.id
                },
                id: 'upload-btn',
                fileUploaded: function (info) {
                    if (typeof info == 'string') {
                        info = JSON.parse(info);
                    }
                    previewBinary(info);
                },
                error: function (errTip) {
                    alert('文件上传出错' + errTip);
                }
            });
        };
        var showModel = function (model) {
            form.renderModel({model: model});
            previewBinary(model.file);
            var notes = model.releaseNotes || {};
            var specs = {};
            for (var locale in notes) {
                var spec = {'releaseNotes': notes[locale]};
                specs[locale] = spec;
            }
            LocaleSpec.setAll(specs);
            requestToken(model);
        };
        var loadModel = function () {
            var id = App.router.getParameter('id');
            if (!id) {
                var app = {'id': App.router.getParameter('appId')};
                showModel({'app': app});
                return;
            }
            return App.api('/app/release', {'id': id}, {
                success: function (model) {
                    if ($.isEmptyObject(model)) {
                        alert('The releases doest not exists or has been removed')
                        return false;
                    }
                    showModel(model);
                }
            });
        };
        //init
        LocaleSpec.loadLocales().then(loadModel).then(function () {
            //locale spec
            window.addLocaleSpec = addLocaleSpec;
            $('#add-locale-btn').click(addLocaleSpec);
            //init others
            //$('#binary-control').change(uploadBinary);
            $('#save-btn').click(save);
            $('#cancel-btn').click(function () {
                App.router.go('/app/apps', {s: form.find('[name="app.id"]').val()});
            });
        });
    });
</script>
<form id="save-form" role="form">
    <input type="hidden" name="id" ng-model="model.id"/>
    <input type="hidden" name="app.id" ng-model="model.app.id"/>
    <div class="form-group">
        <label>版本号</label>
        <input type="text" name="version" class="form-control" ng-model="model.version"/>
    </div>
    <div class="form-group">
        <label>文件</label>
        <input type="hidden" name="file.url" class="form-control"/>
        <input type="hidden" name="file.size" class="form-control"/>
        <!--  <input type="file" name="file" class="form-control" id="binary-control"/> -->
        <div id="binary-preview"></div>
        <a href="javascript:;" id="upload-btn">+选择上传文件</a>
    </div>
    <div class="form-group">
        <div class="controls">
            <a href="javascript:;" id="add-locale-btn">
                <span>+</span>
                <span data-l10n-id="localize"></span>
            </a>
            <div id="locale-spec-template">
                <div class="locale-spec" data-locale="{{locale.key}}">
                    <div class="locale-spec-item">
                        <label data-l10n-id="locale"></label>:
                        <span class="locale">{{locale.name}}</span>
                        <a href="javascript:addLocaleSpec('{{locale.key}}');" class="edit" data-l10n-id="edit"></a>
                        <a href="javascript:LocaleSpec.remove('{{locale.key}}');" class="edit"
                           data-l10n-id="delete"></a>
                    </div>
                    <div class="locale-spec-item"><label>ReleaseNotes</label>:
                        <span>{{spec.releaseNotes}}</span>
                    </div>
                </div>
            </div>
            <div id="locale-specs"></div>
        </div>
    </div>
    <div class="form-group">
        <label>是否弹窗提示</label>
        <select class="form-control" name="popupPrompt" ng-model="model.popupPrompt">
            <option value="1">是</option>
            <option value="0">否</option>
        </select>
    </div>
    <a id="save-btn" href="javascript:;" class="btn btn-primary" data-l10n-id="save"></a>
    <a id="cancel-btn" href="javascript:;" class="btn btn-info" data-l10n-id="cancel"></a>
</form>
