<script src="js/region-select.js"></script>
<script>
    App.ready(function () {
    	var searchForm = $('#search-form');
        var searchParams = searchForm.serializeObject();
        var remove = function (region) {
            if (!confirm(App.text('confirm.operation'))) {
                return;
            }
            var params = {'region': region};
            App.api('/app/module_remove', params).then(function () {
            	request(0);
            });
        };
        var request = function (offset) {
            App.scrollToTop();
            if (!U.isNull(offset)) {
            	searchParams.offset = offset;
            }
            App.api('/app/modules', searchParams).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    if (context.region === '0') {
                    	context._region = '默认';
                    } else {
                        var regionInfo = Regions.find(context.region);
                    	context._region =  regionInfo ? regionInfo.name + '(' + regionInfo.code + ')' : context.region;
                    }
                    context._modules = context.modules.join(', ');
                    var row = U.template(template, context);
                    table.append(row);
                }
                table.find('[data-op-type="remove"]').click(function(){
                	remove($(this).attr('data-op-key'));
                });
                App.pagination(ps, request);
            });
        };
        var submitIt = function () {
            App.router.go(App.router.getCurrentPath(), searchForm.serializeObject());
        };
        var init = function () {
           	$('#create-btn').click(function() {
           		App.router.go('/app/module');
           	});
            $('#search-btn').click(submitIt);
            $("#search-key").keydown(function (e) {
                if (e.keyCode == 13) {
                    submitIt();
                }
            });
        	Regions.load().then(function(){
        		request();
        	});
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
        	<input type="hidden" name="limit" value="20" ng-model="REQUEST.limit"/>
            <input type="text" id="search-key" class="form-control" name="region" ng-model="REQUEST.region"  placeholder="region"/>
            <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
            <a href="javascript:;" id="create-btn" class="btn btn-success" data-l10n-id="add"></a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 20%">Region</th>
                <th style="width: 60%">配置</th>
                <th style="width: 20%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{_region}}</td>
                <td>{{_modules}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="#/app/module?region={{region}}" data-l10n-id="edit"></a>
                                <a href="javascript:;" data-op-type="remove" data-op-key="{{region}}" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>