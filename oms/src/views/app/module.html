<link rel="stylesheet" type="text/css" href="vendor/select2/select2.css" />
<script src="vendor/select2/select2.min.js"></script>
<script src="js/region-select.js"></script>
<script>
App.ready(function(){
	App.menu.select('/app/modules');
	var form = $('#save-form');
	var _model = null;
    var loadModel = function () {
        var region = App.router.getParameter('region');
        if (!region) {
        	return $.when(null);
        }
        return App.api('/app/module', {'region': region}).then(function(model) {
        	model._modules = model.modules.join(',');
        	_model = model;
        	form.find('[name=region]').attr('disabled', 'disabled');
        	form.renderModel(model);
        });
    };
    var save = function() {
    	var model = form.serializeObject();
    	if (_model) {
    		model.region = _model.region;
    	}
    	if (mars.isNull(model.region) || model.region.length == 0) {
    		model.region = '0';
    	}
    	App.api('/app/module_save', model).then(function() {
      		App.router.go('/app/modules');
        });
    };
    var init = function() {
	    loadModel().then(function(){
	    	var select = new RegionSelect('region-select');
		 	var options = (_model && _model.region) ? {'value': _model.region}: {};
		    select.init(options);
	    }).then(function(){
	    	$('#save-btn').click(save);
    	});
    };
    init();
});
</script>

<form id="save-form" role="form" onsubmit="return false;">
			<div class="form-group">
				<label>Region</label>
        		<select name="region" class="form-control" id="region-select-s1"></select>
			</div>
			<div class="form-group">
				<label>配置</label>
				<input type="text" class="form-control" name="modules" ng-model="_modules"/>
			</div>
			<div class="form-group">
				<label></label>
				<div class="controls">
					<a href="javascript:;" id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
					<a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
				</div>
			</div>
</form>