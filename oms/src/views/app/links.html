<script>
App.ready(function(){
	var remove = function(id) {
		if (!confirm(App.text('confirm.delete'))) {
			return;
		}
		App.api('/app/removelink', {'id': id}).then(function(){
			window.location.reload();
		});
	};
	var request = function(offset) {
		App.scrollToTop();
		App.api('/app/links',	{
			'id' : App.router.getParameter('id'),
			'offset' : U.isNull(offset) ? 0 : offset,
			'limit' : 20
		}).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for ( var i = 0; i < list.length; i++) {
					var item = list[i];
					var links = item.links || {};
					var linksCount = Object.keys(links).length + 1;
					var linksCounter = 1;
					var _links = '默认: ' + item.defaultLink;
					$.each(links, function(key, value) {
						_links += '<br/>' + key + ': ' + value;
						linksCounter++;
						if (linksCounter >= 3 && linksCounter < linksCount) {
							_links += '&nbsp;&nbsp;等' + linksCount + "个链接";
							return false;
						}
					});
					item._links = _links;
					var row = U.template(template, item);
					table.append(row);
				}
				App.pagination(ps, request);
		});
	};
	var submitIt = function() {
		App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
	};
	var init = function() {
		window.remove = remove;
		$('#search-btn').click(submitIt);
		$("#search-key").keydown(function(e){
		    　if(e.keyCode == 13) {
		    	submitIt();
		    　}
		});
		request();
	};
	
	init();
});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" onsubmit="return false;">
			<input type="text" id="search-key" class="form-control" name="id" ng-model="REQUEST.id" />
			<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
			<a href="#/app/savelink" class="btn btn-success" data-l10n-id="create"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 20%">ID</th>
				<th style="width: 30%">链接</th>
				<th style="width: 30%">备注</th>
				<th style="width: 20%">操作</th>
			</tr>
			<tr class="template">
				<td>{{id}}</td>
				<td>{{_links}}</td>
				<td>{{notes}}</td>
				<td class="operations">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li>
								<a href="#/app/savelink?id={{id}}" data-l10n-id="edit"></a>
							</li>
							<li>
								<a href="javascript:remove('{{id}}');" data-l10n-id="delete"></a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>