<script src="js/localesaver.js"></script>
<script>
    App.ready(function () {
    	var form = $('#save-form');
        var localeSaver = new LocaleSaver({'dialogUrl': '/app/alert_spec.html'});
        var save = function () {
            var data = localeSaver.getDataAsModel();
         	var content = data['content'];
            if (!content || $.isEmptyObject(content)) {
            	alert('内容不能为空');
            	return;
            }
            if (!confirm('确定要修改吗？修改后用户将收到新的警告内容')) {
            	return;
            }
            App.api('/app/alert_save', {'alert': JSON.stringify(content)}).then(function () {
                window.location.reload();
            });
        };
        var remove = function() {
            if (!confirm('确定要删除吗？')) {
            	return;
            }
        	 App.api('/app/alert_save', {}).then(function () {
                 window.location.reload();
             });
        };
        var loadModel = function () {
            return App.api('/app/alert', null, {
                success: function (model) {
                    form.renderModel(model);
                    localeSaver.setDataByModel(model, ['content']);
                }
            });
        };
        var init = function() {
        	localeSaver.init().then(loadModel).then(function () {
                $('#save-btn').click(save);
                $('#remove-btn').click(remove);
            });
        };
        init();
    });
</script>
<form id="save-form" role="form">
    <div class="form-group">
        <labe>版本</label>
        <input type="text" name="version" class="form-control" ng-model="version" disabled/>
    </div>
    <div class="form-group">
        <div class="controls">
            <a href="javascript:;" id="local-add-btn">
                <span>+</span>
                <span data-l10n-id="localize"></span>
            </a>
            <div id="locale-spec-template">
                <div class="locale-spec">
                    <div class="locale-spec-item">
                        <label data-l10n-id="locale"></label>:
                        <span class="locale">{{locale.name}}</span>
                        <a href="javascript:;" class="edit" data-l10n-id="edit" data-op-type="update" data-op-key="{{locale.key}}"></a>
                        <a href="javascript:;" class="edit" data-l10n-id="delete" data-op-type="remove" data-op-key="{{locale.key}}"></a>
                    </div>
                    <div class="locale-spec-item">
                    	<label>内容</label>:<span>{{spec.content}}</span>
                    </div>
                </div>
            </div>
            <div id="locale-specs"></div>
        </div>
    </div>
    <a id="save-btn" href="javascript:;" class="btn btn-primary" data-l10n-id="save"></a>
    <a id="cancel-btn" href="javascript:window.location.reload();" class="btn btn-info" data-l10n-id="cancel"></a>
    <a id="remove-btn" href="javascript:;" class="btn btn-warning" data-l10n-id="delete"></a>
</form>