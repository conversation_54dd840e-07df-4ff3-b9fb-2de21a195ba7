
<script>
	App.ready(function() {
		App.menu.select('/im/emotiongroups');
		
		var id = App.router.getParameter('id');
		var init = function() {
			App.api('/im/emotions', {
				'id' : id
			}).then(function(result) {
				var table = $('.table-main');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < result.length; i++) {
					var context = result[i];
					if (context.url) {
						context._url = '<a href="'+context.url+'" target="_blank"><img src="'+context.url+'" /></a>';
					}
					context.groupId = id;
					var row = U.template(template, context);
					table.append(row);
				}
			});
		}
		init();
		$('.btn-add').click(function() {
			App.router.go('/im/emotion', {
				groupId : id
			});
		});
		$('.btn-download').click(function() {
			App.api('/im/emotiongroup_zip', {
				groupId : id
			}).then(function(result) {
				if (window.confirm("是否下载？")) {
					window.location.target = "_blank";
					window.location.href = result;
				}
			});
		});
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<a class="btn btn-primary btn-add" href="javascript:;">添加</a>
		<a class="btn btn-primary btn-download" href="javascript:;">打包本组表情</a>
	</div>
	<div class="panel-body">
		<table class="table x-table table-main">
			<tr class="header">
				<th width="20%">ID</th>
				<th width="30%">图片</th>
				<th width="30%">名称</th>
				<th width="20%">操作</th>
			</tr>
			<tr class="template">
				<td>{{id}}</td>
				<td>{{_url}}</td>
				<td>{{name}}</td>
				<td>
					<div class="dropdown" style="display: inline-block;">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li>
								<a href="#/im/emotion?id={{id}}&groupId={{groupId}}">编辑</a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>