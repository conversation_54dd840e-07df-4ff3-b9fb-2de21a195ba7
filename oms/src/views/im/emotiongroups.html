<script>
	App.ready(function() {
		var init = function() {
			App.api('/im/emotiongroups').then(function(result) {
				var table = $('.table-main');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < result.length; i++) {
					var context = result[i];
					var row = U.template(template, context);
					table.append(row);
				}
			});
		};
		init();
		$('.btn-add').click(function() {
			App.router.go('/im/emotiongroup');
		});
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<a class="btn btn-primary btn-add" href="javascript:;">添加分组</a>
	</div>
	<div class="panel-body">
		<table class="table x-table table-main">
			<tr class="header">
				<th width="20%">ID</th>
				<th width="30%">封面</th>
				<th width="30%">名称</th>
				<th width="20%">操作</th>
			</tr>
			<tr class="template">
				<td>{{id}}</td>
				<td><img src="{{cover}}"></td>
				<td>{{name}}</td>
				<td>
					<div class="dropdown" style="display: inline-block;">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li>
								<a href="#/im/emotiongroup?id={{id}}">编辑</a>
							</li>
							<li>
								<a href="#/im/emotions?id={{id}}">子表情</a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>