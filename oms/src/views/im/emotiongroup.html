<script src="js/localespec.js"></script>
<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script>
	App.ready(function() {
		App.menu.select('/im/emotiongroups');
		var params = App.router.getParameters();
		var form = $('#save-form');

		var previewImage = function(url) {
			if (url) {
				$('#icon-preview').html('<img src="'+url+'" width="200" height="200"/>');
			} else {
				$('#icon-preview').html('');
			}
			form.find('[name="cover"]').val(url);
		};

		App.upload.init({
			namespace:'emotion',
			type:'image',
			id: 'upload-btn',
			fileUploaded: function (info) {
				previewImage(info.url);
			},
			error: function (errTip) {
				alert('文件上传出错' + errTip);
			}
		});

		window.addLocaleSpec = function(locale) {
			var spec = LocaleSpec.get(locale);
			App.openDialog({
				url : App.router.getRealPath('/im/emotiongroup_localespec.html'),
				context : {
					spec : spec
				},
				noHeader : true,
				primaryClick : LocaleSpec.save,
				beforeRender : function(body) {
					var select = body.find('[name="locale"]');
					$.each(LocaleSpec.getLocaleList(), function(index, item) {
						select.append('<option value="'+item.key+'">' + item.name + '</option>');
					});
				}
			});
		};

		var renderModel = function(model) {
			App.renderPage({
				'model' : model
			});
			var _localizedProps = {};
			_localizedProps.name = model.names;
			LocaleSpec.setAllByKeys(_localizedProps);
			previewImage(model.cover);
		};

		var loadModel = function() {
			var id = App.router.getParameter('id');
			if (!id) {
				renderModel({});
				return;
			}
			return App.api('/im/emotiongroup', {
				'id' : id
			}, {
				success : function(data) {
					model = data;
					if ($.isEmptyObject(model)) {
						return false;
					}
					renderModel(model);
				}
			});
		};

		var save = function(e) {
			_localizedProps = LocaleSpec.getAllByKeys();
			var data = form.serializeObject();
			data.names = _localizedProps.name;
			App.api('/im/emotiongroup_save', {
				'model' : JSON.stringify(data)
			}).then(function() {
				App.router.go('/im/emotiongroups');
			});
		};

		var init = function() {
			LocaleSpec.loadLocales();
			loadModel();
			$('#add-locale-btn').click(addLocaleSpec);
			$('.btn-save').click(save);
		};
		init();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-body">
		<form id="save-form" class="form">
			<input type="hidden" name="id" ng-model="model.id" />
			<input type="hidden" name="cover" ng-model="model.cover" />
			<div class="form-group">
				<label>封面</label>
				<div><a href="javascript:;" id="upload-btn">+选择上传文件</a></div>
				<div id="icon-preview"></div>
			</div>
			<div class="form-group">
				<a href="javascript:;" id="add-locale-btn">
					<span>+</span>
					<span data-l10n-id="localize"></span>
				</a>
				<div id="locale-spec-template">
					<div class="locale-spec" data-locale="{{locale.key}}">
						<div class="locale-spec-item">
							<label data-l10n-id="locale"></label>:
							<span class="locale">{{locale.name}}</span>
							<a href="javascript:addLocaleSpec('{{locale.key}}');" class="edit" data-l10n-id="edit"></a>
							<a href="javascript:LocaleSpec.remove('{{locale.key}}');" class="edit" data-l10n-id="delete"></a>
						</div>
						<div class="locale-spec-item">
							<label data-l10n-id="name"></label>:
							<span>{{spec.name}}</span>
						</div>
					</div>
				</div>
				<div id="locale-specs"></div>
			</div>
			<a class="btn btn-primary btn-save" href="javascript:;">保存</a>
			<a class="btn btn-primary" href="javascript:window.history.back();">返回</a>
		</form>
	</div>
</div>