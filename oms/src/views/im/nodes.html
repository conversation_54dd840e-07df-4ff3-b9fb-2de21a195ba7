<script>
    App.ready(function () {
    	var searchForm = $('#search-form');
        var searchParams = searchForm.serializeObject();
        var openSaveDialog = function(model) {
        	App.api('/im/nodegroups').done(function(groups){
                App.openDialog({
                    url: App.router.getRealPath('/im/node.html'),
                    context: model,
                    noHeader: true,
                    beforeRender: function(body){
                    	var select = body.find('[name="group.id"]');
                		$.each(groups, function(index, item){
                			select.append('<option value="' + item.id + '">'+ item.name +'</option>');
                		});
                    },
                    primaryClick: save
                });
        	});
        };
        var save = function () {
            var form = $('#dialog-form');
            App.api('/im/node_save', form.serializeObject()).then(reload);
        };
        var remove = function (id) {
            if (!confirm(App.text('confirm.operation'))) {
                return;
            }
            var tokens = id.split('_');
            var params = {'region': tokens[0], 'ip': tokens[1]};
            App.api('/im/node_remove', params).then(function () {
                window.location.reload();
            });
        };
        var update = function (id) {
        	var item = itemsMap[id];
            openSaveDialog(item);
        };
        var reload = function() {
        	App.closeDialog();
        	request(0);
        };
        var itemsMap = {};
        var request = function (offset) {
            App.scrollToTop();
        	var requestParams = $.extend({'limit': 20}, searchParams, {'offset': U.isNull(offset) ? 0 : offset});
            App.api('/im/nodes', requestParams).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                itemsMap = {};
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context.id = context.region + '_' + context.ip;
                    itemsMap[context.id] = context;
                    var row = U.template(template, context);
                    table.append(row);
                }
                table.find('[data-op-type="update"]').click(function(){
                	update($(this).attr('data-op-key'));
                });
                table.find('[data-op-type="remove"]').click(function(){
                	remove($(this).attr('data-op-key'));
                });
                App.pagination(ps, request);
            });
        };
        var submitIt = function () {
            App.router.go(App.router.getCurrentPath(), searchForm.serializeObject());
        };
        var init = function () {
           	$('#create-btn').click(openSaveDialog);
            $('#search-btn').click(submitIt);
            $("#search-key").keydown(function (e) {
                if (e.keyCode == 13) {
                    submitIt();
                }
            });
            request();
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
            <input type="text" id="search-key" class="form-control" name="region" ng-model="REQUEST.region"  placeholder="region"/>
            <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
            <a href="javascript:;" id="create-btn" class="btn btn-success">添加</a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 20%">Region</th>
                <th style="width: 30%">IP</th>
                <th style="width: 30%">组</th>
                <th style="width: 20%">操作</th>
            </tr>
            <tr class="template">
                <td>{{region}}</td>
                <td>{{ip}}</td>
                <td>{{group.name}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:;" data-op-type="update" data-l10n-id="edit" data-op-key="{{id}}"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="remove" data-l10n-id="delete" data-op-key="{{id}}"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>