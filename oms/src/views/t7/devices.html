<script>
  App.ready(function () {
    // var ifr = document.getElementById("iframe");
    // ifr.src =
    //   "/device/t6/devices?X-Admin-Session=" +
    //   $.cookie("sess") +
    //   "&deviceId=" +
    //   (App.router.getParameters().s || "");
    // var hashSearch = App.router.getHashSearch();
    // var token = $.cookie('sess');
    // localStorage.setItem('sessionToken', token);
    // var ifrSrc = '/oms/device/t6/wifi/list';
    // var url = hashSearch ? (ifrSrc + '?' + hashSearch) : ifrSrc;
    // var ifr = document.getElementById('iframe');
    // ifr.src = url;
    App.generator.generatePage('/oms/device/t7/wifi/list', '/t7/devices');
  });
</script>

<div style="height: calc(100vh - 60px)">
  <iframe id="iframe" frameborder="0" width="100%" height="100%"></iframe>
</div>
