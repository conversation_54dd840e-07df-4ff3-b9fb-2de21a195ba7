<script>
  App.ready(function () {
    // App.menu.select('/mall/content');
    // var token = $.cookie('sess');
    // localStorage.setItem('sessionToken', token);
    // var ifrSrc = '/web/mall/marketing/content/list';
    // var contentId = App.router.getParameter('contentId');
    // if (contentId) {
    //     ifrSrc = '/web/mall/marketing/content/edit/' + contentId;
    // }
    // var ifr = document.getElementById('iframe');
    // ifr.src = ifrSrc;
    App.generator.generatePage(
      "/web/mall/marketing/content/list",
      "/mall/content"
    );
  });
</script>

<div style="height: calc(100vh - 60px)">
  <iframe id="iframe" frameborder="0" width="100%" height="100%"></iframe>
</div>
