<script>
App.ready(function(){
	var buildResult = function(list) {
		var table = $('#the-table');
		var template = table.attr('data-template');
		if (!template) {
			template = '<tr>'
					+ table.find('tr.template')
							.html() + '</tr>';
			table.attr('data-template', template);
		}
		table.find('tr:not(.header)').remove();
		for ( var i = 0; i < list.length; i++) {
			var context = list[i];
			context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
			var row = U.template(template, context);
			table.append(row);
		}
	};
	var request = function() {
		App.scrollToTop();
		var params = App.router.getParameters();
		if (!params['username']) {
			buildResult([]);
			return;
		}
		App.api('/user/signupcodehistory',	params).then(buildResult);
	};
	var renew = function() {
		var params = $('#search-form').serializeObject();
		if (!params['username']) {
			return;
		}
		if (!confirm(App.text('confirm.operation'))) {
			return;
		}
		App.api('/user/renewsignupcode', params).then(function(result){
			alert(result.code);
			window.location.reload();
		});
	};
	var submitIt = function() {
		App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
	};
	$('#search-btn').click(submitIt);
	$("#search-key").keydown(function(e){
	    　if(e.keyCode == 13) {
	    	submitIt();
	    　}
	});
	$('#renew-btn').click(renew);
	request();
});
</script>
<div class="panel panel-default x-panel">
<div class="panel-heading">
	<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
		<input type="text" id="search-key" class="form-control" name="username" ng-model="REQUEST.username" placeholder="Mobile/Email"/>
		<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
		<a href="javascript:;" id="renew-btn" class="btn btn-success" data-l10n-id="p.user.renewcode"></a>
	</form>
</div>
<div class="panel-body"><div class="table-responsive">
<table class="table table-hover x-table" id="the-table">
	<tr class="header">
		<th style="width:50%">验证码</th>
		<th style="width:50%">发送时间</th>
	</tr>
	<tr class="template">
		<td>{{code}}</td>
		<td>{{_createdAt}}</td>
	</tr>
</table>
</div></div></div>