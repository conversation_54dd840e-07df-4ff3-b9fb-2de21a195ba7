<script>
App.ready(function(){
	App.menu.select('/user/users');
    var params = $.extend({'limit': 20}, App.router.getParameters());
	var request = function(offset) {
		App.scrollToTop();
		if (!mars.isNull(offset)) {
           	params.offset = offset;
        }
		App.api('/user/loginhistory', params).then(function(ps) {
			var list = ps.items || [];
			var table = $('#the-table');
			var template = table.attr('data-template');
			if (!template) {
				template = '<tr>'
						+ table.find('tr.template')
								.html() + '</tr>';
				table.attr('data-template', template);
			}
			table.find('tr:not(.header)').remove();
			for ( var i = 0; i < list.length; i++) {
				var context = list[i];
				context._time = U.date.format(U.date.parse(context.time), 'yyyy-MM-dd HH:mm');
				context._location = context.location ? (context.location[0] + ',' + context.location[1]) : '';
				var row = U.template(template, context);
				table.append(row);
			}
			App.pagination(ps, request);
		});
	};
	$('#back-btn').click(function(){
		App.router.go('/user/users', {'username': App.router.getParameter('userId')});
	});
	request();
});
</script>
<div class="panel panel-default x-panel">
<div class="panel-heading">
	<a href="javascript:;" id="back-btn" class="btn btn-primary" data-l10n-id="back"></a>
	<span data-l10n-id="p.user.loginhistory"></span>
</div>
<div class="panel-body"><div class="table-responsive">
<table class="table table-hover x-table" id="the-table">
	<tr class="header">
		<th style="width:15%">时间</th>
		<th style="width:12%">IP</th>
		<th style="width:18%">位置</th>
		<th style="width:10%">平台</th>
		<th style="width:15%">设备</th>
		<th style="width:10%">时区</th>
		<th style="width:10%">系统版本</th>
		<th style="width:10%">版本号</th>
	</tr>
	<tr class="template">
		<td>{{_time}}</td>
		<td>{{ip}}</td>
		<td>{{_location}}</td>
		<td>{{client.platform}}</td>
		<td>{{client.name}}</td>
		<td title="{{client.locale}}">{{client.timezone}}</td>
		<td>{{client.osVersion}}</td>
		<td>{{client.version}}</td>
	</tr>
</table>
</div></div></div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>