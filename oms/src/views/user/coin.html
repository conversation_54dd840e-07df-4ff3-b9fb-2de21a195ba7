<script>
	App.ready(function() {
		App.menu.select('/user/users');
		var userId = App.router.getParameter('userId');
		var form = $('#form1');
		var init = function() {
			App.api('/user/info', {'id': userId, 'withCoin': 1}, {
                success: function (result) {
                	form.renderModel(result);
                }
            });
			$('#save-btn').click(function() {
				var data = form.serializeObject();
				if (!confirm(App.text('confirm.operation'))) {
					return;
				}
				App.api('/user/update_coin', data).then(function() {
					alert(App.text('done'));
					window.location.reload();
				});
			});
		};
		init();
	});
</script>
 <form id="form1" action="#" onsubmit="return false;">
 	<input type="hidden" name="id" ng-model="user.id"/>
	<div class="form-group">
		<label>用户ID：</label>
		<input type="text" class="form-control" ng-model="user.id" disabled="disabled"/>
	</div>
	<div class="form-group">
		<label>昵称：</label>
		<input type="text" class="form-control" ng-model="user.nick" disabled="disabled"/>
	</div>
	<div class="form-group">
		<label>积分：</label>
		<input type="text" class="form-control" name="coin" ng-model="coin"/>
	</div>
	<div class="form-group">
		<label>&nbsp;</label>
		<a href="javascript:;" id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
		<a href="javascript:window.history.back();" class="btn btn-success" data-l10n-id="cancel"></a>
	</div>
</form>