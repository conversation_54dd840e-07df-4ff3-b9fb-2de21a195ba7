<script>
    App.ready(function () {
        var params = $.extend({'limit': 10}, App.router.getParameters());
        var request = function (offset) {
            App.scrollToTop();
            if (mars.isEmpty(params.username) && mars.isEmpty(params.groupId)) {
            	return;
            }
            if (!mars.isNull(offset)) {
            	params.offset = offset;
            }
            var requestUrl = '';
            if (params.groupId) {
                requestUrl = '/family/getUserListByGroupId';
            } else {
                requestUrl = '/user/users'
            }
            App.api(requestUrl, params).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    var addr = context.addr || {};
                    // console.log(context.gender);
                    context._gender = App.text('p.user.gender.' + context.gender);
                    context._createdAt = U.date.format(U.date.parse(context.createdAt), 'yyyy-MM-dd HH:mm');
                    context._avatar = context.avatar ? '<a href="'+context.avatar+'" target="_blank"><img src="' +
                    			App.previewImgUrl(context.avatar, 120, 120) + '"/></a>':  null;
                    var row = U.template(template, context);
                    table.append(row);
                }
                initOperationButtons();
                App.pagination(ps, request);
            });
        };
        var initOperationButtons = function() {
        	$('[data-op-type="update-nick"]').click(function(){
       			var a = $(this);
        		updateNick(a.attr('data-op-key'),a.attr('data-op-extra'));
        	});
        };
        var updateNick = function (userId, oldNick) {
            if (!oldNick) {
                oldNick = 'u' + userId;
            }
            var nick = prompt(App.text('p.user.update.nick.prompt', [oldNick]), oldNick);
            if (!nick) {
                return;
            }
            App.api('/user/update_nick', {userId: userId, nick: nick}).then(function () {
                alert(App.text('done'));
                window.location.reload();
            });
        };
        var submitIt = function () {
            App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
        };
        $('#search-btn').click(submitIt);
        $("#search-key").keydown(function (e) {
            if (e.keyCode == 13) {
                submitIt();
            }
        });
        request();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
            <input id="search-key" type="text" class="form-control" name="username"
                   data-l10n-id="p.user.search.tip" data-l10n-for="placeholder" ng-model="REQUEST.username" style="width:20rem;"/>
            <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th data-l10n-id="id"></th>
                <th data-l10n-id="p.user.avatar"></th>
                <th data-l10n-id="p.user.nick"></th>
                <th data-l10n-id="p.user.gender"></th>
                <th data-l10n-id="p.user.createdat"></th>
                <th data-l10n-id="p.user.locality"></th>
                <th data-l10n-id="p.user.birth"></th>
                <th data-l10n-id="p.user.growth"></th>
                <th data-l10n-id="p.user.familyCount"></th>
                <th data-l10n-id="p.user.petCount"></th>
                <th data-l10n-id="p.user.deviceCount"></th>
                <th data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{id}}</td>
                <td>{{_avatar}}</td>
                <td>{{nick}}</td>
                <td>{{_gender}}</td>
                <td>{{_createdAt}}</td>
                <td>{{locality}}</td>
                <td>{{birth}}</td>
                <td>{{point.growth}}</td>
                <td><a href="#/user/family?userId={{id}}" target="_self">{{userCount}}</a></td>
                <td><a href="#/pet/pets?id=owner-{{id}}" target="_self">{{petCount}}</a></td>
                <td><a href="#/user/linked_devices?userId={{id}}" target="_self">{{deviceCount}}</a></td>
                <td class="operations">
                    <!-- <a href="#/pet/pets?id=owner-{{id}}" data-l10n-id="p.user.pet"></a>
                    <span class="vertical-bar">|</span> -->
                    <div class="dropdown" style="display:inline-block;">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b></a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="#/user/linked_devices?userId={{id}}" data-l10n-id="p.user.dev"></a>
                            </li>
                            <li>
                                <a href="#/user/history_linked_devices?userId={{id}}" data-l10n-id="p.user.dev.history"></a>
                            </li>
                            <li>
                                <a href="#/app/logs?userId={{id}}" data-l10n-id="p.user.applog"></a>
                            </li>
                            <li>
                                <a href="#/user/loginhistory?userId={{id}}" data-l10n-id="p.user.loginhistory"></a>
                            </li>
                            <li>
                                <a href="#/user/coin?userId={{id}}" data-l10n-id="p.user.coin"></a>
                            </li>
                            <li>
                                <a href="#/user/account?userId={{id}}" data-l10n-id="p.user.account"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="update-nick" data-op-key="{{id}}" data-op-extra="{{nick}}" data-l10n-id="p.user.update.nick"></a>
                            </li>
                            <li>
                                <a href="#/community/posts?type=authored&authorId={{id}}" data-l10n-id="p.user.post"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>
