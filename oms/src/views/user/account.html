<script>
	App.ready(function() {
		App.menu.select('/user/users');
		  
		var wrap = $('#account-info');
		var userId = App.router.getParameter('userId');
		var isOfficial = false;

		var randomPassword = function(len, chars) {
			var s = '';
			for (var i = 0; i < len; i++) {
				var k = parseInt(chars.length * Math.random());
				var c = chars[k];
				s += c;
			}
			return s;
		};

		var updatePassword = function(newPassword) {
			if (!confirm(App.text('confirm.operation'))) {
				return;
			}
			var data = {
				'userId' : userId,
				'password' : newPassword
			}
			App.api('/user/update_password', data).done(function() {
				alert('密码重置成功，新密码是: ' + newPassword);
			});
		};

		var loadModel = function() {
			return App.api('/user/info', {'id':userId, 'withAccount':1}, {
				success : function(result) {
					var user = result.user;
					if (user.roles) {
						for (var i = 0; i < user.roles.length; i++) {
							if (user.roles[i] == 1) {
								isOfficial = true;
								break;
							}
						}
					}
					var account = user.account;
					var tps = account.thirdparties;
					if (tps) {
						for (var i = 0; i < tps.length; i++) {
							var type = tps[i].type;
							account['_tp' + type] = tps[i].info;
							$('[data-thirdparty-type="' + type + '"]')
									.removeClass('none');
						}
					}
					var model = {'user': user, 'account': account};
					wrap.renderModel(model);
					$('[data-account-button="official-set"]').text(isOfficial ? '取消': '设置');
				}
			});
		};
		
		var doneAndRequest = function() {
			alert(App.text('done'));
			window.location.reload();
		};

		var updateUsername = function(type, username) {
			if (!confirm(App.text('confirm.operation'))) {
				return;
			}
			App.api('/user/update_username', {
				'userId': userId,
				'type' : type,
				'username' : username
			}).done(doneAndRequest);
		};
		

		var unbindThirdParty = function(type) {
			if (!confirm(App.text('confirm.operation'))) {
				return;
			}
			App.api('/user/unbindthirdparty', {
				'tpType' : type,
				'userId' : userId
			}).done(doneAndRequest);
		};
		
		var updateRegion = function() {
			var region = prompt('请输入用户Region', '');
			if (!region) {
				return;
			}
			return App.api('/user/update_region', {
				'userId' : userId,
				'region' : region
			}).done(doneAndRequest);
		};
		
		var setOfficial = function() {
			var text = isOfficial ? '确定要取消官方吗？' : '确定要设置为官方吗';
			if (!confirm(text)) {
				return;
			}
			return App.api('/user/update_official', {
				'userId' : userId,
				'remove' : isOfficial ? 1: 0
			}).done(doneAndRequest);
		};

		var onClick = function() {
			var btn = $(this).attr('data-account-button');
			if (btn == 'update-mobile') {
				updateUsername('mobile', $('#mobile').val());
			} else if (btn == 'update-email') {
				updateUsername('email', $('#email').val());
			} else if (btn == 'update-password') {
				var newPassword = randomPassword(1,
						'123456789abcdefghjkmnopqrstuvwxyz')
						+ randomPassword(5, '**********');
				updatePassword(newPassword);
			} else if (btn == 'update-password-custom') {
				var newPassword = prompt('请输入新密码，至少6位');
				if (newPassword.length < 6) {
					return;
				}
				updatePassword(newPassword);
			} else if (btn.indexOf('unbind-') == 0) {
				var type = btn.substring('unbind-'.length);
				unbindThirdParty(type);
			} else if (btn == 'update-region') {
				updateRegion();
			} else if (btn == 'official-set') {
				setOfficial();
			}
		};

		var initButtons = function() {
			var btns = wrap.find('[data-account-button]');
			for (var i = 0; i < btns.length; i++) {
				$(btns[i]).click(onClick);
			}
		};

		loadModel().then(initButtons);

	});
</script>
<style type="text/css">
.inline-input {
	display:inline-block;
	width:400px;
}
.avatar {
	width: 60px;
}
</style>

<div id="account-info">
	<div class="form-group">
		<label>用户ID：</label>
		<input type="text" class="form-control" ng-model="user.id" disabled="disabled"/>
	</div>
	<div class="form-group">
		<label>用户昵称：</label>
		<input type="text" class="form-control" ng-model="user.nick" disabled="disabled"/>
	</div>
	<div class="form-group">
		<label>手机：</label> <input type="text" class="form-control inline-input" id="mobile"
			ng-model="account.mobile"/> <a data-account-button="update-mobile"
			class="btn btn-primary">修改</a>
	</div>
	<div class="form-group">
		<label>邮箱：</label> <input type="text" class="form-control inline-input" id="email"
			ng-model="account.email" /> <a data-account-button="update-email"
			class="btn btn-primary">修改</a>
	</div>
	<div class="form-group none" data-thirdparty-type="1">
		<label>微信：</label>
		<span ng-model="account._tp1.nick"></span>
		<img class="avatar" ng-model="account._tp1.avatar"/>
		<a data-account-button="unbind-1" class="btn btn-primary">解绑</a>
	</div>
	<div class="form-group none" data-thirdparty-type="2">
		<label>微博：</label>
		<span ng-model="account._tp2.nick"></span>
		<img class="avatar" ng-model="account._tp2.avatar"/>
		<a data-account-button="unbind-2" class="btn btn-primary">解绑</a>
	</div>
	<div class="form-group none" data-thirdparty-type="11">
		<label>Facebook：</label>
		<span ng-model="account._tp11.nick"></span>
		<img class="avatar" ng-model="account._tp11.avatar"/>
		<a data-account-button="unbind-11" class="btn btn-primary">解绑</a>
	</div>
	<div class="form-group none" data-thirdparty-type="12">
		<label>Twitter：</label>
		<span ng-model="account._tp12.nick"></span>
		<img class="avatar" ng-model="account._tp12.avatar"/>
		<a data-account-button="unbind-12" class="btn btn-primary">解绑</a>
	</div>
	<div class="form-group">
		<label>用户region：</label>
		<input type="text"  ng-model="account.region" disabled="disabled"/>
		<a data-account-button="update-region" class="btn btn-primary">修改</a>
	</div>
	<div class="form-group">
		<label>官方账号：</label>
		<a data-account-button="official-set" class="btn btn-primary">设置</a>
	</div>
	<div class="form-group">
		<label>密码：</label> <a data-account-button="update-password"
			class="btn btn-primary">重置密码(随机)</a>
			<a data-account-button="update-password-custom"
			class="btn btn-primary">重置密码</a>
	</div>
	<div class="form-group">
		<label>&nbsp;</label>
		<a class="btn btn-success" href="javascript:window.history.back();" data-l10n-id="cancel"></a>
	</div>
</div>