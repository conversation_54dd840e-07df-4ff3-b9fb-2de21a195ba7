<script src="vendor/highchart/highcharts.min.js"></script>
<script src="js/region.js"></script>
<script>
App.ready(function(){
	var form = $('#search-form');
	var container = $('#chart1');
	var regionUI = null;
	
	var date2day = function(d) {
		return d.getFullYear() * 10000 + (d.getMonth() + 1) * 100 + 
				d.getDate();
	};
	
	var loadAddressMap = function() {
		return App.api('/app/countries').then(function(regions){
			Region.init(regions);
		});
	};
	
	var renderChart = function(chartType, data) {
		var cats = [];
		var values = [];
		var total = 0;
		if(chartType=='pie'){
			for(var i=0;i<data.length;i++){
				var item = data[i];
				total += item.v;
				values.push([item.c,item.v]);
			}
		} else {
			for(var i=0;i<data.length;i++){
				var item = data[i];
				total += item.v;
				cats.push(item.c);
				values.push(item.v);
			}
		}
		
		var options = {
		        chart: {
		            type: chartType
		        },
		        title: {
		            text: '报表',
		        },
		        xAxis: {
		            categories: cats,
		            title: { 
		                text: ''
		            }
		        },
		        yAxis: {
		        	gridLineColor: '#DEDEDE',
		        	gridLineWidth: 1,
		        	gridLineDashStyle: 'longdash',
		        	min:0, 
		            title: {
		                text: '数量'
		            }
		        },
		        series: [{
		            name: '数量',
		            data: values
		        }]
		    };
		
		if(chartType=='pie'){
			options.tooltip =  {
	    	    pointFormat: '{series.name}: <b>{point.percentage:.2f}%</b>'
	        };
		}

		container.highcharts(options);
		$('#chart1-total').html('共找到<font color="red">' + total + '</font>条数据');
	};
	
	var stat = function(){
		var addressCode = regionUI.getValue();
		form.find('[name="addressCode"]').val(addressCode);
		container.empty();
		var chartType = form.find('[name="chartType"]').val();
		App.api('/user/statistics', form.serializeObject()).then(function(data){
			if (!data) {
				return;
			}
			renderChart(chartType, data);
		});
	};
	
	var initButtons = function() {
		$('#stat-btn').click(stat);
	};
	
	var init = function(){
		loadAddressMap().then(function(){
			regionUI = new RegionUI('address');
			regionUI.setValue('86');
			
			var now = new Date();
			var today = date2day(now);
			now.setTime(now.getTime() - 7*24*3600*1000);
			var aWeekAgo = date2day(now);
			form.find('[name="since"]').val(aWeekAgo);
			form.find('[name="until"]').val(today);
			form.find('[name="groupBy"]').val('day');
			
		}).then(initButtons).then(stat);
	};
	
	init();
});
</script>
<style>
#advanced-options .option {
	margin:10px 0 0 0;
}
</style>

<form id="search-form" class="form-inline">
	<select class="form-control" name="groupBy"><option value="day">按天</option><option value="addr">按区域</option></select>
	<a href="javascript:;" id="stat-btn" class="btn btn-success">查看报表</a>
	<!-- <a href="javascript:;" id="advanced-options-toggle"><span>高级选项</span><b class="caret"></b> </a>-->
	<div id="advanced-options">
		<div class="option">
			<span>区域</span>
			<input type="hidden" name="addressCode"/>
			<select class="form-control" id="address-country"></select>
			<select class="form-control" id="address-province"></select>
			<select class="form-control" id="address-city"></select>
			<select class="form-control" id="address-district"></select>
		</div>
		<div class="option">
			<span>日期(格式20150101)</span>
			<span>从</span>
			<input type="text" class="form-control" name="since"/>
			<span>到</span>
			<input type="text" class="form-control" name="until"/>
		</div>
		<div class="option">
			<label>有宠物</label><input type="checkbox" name="hasPet" value="1"/>
			<label style="padding-left:10px;">有狗</label><input type="checkbox" name="hasDog" value="1"/>
			<label style="padding-left:10px;">有猫</label><input type="checkbox" name="hasCat" value="1"/>
			<label style="padding-left:10px;">有p设备</label><input type="checkbox" name="hasPDevice" value="1"/>
			<label style="padding-left:10px;">有Mate</label><input type="checkbox" name="hasMate" value="1"/>
		</div>
		<div class="option">
			<span>图表类型</span>
			<select class="form-control" name="chartType">
				<option value="column">柱状图</option>
				<option value="line">折线图</option>
				<option value="pie">饼图</option>
			</select>
		</div>
	</div>
</form>
<div id="chart-wrap">
	<div id="chart1-total" style="padding:20px 0;"></div>
	<div id="chart1"></div>
</div>