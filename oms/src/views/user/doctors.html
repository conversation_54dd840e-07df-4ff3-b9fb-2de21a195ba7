<script>
    App.ready(function () {
        var request = function (offset) {
            App.scrollToTop();
            var params = $.extend({'limit': 10}, App.router.getParameters(), {'offset': offset});
            App.api('/user/doctor_roster', params).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._avatar = context.avatar ? '<a href="'+context.avatar+'" target="_blank"><img src="' +
                			App.previewImgUrl(context.avatar, 120, 120) + '"/></a>':  null;
                    var row = U.template(template, context);
                    table.append(row);
                }
                initOperationButtons();
                App.pagination(ps, request);
            });
        };
        var initOperationButtons = function() {
        	$('[data-op-type=remove]').click(function(){
        		remove($(this).attr('data-op-key'));
        	});
        };
        var remove = function(id) {
        	  if (!confirm(App.text('confirm.delete'))) {
        		  return;
        	  }
	       	  return App.api('/user/doctor_remove', {'id': id}).done(function () {
	       		  request(0);
	       	  });
        };
        var doAdd = function(userId) {
        	return App.api('/user/doctor_save', {'id': userId}).done(function() {
                request(0);
        	});
        };
        var add = function() {
        	var username = prompt(App.text('p.user.please.input.username'), '');
            if (!username) {
                return;
            }
            return App.api('/user/info', {'username': username}).done(function(result) {
            	var userId = result.user.id;
            	doAdd(userId);
      	    });
        };
        var init = function() {
        	$('#add-btn').click(add);
            request();
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
         <a href="javascript:;" id="add-btn" class="btn btn-success" data-l10n-id="add"></a>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 30%" data-l10n-id="id"></th>
                <th style="width: 30%" data-l10n-id="p.user.nick"></th>
                <th style="width: 30%" data-l10n-id="p.user.avatar"></th>
                <th style="width: 10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{id}}</td>
                <td>{{nick}}</td>
                <td>{{_avatar}}</td>
                <td class="ops">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:;" data-op-type="remove" data-op-key="{{id}}" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>