<style type="text/css">
#device-container {
	margin: 10px 20px;
}
.device-block {
	margin: 20px 0;
}
.device-type {
	border-bottom: 1px dashed #CCC;
	padding-bottom: 5px;
	margin-bottom: 10px;
}
.device-block li {
	line-height: 2rem;
}
</style>
<script>
App.ready(function() {
	var bluetoothDeviceList = ['hg', 'fit', 'go', 'k3', 'aq', 'aqr', 'p3', 'h2', 'w5', 'r2', 'ctw3'];
	// wifi设备列表
	var wifiDeviceList = [
		't3',
		'mate',
		'feeder',
		'cozy',
		'feedermini',
		'aqh1',
		'k2',
		't4',
		't5',
		'd3',
		'd4',
		'd4s',
		'h3',
		'd4sh',
	];
	App.menu.select('/user/users');
	var container = $('#device-container');
	var renderItem = function(item) {
		var html = '<div class="device-block">';
		html += '<div class="device-type">'+ item.type +'</div>';
		html += '<ul>';
		for (var i = 0; i < item.devices.length; i++) {
			var dev = item.devices[i];
			html += '<li>';
			html += '<a href="#/' + item.type + '/devices?type=' + (bluetoothDeviceList.indexOf(item.type) > -1 ? 1 : 'id') + '&s='+ dev.id +'">设备ID: ' + dev.id + '</a>';
			if (dev.petId) {
				html += ',&nbsp;&nbsp;' + '<a href="#/pet/pets?id='+ dev.petId +'">宠物ID: ' + dev.petId + '</a>' ;
			}
			html += '</li>';
		}
		html += '</ul>';
		html += '</div>';
		return html;
	};
	var request = function() {
		var params = App.router.getParameters();
		var requestUrl = '';
		if (params.groupId) {
			requestUrl = '/family/getDeviceListByGroupId'
		} else {
			requestUrl = '/user/linked_devices';
		}
		App.api(requestUrl, params).then(function(items) {
			if (items.length == 0) {
				container.html('<p>没有绑定设备</p>');
				return;
			}
			$.each(items, function(index, item){
				container.append(renderItem(item));
			});
		});
	};
	request();
});
</script>
	
<div class="panel panel-default x-panel">
<div class="panel-heading">
	<a href="javascript:window.history.back();" id="back-btn" class="btn btn-primary" data-l10n-id="back"></a>
	<span>用户绑定设备列表</span>
</div>
<div class="panel-body">
	<div id="device-container">
		
	</div>
</div>
</div>
