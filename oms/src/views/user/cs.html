<script>
    App.ready(function () {
        var request = function (offset) {
            App.scrollToTop();
            var params = $.extend({'limit': 10}, App.router.getParameters(), {'offset': offset});
            App.api('/user/cs_roster', params).then(function (ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>'
                            + table.find('tr.template')
                                    .html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    context._suspended = context.suspended ? '挂起' : '正常';
                    var user = context.user;
                    user._avatar = user.avatar ? '<a href="'+user.avatar+'" target="_blank"><img src="' +
                			App.previewImgUrl(user.avatar, 120, 120) + '"/></a>':  null;
                    var row = U.template(template, context);
                    table.append(row);
                }
                initOperationButtons();
                App.pagination(ps, request);
            });
        };
        var initOperationButtons = function() {
    		$('[data-op-display="suspend1"]').parent().addClass('none');
    		$('[data-op-display="resume0"]').parent().addClass('none');
        	$('[data-op-type=remove]').click(function(){
        		remove($(this).attr('data-op-key'));
        	});
        	$('[data-op-type=suspend]').click(function(){
        		suspend($(this).attr('data-op-key'), 1);
        	});
        	$('[data-op-type=resume]').click(function(){
        		suspend($(this).attr('data-op-key'), 0);
        	});
        };
        var remove = function(id) {
        	  if (!confirm(App.text('confirm.delete'))) {
        		  return;
        	  }
	       	  return App.api('/user/cs_remove', {'id': id}).done(function () {
	       		  request(0);
	       	  });
        };
        var suspend = function(id, state) {
	      	  if (!confirm(state > 0 ? '确定要挂起吗？' : '确定要恢复正常状态')) {
	      		  return;
	      	  }
	       	  return App.api('/user/cs_suspend', {'id': id, 'suspend': state}).done(function () {
	       		  request(0);
	       	  });
        };
        var doAdd = function(userId) {
        	return App.api('/user/cs_save', {'id': userId}).done(function() {
                request(0);
        	});
        };
        var add = function() {
        	var username = prompt(App.text('p.user.please.input.username'), '');
            if (!username) {
                return;
            }
            return App.api('/user/info', {'username': username}).done(function(result) {
            	var userId = result.user.id;
            	doAdd(userId);
      	    });
        };
        var init = function() {
        	$('#add-btn').click(add);
            request();
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
         <a href="javascript:;" id="add-btn" class="btn btn-success" data-l10n-id="add"></a>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 20%" data-l10n-id="id"></th>
                <th style="width: 30%" data-l10n-id="p.user.nick"></th>
                <th style="width: 30%" data-l10n-id="p.user.avatar"></th>
                <th style="width: 10%">状态</th>
                <th style="width: 10%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{user.id}}</td>
                <td>{{user.nick}}</td>
                <td>{{user._avatar}}</td>
                <td>{{_suspended}}</td>
                <td class="ops">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:;" data-op-type="remove" data-op-key="{{user.id}}" data-l10n-id="delete"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-display="suspend{{suspended}}" data-op-type="suspend" data-op-key="{{user.id}}">挂起</a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-display="resume{{suspended}}" data-op-type="resume" data-op-key="{{user.id}}">恢复正常</a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>