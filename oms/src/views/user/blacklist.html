<script>
	App.ready(function() {
		var form = $('#search-form');
		var requestParams = App.router.getParameters();
		var request = function(offset) {
            App.scrollToTop();
			var params = $.extend(requestParams, {'offset': offset});
			App.api('/user/blacklist_roster', params).then(function(ps) {
                var items = ps.items || [];
				var table = $('.table-main');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < items.length; i++) {
					var context = items[i];
					context._expire = U.date.format(U.date.parse(context.expire), 'yyyy-MM-dd');
					var user = context.user;
					user._avatar = user.avatar ? '<a href="'+user.avatar+'" target="_blank"><img src="' +
	                    			App.previewImgUrl(user.avatar, 120, 120) + '"/></a>':  null;
					var row = U.template(template, context);
					table.append(row);
				}
                initOperationButtons();
                App.pagination(ps, request);
			});
		};
        var initOperationButtons = function() {
        	$('[data-op-type=remove]').click(function(){
        		remove($(this).attr('data-op-key'));
        	});
        	$('[data-op-type=edit]').click(function(){
        		edit($(this).attr('data-op-key'));
        	});
        };
        var doneAndRequest = function() {
        	//alert(App.text('done'));
        	return request(0);
        };
        var save = function(data) {
        	if (!data.userId) {
				alert('id不能为空');
				return false;
        	}
        	if (isNaN(data.day) || data.day<=0 ) {
        		alert('天数要是数字');
				return false;
        	}
			return App.api('/user/blacklist_save', data).then(doneAndRequest).then(function(){
				form[0].reset();
			});
        };
        var edit = function(userId) {
        	var day = prompt('请输入新的禁言天数', '');
        	var data = {'userId': userId, day: day};
        	save(data);
        };
        var add = function() {
        	var data = form.serializeObject();
        	save(data);
        };
        var remove = function(userId) {
      	   if (!confirm(App.text('confirm.delete'))) {
      		   return;
      	   }
	       return App.api('/user/blacklist_remove', {'userId': userId}).done(doneAndRequest);
        };
        var submitIt = function () {
            App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
        };
		var init = function() {
			 $('#search-btn').click(submitIt);
			 $('#add-btn').click(add);
		     request();
		};
		init();
	});
</script>
<style>
</style>
<div class="panel panel-default">
	<div class="panel-heading">
		<form id="search-form" class="form-inline">
			<input type="text" name="userId" ng-model="REQUEST.userId" class="form-control" placeholder="用户ID"/>
			<input type="number" name="day" class="form-control" placeholder="天数" />
			<a href="javascript:;" class="btn btn-primary" id="search-btn" data-l10n-id="search"></a>
			<a href="javascript:;" class="btn btn-info" id="add-btn" data-l10n-id="add"></a>
		</form>
	</div>
	<div class="panel-body">
		<table class="table x-table table-main">
			<tr class="header">
			    <th style="width: 20%" data-l10n-id="id"></th>
                <th style="width: 30%" data-l10n-id="p.user.nick"></th>
                <th style="width: 20%" data-l10n-id="p.user.avatar"></th>
                <th style="width: 20%">截止日期</th>
                <th style="width: 10%" data-l10n-id="operations"></th>
			</tr>
			<tr class="template">
				<td>{{user.id}}</td>
				<td>{{user.nick}}</td>
				<td>{{user._avatar}}</td>
				<td>{{_expire}}</td>
				<td class="ops">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:;" data-op-type="edit" data-op-key="{{user.id}}">修改天数</a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="remove" data-op-key="{{user.id}}" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>