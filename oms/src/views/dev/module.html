<script>
	App.ready(function() {
		var hardwareType = App.router.getParameter('_t');
		var form = $('#save-form');
		App.menu.select('/dev/modules?_t=' + hardwareType);
		var loadModel = function() {
			var id = App.router.getParameter('id');
			if (!id) {
				return;
			}
			return App.api('/'+hardwareType+'/module', {'id' : id}, {
				success: function(model) {
					if ($.isEmptyObject(model)) {
						alert('The module does not exists or has been removed.');
						return false;
					}
					$('#save-form').renderModel({
						model : model
					});
				}
			});
		};
		var save = function() {
			var data = form.serializeObject();
			App.api(hardwareType + '/module_save', data).then(function() {
				App.router.go('/dev/modules?_t=' + hardwareType);
			});
		};
		var initButtons = function() {
			$('#save-btn').click(save);
		};
		var init = function() {
			$.when(loadModel()).then(initButtons);
		};

		init();
	});
</script>
<style>
#cover-preview img {
	width: 437px;
	height: 198px;
}
</style>
<form id="save-form" role="form" onsubmit="return false;">
	<div class="form-group">
		<label>模块：</label> <input type="text" name="id" class="form-control" ng-model="model.id" />
	</div>
	<div class="form-group">
		<label>排序：</label> <input type="text" name="priority" class="form-control" ng-model="model.priority" />
	</div>
	<div class="form-group">
		<label>最低版本：</label> <input type="text" name="minVersion" class="form-control" ng-model="model.minVersion" />
	</div>
	<div class="form-group">
		<label>Log等级：</label> <input type="text" name="logLevel" class="form-control" ng-model="model.logLevel" />
	</div>
	<div class="form-group">
		<label>升级时间(秒)：</label> <input type="text" name="upgradeTime" class="form-control" ng-model="model.upgradeTime" />
	</div>
	<div class="form-group">
		<label></label> <a id="save-btn" href="javascript:;" class="btn btn-primary">保存</a>
	</div>
</form>