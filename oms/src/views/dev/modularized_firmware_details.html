<script>
  App.ready(function () {
    var createdInfoDeviceTypeList = ['t5', 't6', 't7'];
    var hardwareType = App.router.getParameter('_t');
    var hardwareVersion = App.router.getParameter('hardware');
    var selectedUrl = '/dev/modularized_firmware_details?_t=' + hardwareType;
    if (hardwareVersion) {
      selectedUrl += '&hardware=' + hardwareVersion;
    }
    App.menu.select(selectedUrl);
    var request = function (offset) {
      App.scrollToTop();
      if (!U.isNull(offset)) {
        requestParams.offset = offset;
      }
      App.api('/' + hardwareType + '/firmware_details', requestParams).then(
        function (ps) {
          var list = ps.items || [];
          var table = $('#the-table');
          var template = table.attr('data-template');
          if (!template) {
            template = '<tr>' + table.find('tr.template').html() + '</tr>';
            table.attr('data-template', template);
          }
          table.find('tr:not(.header)').remove();
          for (var i = 0; i < list.length; i++) {
            var context = list[i];
            if (
              createdInfoDeviceTypeList.indexOf(hardwareType.toLowerCase()) >= 0
            ) {
              // 东八区与0时区的分钟差为(-16*80)
              context._createdAt = context.createdAt
                ? U.date.format(
                    U.date.parse(context.createdAt, -16 * 60),
                    'yyyy-MM-dd HH:mm:ss'
                  )
                : '-';
              context.uploader = context.uploader || '-';
            } else {
              context._createdAt = '-';
              context.uploader = '-';
            }
            var row = U.template(template, context);
            table.append(row);
          }
          initOperations();
          App.pagination(ps, request);
        }
      );
    };
    var initOperations = function () {
      $('[data-op-type=remove]').click(function () {
        var id = $(this).attr('data-op-key');
        remove(id);
      });
      $('[data-op-type=update]').click(function () {
        var id = $(this).attr('data-op-key');
        App.router.go('/dev/modularized_firmware_detail', {
          _t: hardwareType,
          id: id,
        });
      });
    };
    var remove = function (id) {
      var got = prompt(App.text('prompt.input', ['delete']), '');
      if (!got || got != 'delete') {
        return;
      }
      App.api('/' + hardwareType + '/firmware_detail_remove', { id: id }).then(
        function () {
          request(0);
        }
      );
    };
    var submitIt = function () {
      requestParams = $('#search-form').serializeObject();
      request();
    };
    var init = function () {
      $('#search-btn').click(submitIt);
      $('#create-btn').click(function () {
        var urlParam = { _t: hardwareType };
        if (hardwareVersion) {
          urlParam.hardware = hardwareVersion;
        } else {
          requestParams = $('#search-form').serializeObject();
          urlParam.hardware = requestParams.hardware || 1;
        }
        App.router.go('/dev/modularized_firmware_detail', urlParam);
      });
      submitIt();
    };
    init();
  });
</script>
<div class="panel panel-default x-panel">
  <div class="panel-heading">
    <form
      id="search-form"
      class="form-inline"
      action="#"
      method="get"
      onsubmit="return false;"
    >
      <input type="hidden" name="_t" ng-model="REQUEST._t" />
      <input type="hidden" name="offset" ng-model="REQUEST.offset" />
      <input type="hidden" name="limit" ng-model="REQUEST.limit" />
      硬件：<input
        type="text"
        name="hardware"
        class="form-control"
        ng-model="REQUEST.hardware"
        value="1"
      />
      模块：<input
        type="text"
        name="module"
        class="form-control"
        ng-model="REQUEST.module"
      />
      <a
        href="javascript:;"
        id="search-btn"
        class="btn btn-primary"
        data-l10n-id="search"
      ></a>
      <a
        href="javascript:;"
        class="btn btn-info"
        id="create-btn"
        data-l10n-id="add"
      ></a>
    </form>
  </div>
  <div class="panel-body">
    <table class="table table-hover x-table" id="the-table">
      <tr class="header">
        <th style="width: 5%">硬件</th>
        <th style="width: 10%">固件版本</th>
        <th style="width: 10%">模块</th>
        <th style="width: 20%">MD5</th>
        <th style="width: 10%">下载包</th>
        <th style="width: 20%">备注</th>
        <th style="width: 8%">上传时间</th>
        <th style="width: 7%">上传人</th>
        <th style="width: 10%" data-l10n-id="operations"></th>
      </tr>
      <tr class="template">
        <td>{{hardware}}</td>
        <td>{{version}}</td>
        <td>{{module}}</td>
        <td>{{file.digest}}</td>
        <td><a href="{{file.url}}" target="_blank">{{file.size}} 字节</a></td>
        <td>{{note}}</td>
        <td>{{_createdAt}}</td>
        <td>{{uploader}}</td>
        <td class="operations">
          <div class="dropdown">
            <a
              class="dropdown-toggle"
              data-toggle="dropdown"
              href="javascript:;"
            >
              <span data-l10n-id="operations"></span>
              <b class="caret"></b>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
              <li>
                <a
                  href="javascript:;"
                  data-op-type="update"
                  data-op-key="{{id}}"
                  data-l10n-id="edit"
                ></a>
              </li>
              <li>
                <a
                  href="javascript:;"
                  data-op-type="remove"
                  data-op-key="{{id}}"
                  data-l10n-id="delete"
                ></a>
              </li>
            </ul>
          </div>
        </td>
      </tr>
    </table>
  </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>
