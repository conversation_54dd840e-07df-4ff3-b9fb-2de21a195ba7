<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script>
  App.ready(function () {
    var hardwareType = App.router.getParameter('_t');
    var hardwareVersion = App.router.getParameter('hardware');
    var md5 = '';
    var oldMd5 = '';
    var needMd5DeviceList = [
      'd3',
      'd4',
      'd4s',
      't3',
      't4',
      'd4sh',
      'd4h',
      't5',
      't6',
      't7',
    ];
    var isNeedMd5 = needMd5DeviceList.indexOf(hardwareType) > -1;
    var form = $('#save-form');
    var selectedUrl = '/dev/modularized_firmware_details?_t=' + hardwareType;
    if (hardwareVersion) {
      selectedUrl += '&hardware=' + hardwareVersion;
    }
    App.menu.select(selectedUrl);
    var previewBinary = function (result) {
      var preview;
      preview = $('#binary-preview');
      if (result) {
        preview.html(
          '<a href="' + result.url + '" target="_blank">' + result.url + '</a>'
        );
      } else {
        preview.html('');
      }
      form.find('input[name="file.url"]').val(result ? result.url : '');
      form.find('input[name="file.size"]').val(result ? result.size : '');
      form.find('input[name="file.digest"]').val(result ? result.digest : '');
    };
    var result = null;

    App.upload.init({
      namespace: hardwareType + '-fw',
      type: 'firmware',
      id: 'upload-btn',
      fileUploaded: function (info) {
        // 硬件不支持https，只能用https，所以提交参数里面依然是http
        // 但是页面上，又不支持http的接口，因此需要做个临时性的转换才行
        var url = info.url.replace('http://', 'https://');
        $.ajax({
          url: url + '?hash/md5',
          dataType: 'json',
          success: function (ret) {
            if (isNeedMd5 && ret.md5 !== md5) {
              alert('上传的文件与md5校验文件内容不一致，请重新上传');
              return;
            }

            info.digest = ret.md5;
            result = info;
            previewBinary(result);
          },
        });
      },
      error: function (errTip) {
        alert('文件上传出错' + errTip);
      },
    });

    if (needMd5DeviceList.indexOf(hardwareType) > -1) {
      $('#upload-form').hide();
      $('#upload-md5').show();
      // 本地解析上传文件
      App.resolveFileContent.init({
        id: '#upload-md5-btn',
        fileChange: function (content) {
          // md5没有发生变化
          if (oldMd5 === content) return;

          previewBinary();
          $('#upload-form').hide();
          oldMd5 = md5;
          md5 = content;
          $('#md5-value').val(md5);
          if (md5) {
            $('#upload-form').show();
          }
        },
      });
    } else {
      $('#upload-md5').hide();
      $('#upload-form').show();
    }

    // 初始化form表单
    // console.log(form);
    form.renderModel({ model: { hardware: hardwareVersion } });

    var save = function () {
      if (isNeedMd5 && !md5) {
        alert('请先上传md5校验文件');
        return;
      }

      var data = form.serializeObject();
      data.hardware = hardwareVersion;

      // console.log(data);
      // return;

      if (!data.file.url) {
        alert('请上传固件模块包');
        return;
      }

      if (isNeedMd5 && data.file.digest !== md5) {
        alert(
          '提供的模块包文件的MD5与md5校验文件不一致！请重新上传模块包文件或者调整MD5校验文件'
        );
        return;
      }
      md5 && (data = Object.assign(data));

      App.api(hardwareType + '/firmware_detail_save', data).then(function () {
        App.router.go('/dev/modularized_firmware_details?_t=' + hardwareType);
      });
    };

    var loadModules = function () {
      const param = {};
      if (hardwareVersion) {
        param.hardware = hardwareVersion;
      }
      return App.api('/' + hardwareType + '/modules', param).then(function (
        list
      ) {
        var select = form.find('[name="module"]');
        $.each(list, function (index, item) {
          select.append(
            '<option value="' + item.id + '">' + item.id + '</option>'
          );
        });
      });
    };

    var loadModel = function () {
      var id = App.router.getParameter('id');
      if (!id) {
        return;
      }
      return App.api('/' + hardwareType + '/firmware_detail', { id: id }).then(
        function (data) {
          if ($.isEmptyObject(data)) {
            alert('The firmware doest not exists or has been removed');
            return false;
          }
          form.renderModel({ model: data });
          previewBinary(data.file);

          md5 = data.file.digest;
          oldMd5 = data.file.digest;
          $('#md5-value').val(md5);
          $('#upload-form').show();
        }
      );
    };

    var registeMd5ValueInput = function () {
      $('#md5-value').on('input', function (ev) {
        console.log(ev.target.value);
        md5 = ev.target.value.trim();
      });
    };

    //init
    loadModules()
      .then(loadModel)
      .then(function () {
        //init buttons
        if (hardwareVersion) {
          form.find('input[name="hardware"]').val(hardwareVersion);
        }
        $('#save-btn').click(save);
        registeMd5ValueInput();
        // if (__CONFIG__.oversea) {
        //   $("#upload-form").remove();
        // } else {
        //   $("#upload-form-oversea").remove();
        // }
      });
  });
</script>
<form id="save-form" role="form" onsubmit="return false;">
  <input type="hidden" name="id" ng-model="model.id" />
  <input type="hidden" name="file.url" ng-model="model.file.url" />
  <input type="hidden" name="file.size" ng-model="model.file.size" />
  <input type="hidden" name="file.digest" ng-model="model.file.digest" />

  <div class="form-group">
    <label>硬件</label>
    <input
      type="number"
      name="hardware"
      class="form-control"
      ng-model="model.hardware"
      disabled
    />
  </div>
  <div class="form-group">
    <label>版本号</label>
    <input
      type="number"
      name="version"
      class="form-control"
      ng-model="model.version"
    />
  </div>
  <div class="form-group">
    <label>模块</label>
    <select name="module" class="form-control" ng-model="model.module"></select>
  </div>
  <div class="form-group" id="upload-md5">
    <label data-l10n-id="md5File"></label>
    <div>
      <!-- <a href="javascript:;" id="upload-md5-btn">+选择上传MD5文件</a> -->
      <input id="upload-md5-btn" type="file" style="margin-bottom: 8px" />
      <input
        class="form-control"
        id="md5-value"
        placeholder="请输入MD5或者上传MD5文件"
        onchange=""
      />
      <!-- <p>已上传的md5内容：<span id="md5-value"></span></p> -->
    </div>
  </div>
  <div class="form-group" id="upload-form">
    <label data-l10n-id="file"></label>
    <div>
      <a href="javascript:;" id="upload-btn">+选择上传文件</a>
    </div>
    <div id="binary-preview"></div>
  </div>
  <div class="form-group">
    <label data-l10n-id="detail"></label>
    <textarea
      name="note"
      class="form-control"
      rows="5"
      ng-model="model.note"
    ></textarea>
  </div>
  <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
  <a
    href="javascript:window.history.back();"
    class="btn btn-info"
    data-l10n-id="cancel"
  ></a>
</form>
