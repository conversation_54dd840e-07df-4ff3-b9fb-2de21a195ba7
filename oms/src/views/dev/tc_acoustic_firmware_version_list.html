<script>
  App.ready(function () {
    var hardwareType = App.router.getParameter('_t');
    var hardware = App.router.getParameter('hardware');
    var hardwares = [1];
    var url =
      '/oms/device/' + hardwareType + '/tc-acoustic/firmware/version/list';
    var selectedUrl =
      '/dev/tc_acoustic_firmware_version_list?_t=' + hardwareType;
    if (!!hardware) {
      selectedUrl += '&hardware=' + hardware;
    }
    App.generator.generatePage(url, selectedUrl);
  });
</script>
<div style="height: calc(100vh - 60px)">
  <iframe id="iframe" frameborder="0" width="100%" height="100%"></iframe>
</div>
