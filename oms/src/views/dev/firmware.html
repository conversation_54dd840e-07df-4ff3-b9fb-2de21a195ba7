<script src="js/qiniu.min.js"></script>
<script src="vendor/plupload/plupload.full.min.js"></script>
<script src="js/localesaver.js"></script>
<script>
App.ready(function () {
	var hardwareType = App.router.getParameter('_t');
	App.menu.select('/dev/firmwares?_t=' + hardwareType);
    var form = $('#save-form');
    var localeSaver = new LocaleSaver({'dialogUrl': '/dev/firmware_locale.html'});
    
    var previewBinary = function (result) {
        var preview = $('#binary-preview');
        if (result) {
            preview.html('<a href="' + result.url + '" target="_blank">' + result.size + ' bytes, ' + App.text('download') + '</a>');
        } else {
            preview.html('');
        }
        form.find('input[name="file.url"]').val(result ? result.url : '');
        form.find('input[name="file.size"]').val(result ? result.size : '');
    };
    var renderModel = function(model) {
    	if (model.id) {
    		form.find('[name=version]').attr('readonly', 'readonly');
    	}
        form.renderModel(model);
        previewBinary(model.file);
        localeSaver.setDataByModel(model, ['note']);
        {
            var hardwareId = form.find('[name="hardware"]').val();
            $('[data-extra-container]').addClass('none');
            if (hardwareId) {
                $('[data-extra-container=' + hardwareType + '-' + hardwareId + ']').removeClass('none');
            }
        }
    };
    var loadModel = function () {
        var id = App.router.getParameter('id');
        if (!id) {
            return $.when(App.router.getParameters());
        }
        return App.api(hardwareType +'/firmware', {'id': id}, {
            success: function (data) {
                if ($.isEmptyObject(data)) {
                    alert('The firmware doest not exists or has been removed')
                    return false;
                }
            }
        });
    };    
    var save = function() {
  	    var data = form.serializeObject();
        var localData = localeSaver.getDataAsModel();
        for (var key in localData) {
      	   data[key] = localData[key];
        }
	  	App.api(hardwareType +'/firmware_save', data).then(function(){
	  	   App.router.go('/dev/firmwares', {'_t': hardwareType, 'hardware': data.hardware});
	  	});
    };
    var init = function() {
         App.upload.init({
            namespace: hardwareType + '-fw',
            type: 'file',
            id: 'upload-btn',
            fileUploaded: function (info) {
            	previewBinary(info);
            },
            error: function (errTip) {
                alert('文件上传出错' + errTip);
            }
        });
        localeSaver.init().then(loadModel).then(renderModel).then(function(){
             //init buttons
             $('#save-btn').click(save);
        });
    };
	init();
});
</script>
<form id="save-form" role="form" onsubmit="return false;">
    <input type="hidden" name="id" ng-model="id"/>
    <input type="hidden" name="file.url" ng-model="file.url"/>
    <input type="hidden" name="file.size" ng-model="file.size"/>
    <div class="form-group">
        <label>硬件</label>
        <input type="text" name="hardware" class="form-control" ng-model="hardware" readonly="readonly"/>
    </div>
    <div class="form-group">
        <label>版本号</label>
        <input type="text" name="version" class="form-control" ng-model="version"/>
    </div>
    <div data-extra-container="fit-1" class="none">
        <div class="form-group">
            <label>固件类型</label>
            <select name="extra.imageType" class="form-control" ng-model="extra.imageType">
                <option value="A">A</option>
                <option value="B">B</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label>公开</label>
        <select name="open" class="form-control" ng-model="open">
            <option value="0">内测</option>
            <option value="1">公开</option>
        </select>
    </div>
    <div class="form-group">
        <label data-l10n-id="file"></label>
        <div>
            <a href="javascript:;" id="upload-btn" data-l10n-id="file.selector"></a>
        </div>
        <div id="binary-preview"></div>
    </div>
    <div class="form-group">
	        <div class="controls">
	            <a href="javascript:;" id="local-add-btn">
	                <span>+</span>
	                <span data-l10n-id="localize"></span>
	            </a>
	            <div id="locale-spec-template">
	                <div class="locale-spec">
	                    <div class="locale-spec-item">
	                        <label data-l10n-id="locale"></label>:
	                        <span class="locale">{{locale.name}}</span>
	                        <a href="javascript:;" class="edit" data-l10n-id="edit" data-op-type="update" data-op-key="{{locale.key}}"></a>
	                        <a href="javascript:;" class="edit" data-l10n-id="delete" data-op-type="remove" data-op-key="{{locale.key}}"></a>
	                    </div>
	                    <div class="locale-spec-item">
	                    	<label>ReleaseNotes</label>:<span>{{spec.note}}</span><br/>
	                    </div>
	                </div>
	            </div>
	            <div id="locale-specs"></div>
	        </div>
	 </div>
     <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
     <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
</form>