<script>
  App.ready(function () {
    // var form = $('#search-form');
    // var requestParams;
    var hardwareType = App.router.getParameter('_t');
    var hardwares = [1];
    // App.menu.select('/dev/modularized_firmwares?_t=' + hardwareType);
    // var getAllHardware = function () {
    //     App.api(hardwareType + '/hardwares').then(function (res) {
    //         if (res != null && res.length > 0) {
    //             hardwares = res;
    //         }
    //         initHwSelect();
    //     });
    // };
    // var request = function (offset) {
    //     App.scrollToTop();
    //     if (!mars.isNull(offset)) {
    //         requestParams.offset = offset;
    //     }

    //     App.api(hardwareType + '/firmwares', requestParams).then(function (ps) {
    //         var list = ps.items || [];
    //         var table = $('#the-table');
    //         var template = table.attr('data-template');
    //         if (!template) {
    //             template = '<tr>'
    //                 + table.find('tr.template')
    //                     .html() + '</tr>';
    //             table.attr('data-template', template);
    //         }
    //         table.find('tr:not(.header)').remove();
    //         for (var i = 0; i < list.length; i++) {
    //             var context = list[i];
    //             context._force = context.forceUpgrade ? '强制' : '不强制';
    //             context._releaseNotes = mars.l10n.getBy(context.releaseNotes);
    //             context._open = context.open ? '公开' : '内测';
    //             // if (context.regionId == null || context.regionId === 0) {
    //             //     context._region = '美西';
    //             // } else if (context.regionId === 1) {
    //             //     context._region = '新加坡';
    //             // } else if (context.regionId === -1) {
    //             //     context._region = '全部';
    //             // }

    //             context._releaseNotes = mars.l10n.getBy(context.releaseNotes);
    //             context._appAlert = mars.l10n.getBy(context.appAlert);
    //             var details = context.details;
    //             if (details) {
    //                 var s = '';
    //                 for (var j = 0; j < details.length; j++) {
    //                     var f = details[j];
    //                     s += f.module + ': ' + f.version + '<br/>';
    //                 }
    //                 context._details = s;
    //             }
    //             var row = U.template(template, context);
    //             table.append(row);
    //             // if (hardwareType !== 'feedermini' && __CONFIG__.oversea) {
    //             //     $('[data-op-display=' + 'region' + ']').addClass('none');
    //             // }
    //         }
    //         initOperations();
    //         App.pagination(ps, request);
    //     });
    // };
    // var initOperations = function () {
    //     var hideKeys = ['open1', 'private0'];
    //     $.each(hideKeys, function (index, item) {
    //         $('[data-op-display=' + item + ']').addClass('none');
    //     });
    //     $('[data-op-type=remove]').click(function () {
    //         remove($(this).attr('data-op-key'));
    //     });
    //     $('[data-op-type=edit]').click(function () {
    //         var id = $(this).attr('data-op-key');
    //         App.router.go('/dev/modularized_firmware', {'_t': hardwareType, 'id': id});
    //     });
    //     $('[data-op-type=betadevices]').click(function () {
    //         var id = $(this).attr('data-op-key');
    //         App.router.go('/dev/betadevices', {'_t': hardwareType, 'firmwareId': id});
    //     });
    //     $('[data-op-type=notify]').click(function () {
    //         notify($(this).attr('data-op-key'));
    //     });
    // };
    // var remove = function (id) {
    //     var got = prompt(App.text('prompt.input', ['delete']), '');
    //     if (got != 'delete') {
    //         return;
    //     }
    //     App.api(hardwareType + '/firmware_remove', {'id': id}).then(function () {
    //         window.location.reload();
    //     });
    // };
    // var notify = function (id) {
    //     var deviceId = prompt('请输入设备ID', '');
    //     if (!deviceId) {
    //         return;
    //     }
    //     if (!confirm(App.text('confirm.operation'))) {
    //         return;
    //     }
    //     App.api(hardwareType + '/firmware_notify', {'firmwareId': id, 'deviceId': deviceId}).then(function () {
    //         alert('已发出通知');
    //     });
    // };
    // var create = function () {
    //     var hardware = parseInt(form.find('[name=hardware]').val());
    //     if (hardware <= 0) {
    //         alert('请选择硬件');
    //         return;
    //     }
    //     App.router.go('/dev/modularized_firmware', {'_t': hardwareType, 'hardware': hardware});
    // };

    // var submitIt = function () {
    //     App.router.go(App.router.getCurrentPath(), form.serializeObject());
    // };

    // var initHwSelect = function () {
    //     if (hardwareType == 'fit') {
    //         hardwares = [1, 2];
    //     }
    //     var hwSelect = form.find('[name=hardware]');
    //     for (var i = 0; i < hardwares.length; i++) {
    //         var hw = hardwares[i];
    //         hwSelect.append('<option value="' + hw + '">' + hw + '</option>');
    //     }
    //     var selectedHardware = App.router.getParameter('hardware') || hardwares[0];
    //     hwSelect.val(selectedHardware);
    // };

    // var init = function () {
    //     getAllHardware();
    //     requestParams = $.extend({'limit': 20}, form.serializeObject());
    //     requestParams.hardware = App.router.getParameter('hardware') || hardwares[0];
    //     if (requestParams.hardware == null || requestParams.hardware == undefined) {
    //         requestParams.hardware = 1;
    //     }
    //     request();
    //     $('#search-btn').click(submitIt);
    //     $("#search-key").keydown(function (e) {
    //         if (e.keyCode == 13) {
    //             submitIt();
    //         }
    //     });
    //     $('#create-btn').click(create);
    // };
    // init();
    App.generator.generatePage(
      '/oms/device/' + hardwareType + '/firmware_version',
      '/dev/modularized_firmwares?_t=' + hardwareType
    );
  });
</script>
<div style="height: calc(100vh - 60px)">
  <iframe id="iframe" frameborder="0" width="100%" height="100%"></iframe>
</div>
<!-- <div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
            <input type="hidden" name="_t" ng-model="REQUEST._t"/>
            <input type="hidden" name="offset" ng-model="REQUEST.offset"/>
            <input type="hidden" name="limit" ng-model="REQUEST.limit"/>
            <span>硬件</span>
            <select name="hardware" class="form-control" ng-model="REQUEST.hardware"></select>
            <input type="text" id="search-key" name="version" class="form-control" ng-model="REQUEST.version"
                   placeholder="固件版本号"/>
            <a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
            <a href="javascript:;" id="create-btn" class="btn btn-info" data-l10n-id="add"></a>
        </form>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <table class="table table-hover x-table" id="the-table">
                <tr class="header">
                    <th style="width: 5%">硬件</th>
                    <th style="width: 10%">版本号</th>
                    <th style="width: 18%">固件</th>
                    <th style="width: 10%">开放状态</th>
                    <th style="width: 20%">releaseNotes</th>
                    <th style="width: 10%">是否强制升级</th>
                    <th style="width: 10%">App最低版本</th>
                    <th style="width: 10%">App提示</th>
                    <th style="width: 10%" data-l10n-id="operations"></th>
                </tr>
                <tr class="template">
                    <td>{{hardware}}</td>
                    <td>{{version}}</td>
                    <td>{{_details}}</td>
                    <td>{{_open}}</td>
                    <td>{{_releaseNotes}}</td>
                    <td>{{_force}}</td>
                    <td>{{appMinVersion}}</td>
                    <td>{{_appAlert}}</td>
                    <td class="operations device-filter-wrap-{{deviceFilter}}">
                        <div class="dropdown">
                            <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;"><span
                                    data-l10n-id="operations"></span><b class="caret"></b></a>
                            <ul class="dropdown-menu dropdown-menu-right">
                                <li><a href="{{file.url}}" target="_blank" data-l10n-id="download"></a></li>
                                <li><a href="javascript:;" data-op-type="edit" data-op-key="{{id}}"
                                       data-l10n-id="edit"></a></li>
                                <li data-op-display="open{{open}}"><a href="javascript:;" data-op-type="betadevices"
                                                                      data-op-key="{{id}}">内测设备</a></li>
                                <li data-op-display="open{{open}}"><a href="javascript:;" data-op-type="notify"
                                                                      data-op-key="{{id}}">通知升级</a></li>
                                <li><a href="javascript:;" data-op-type="remove" data-op-key="{{id}}"
                                       data-l10n-id="delete"></a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>


</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div> -->
