<script>
App.ready(function(){
	var form = $('#search-form');
	var requestParams;
	var hardwareType = App.router.getParameter('_t');
	var hardwares = [1];
	App.menu.select('/dev/firmwares?_t=' + hardwareType);
	var request = function(offset) {
		App.scrollToTop();
		if (!mars.isNull(offset)) {
			requestParams.offset = offset;
		}
		App.api(hardwareType +'/firmwares', requestParams).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for ( var i = 0; i < list.length; i++) {
					var context = list[i];
					context._note = mars.l10n.getBy(context.note);
					context._open = context.open ? '公开' : '封闭';
					context._extra = context.extra ? (', '+ JSON.stringify(context.extra)) : '';
					var row = U.template(template, context);
					table.append(row);
				}
				initOperations();
				App.pagination(ps, request);
		});
	};
	var initOperations = function() {
		var hideKeys = ['open1', 'private0'];
		$.each(hideKeys, function(index, item){
			$('[data-op-display='+item+']').addClass('none');
		});
		$('[data-op-type=remove]').click(function() {
			remove($(this).attr('data-op-key'));
		});
		$('[data-op-type=edit]').click(function() {
			var id = $(this).attr('data-op-key');
			App.router.go('/dev/firmware', {'_t': hardwareType, 'id': id});
		});
		$('[data-op-type=betadevices]').click(function() {
			var id = $(this).attr('data-op-key');
			App.router.go('/dev/betadevices', {'_t': hardwareType, 'firmwareId': id});
		});
		$('[data-op-type=notify]').click(function() {
			notify($(this).attr('data-op-key'));
		});
	};
	var remove = function(id) {
		var got = prompt(App.text('prompt.input', ['delete']), '');
		if (got != 'delete') {
			return;
		}
		App.api(hardwareType + '/firmware_remove', {'id': id}).then(function(){
			window.location.reload();
		});
	};
	var notify = function(id) {
		var deviceId = prompt('请输入设备ID', '');
		if (!deviceId) {
			return;
		}
		if (!confirm(App.text('confirm.operation'))) {
			return;
		}
		App.api(hardwareType + '/firmware_notify', {'firmwareId': id, 'deviceId': deviceId}).then(function(){
			alert('已发出通知');
		});
	};
	var create = function() {
		var hardware = parseInt(form.find('[name=hardware]').val());
		if (hardware <= 0) {
			alert('请选择硬件');
			return;
		}
		App.router.go('/dev/firmware', {'_t': hardwareType, 'hardware': hardware});
	};
	var submitIt = function() {
		App.router.go(App.router.getCurrentPath(), form.serializeObject());
	};
	var init = function() {
		if (hardwareType == 'fit' || hardwareType == 'aq' || hardwareType == 'w5' || hardwareType == 'hg' || hardwareType == 'ctw3') {
			hardwares = [1, 2];
		}
		var hwSelect = form.find('[name=hardware]');
		for (var i = 0; i < hardwares.length; i++) {
			var hw = hardwares[i];
			hwSelect.append('<option value="' + hw + '">' + hw + '</option>');
		}
		var selectedHardware = App.router.getParameter('hardware') || hardwares[0];
		hwSelect.val(selectedHardware);
		requestParams = $.extend({'limit': 20}, form.serializeObject());
		request();
		$('#search-btn').click(submitIt);
        $("#search-key").keydown(function (e) {
            if (e.keyCode == 13) {
                submitIt();
            }
        });
		$('#create-btn').click(create);
	};
	init();
});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
			<input type="hidden" name="_t" ng-model="REQUEST._t"/>
			<input type="hidden" name="offset" ng-model="REQUEST.offset"/>
			<input type="hidden" name="limit" ng-model="REQUEST.limit"/>
			<span>硬件</span>
			<select name="hardware" class="form-control" ng-model="REQUEST.hardware"></select>
			<input type="text" id="search-key" name="version" class="form-control" ng-model="REQUEST.version" placeholder="固件版本号"/>
			<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
			<a href="javascript:;" id="create-btn" class="btn btn-info" data-l10n-id="add"></a>
		</form>
	</div>
	<div class="panel-body">
		<div class="table-responsive">
			<table class="table table-hover x-table" id="the-table">
				<tr class="header">
					<th style="width: 10%">硬件</th>
					<th style="width: 20%">版本号</th>
					<th style="width: 20%">大小</th>
					<th style="width: 10%">公开</th>
					<th style="width: 30%">ReleaseNotes</th>
					<th style="width: 10%" data-l10n-id="operations"></th>
				</tr>
				<tr class="template">
					<td>{{hardware}}</td>
					<td>{{version}}{{_extra}}</td>
					<td>{{file.size}}</td>
					<td>{{_open}}</td>
					<td>{{_note}}</td>
					<td class="operations device-filter-wrap-{{deviceFilter}}">
						<div class="dropdown">
							<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;"><span data-l10n-id="operations"></span><b class="caret"></b></a>
							<ul class="dropdown-menu dropdown-menu-right">
								<li><a href="{{file.url}}" target="_blank" data-l10n-id="download"></a></li>
								<li><a href="javascript:;" data-op-type="edit" data-op-key="{{id}}" data-l10n-id="edit"></a></li>
								<li data-op-display="open{{open}}"><a href="javascript:;" data-op-type="betadevices" data-op-key="{{id}}">内测设备</a></li>
								<li data-op-display="open{{open}}"><a href="javascript:;" data-op-type="notify" data-op-key="{{id}}">通知升级</a></li>
								<li><a href="javascript:;" data-op-type="remove" data-op-key="{{id}}" data-l10n-id="delete"></a></li>
							</ul>
						</div>
					</td>
				</tr>
			</table>
		</div>
	</div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>