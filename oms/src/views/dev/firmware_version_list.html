<script>
  App.ready(function () {
    var deviceType = App.router.getParameter('_t');
    var hardware = App.router.getParameter('hardware');
    var hardwares = [1];
    var url = '/oms/device/' + deviceType + '/firmware/version/list';
    var selectedUrl = '/dev/firmware_version_list?_t=' + deviceType;
    if (!!hardware) {
      selectedUrl += '&hardware=' + hardware;
    }
    console.log(url, selectedUrl);
    App.generator.generatePage(url, selectedUrl);
  });
</script>
<div style="height: calc(100vh - 60px)">
  <iframe id="iframe" frameborder="0" width="100%" height="100%"></iframe>
</div>
