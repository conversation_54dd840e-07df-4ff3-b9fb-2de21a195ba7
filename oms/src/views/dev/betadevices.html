<script>
App.ready(function(){
	var hardwareType = App.router.getParameter('_t');
	App.menu.select('/dev/firmwares?_t=' + hardwareType);
	var firmwareId = App.router.getParameter('firmwareId');
	var form = $('#search-form');
	var requestParams = $.extend({'limit': 20}, form.serializeObject());
	var request = function(offset) {
		App.scrollToTop();
		if (!mars.isNull(offset)) {
			requestParams.offset = offset;
		}
		App.api(hardwareType +'/firmware_betadevices', requestParams).then(function(ps) {
				var list = ps.items || [];
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>'
							+ table.find('tr.template')
									.html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for ( var i = 0; i < list.length; i++) {
					var deviceId = list[i];
					var context = {'deviceId': deviceId};
					var row = U.template(template, context);
					table.append(row);
				}
				initOperations();
				App.pagination(ps, request);
		});
	};
	var initOperations = function() {
		$('[data-op-type=remove]').click(function() {
			remove($(this).attr('data-op-key'));
		});
	};
	// var add = function() {
	// 	var deviceId = prompt('请输入设备ID', '');
	// 	if (!deviceId) {
	// 		return;
	// 	}
	// 	App.api(hardwareType +'/firmware_add_betadevice', {'firmwareId': firmwareId, 'deviceId': deviceId}).then(function(){
	// 		window.location.reload();
	// 	});
	// };

	var add = function() {
			App.openDialog({
				url : App.router.getRealPath('/dev/addfirmwaredevice.html'),
				noHeader : true,
				primaryClick : save
			});
	};

	var save = function() {
			var data = $('#dialog-form').serializeObject();
			data['firmwareId'] = firmwareId;
			App.api(hardwareType +'/firmware_add_betadevice', data).then(function(){
				window.location.reload();
			});
	};

	var remove = function(deviceId) {
		if (!confirm(App.text('confirm.operation'))) {
			return;
		}
		App.api(hardwareType +'/firmware_remove_betadevice', {'firmwareId': firmwareId, 'deviceId': deviceId}).then(function(){
			window.location.reload();
		});
	};
    var submitIt = function () {
        App.router.go(App.router.getCurrentPath(), $('#search-form').serializeObject());
    };
	var init = function() {
		request();
		$('#add-btn').click(add);
		$('#search-btn').click(submitIt);
        $("#search-key").keydown(function (e) {
            if (e.keyCode == 13) {
                submitIt();
            }
        });
	};
	init();
});
</script>
<div class="panel panel-default x-panel">
<div class="panel-heading">
	<form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
		<input type="hidden" name="_t" ng-model="REQUEST._t"/>
		<input type="hidden" name="offset" ng-model="REQUEST.offset"/>
		<input type="hidden" name="limit" ng-model="REQUEST.limit"/>
		<input type="hidden" name="firmwareId" ng-model="REQUEST.firmwareId"/>
		<input type="text" id="search-key" name="deviceId" class="form-control" ng-model="REQUEST.deviceId" placeholder="设备ID"/>
		<a href="javascript:;" id="search-btn" class="btn btn-primary" data-l10n-id="search"></a>
		<a href="javascript:;" id="add-btn" class="btn btn-warning" data-l10n-id="add"></a>
		<!--<a href="javascript:add()" class="btn btn-warning" data-l10n-id="add"></a>-->
		<a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="back"></a>
		<div class="clear"></div>
	</form>
</div>
<div class="panel-body"><div class="table-responsive">
<table class="table table-hover x-table" id="the-table">
	<tr class="header">
		<th style="width:50%">设备ID</th>
		<th style="width:50%" data-l10n-id="operations"></th>
	</tr>
	<tr class="template">
		<td>{{deviceId}}</td>
		<td class="operations">
			<a href="javascript:;" data-op-type="remove" data-op-key="{{deviceId}}" data-l10n-id="delete"></a>
		</td>
	</tr>
</table>
</div></div></div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>