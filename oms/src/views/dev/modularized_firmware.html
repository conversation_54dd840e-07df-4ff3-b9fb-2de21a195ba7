<script src="js/localesaver.js"></script>
<script>
    App.ready(function () {
        var hardwareType = App.router.getParameter('_t');
        var form = $('#save-form');
        var model;
        var localeSaver = new LocaleSaver({'dialogUrl': '/dev/modularized_firmware_locale.html'});
        App.menu.select('/dev/modularized_firmwares?_t=' + hardwareType);

        var loadModel = function () {
            var id = App.router.getParameter('id');
            if (!id) {
                model = App.router.getParameters();
                return $.when();
            }
            return App.api(hardwareType + '/firmware', {'id': id}, {
                success: function (data) {
                    if ($.isEmptyObject(data)) {
                        alert('The firmware doest not exists or has been removed')
                        return false;
                    }
                    model = data;
                }
            });
        };
        var renderModel = function () {
            if (model.id) {
                form.find('[name=version]').attr('readonly', 'readonly');
            }
            form.renderModel(model);
            // if (hardwareType !== 'feedermini'&&__CONFIG__.oversea) {
            //     form.find('[id="region"]').remove();
            // }
            previewBinary(model.file);
            localeSaver.setDataByModel(model, ['releaseNotes']);
            {
                var hardwareId = form.find('[name="hardware"]').val();
                $('[data-extra-container]').addClass('none');
                if (hardwareId) {
                    $('[data-extra-container=' + hardwareType + '-' + hardwareId + ']').removeClass('none');
                }
            }
        };
        var previewBinary = function (result) {
            var preview = $('#binary-preview');
            if (result) {
                preview.html('<a href="' + result.url + '" target="_blank">' + result.size + ' bytes, ' + App.text('download') + '</a>');
            } else {
                preview.html('');
            }
            form.find('input[name="file.url"]').val(result ? result.url : '');
            form.find('input[name="file.size"]').val(result ? result.size : '');
        };
        var save = function () {
            var data = form.serializeObject();
            var localData = localeSaver.getDataAsModel();
            for (var key in localData) {
                data[key] = localData[key];
            }
            if (!model.id) { //create case
                var firmwareSelects = $('[data-firmware-detail]') || [];
                var details = [];
                $.each(firmwareSelects, function (index, select) {
                    var detail = detailsMap[$(select).val()];
                    details.push(detail);
                });
                data['details'] = details;
            }
            App.api(hardwareType + '/firmware_save', data).then(function () {
                App.router.go('/dev/modularized_firmwares', {'_t': hardwareType, 'hardware': data.hardware});
            });
        };

        var detailsMap;
        var renderDetails = function (details, disabled) {
            var container = $('#firmware-container');
            detailsMap = {};
            $.each(details, function (index, detail) {
                var html = '<li><div>' + detail.module + '</div><select data-firmware-detail="1" class="form-control"' +
                    (disabled ? ' disabled="disabled"' : '') + '>';
                var items = detail.items ? detail.items : [detail];
                $.each(items, function (i, item) {
                    var name = '版本:' + item.version + (item.note ? '(' + item.note + ')' : '');
                    html += '<option value="' + item.id + '">' + name + '</option>';
                    detailsMap[item.id] = item;
                });
                html += '</select></li>';
                container.append(html);
            });
        };
        var loadModules = function () {
            if (model.id) { // update
                renderDetails(model.details || [], true);
            } else {
                return App.api(hardwareType + '/firmware_details_groupby_module', {'hardware': model.hardware}).then(function (items) {
                    renderDetails(items, false);
                });
            }
        };
        var init = function () {
            localeSaver.init().then(loadModel).then(renderModel).then(loadModules).then(function () {
                //init buttons
                $('#save-btn').click(save);
            });
            $('#detail-add-btn').click(function () {
                App.openDialog({
                    url: App.router.getRealPath('modularized_firmware_detail.html'),
                    context: {},
                    primaryClick: save,
                    beforeRender: function (body) {
                        var select = body.find('[name="locale"]');
                        $.each(localeList, function (index, item) {
                            select.append('<option value="' + item.key + '">'
                                + item.name + '</option>');
                        });
                    }
                });
            });
        };
        init();
    });
</script>
<form id="save-form" role="form" onsubmit="return false;">
    <input type="hidden" name="id" ng-model="id"/>
    <input type="hidden" name="file.url" ng-model="file.url"/>
    <input type="hidden" name="file.size" ng-model="file.size"/>
    <div class="form-group">
        <label>硬件</label>
        <input type="number" name="hardware" class="form-control" ng-model="hardware" readonly="readonly"/>
    </div>
    <div class="form-group">
        <label>版本号</label>
        <input type="number" name="version" class="form-control" ng-model="version"/>
    </div>
    <div class="form-group">
        <label>是否强制升级</label>
        <select name="forceUpgrade" class="form-control" ng-model="forceUpgrade">
            <option value="0">不强制</option>
            <option value="1">强制</option>
        </select>
    </div>
    <div class="form-group">
        <label>App要求最低版本号</label>
        <input type="number" name="appMinVersion" class="form-control" ng-model="appMinVersion"
               placeholder="格式如0.2, 1.0, 1.3.5"/>
    </div>
    <div class="form-group">
        <label>公开</label>
        <select name="open" class="form-control" ng-model="open">
            <option value="0">内测</option>
            <option value="1">公开</option>
        </select>
    </div>
<!--    <div class="form-group" id="region" >-->
<!--        <label>发布区域</label>-->
<!--        <select name="regionId" class="form-control" ng-model="regionId">-->
<!--            <option value="0">美西</option>-->
<!--            <option value="1">新加坡</option>-->
<!--        </select>-->
<!--    </div>-->
    <div class="form-group">
        <label>固件选择</label>
        <ul id="firmware-container">
        </ul>
    </div>
    <div class="form-group">
        <div class="controls">
            <a href="javascript:;" id="local-add-btn">
                <span>+</span>
                <span data-l10n-id="localize"></span>
            </a>
            <div id="locale-spec-template">
                <div class="locale-spec">
                    <div class="locale-spec-item">
                        <label data-l10n-id="locale"></label>:
                        <span class="locale">{{locale.name}}</span>
                        <a href="javascript:;" class="edit" data-l10n-id="edit" data-op-type="update"
                           data-op-key="{{locale.key}}"></a>
                        <a href="javascript:;" class="edit" data-l10n-id="delete" data-op-type="remove"
                           data-op-key="{{locale.key}}"></a>
                    </div>
                    <div class="locale-spec-item">
                        <label>ReleaseNotes</label>:<span>{{spec.releaseNotes}}</span><br/>
                    </div>
                </div>
            </div>
            <div id="locale-specs"></div>
        </div>
    </div>
    <a id="save-btn" class="btn btn-primary" data-l10n-id="save"></a>
    <a href="javascript:window.history.back();" class="btn btn-info" data-l10n-id="cancel"></a>
</form>
