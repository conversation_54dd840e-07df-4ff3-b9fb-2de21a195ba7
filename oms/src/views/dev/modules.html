<script>
	App.ready(function() {
		var hardwareType = App.router.getParameter('_t');
		App.menu.select('/dev/modules?_t=' + hardwareType);
		var params = App.router.getParameters();
		var request = function() {
			App.scrollToTop();
			App.api('/'+hardwareType+'/modules', params).then(function(list) {
				var table = $('#the-table');
				var template = table.attr('data-template');
				if (!template) {
					template = '<tr>' + table.find('tr.template').html() + '</tr>';
					table.attr('data-template', template);
				}
				table.find('tr:not(.header)').remove();
				for (var i = 0; i < list.length; i++) {
					var context = list[i];
					var row = U.template(template, context);
					table.append(row);
				}
				initOperations();
			});
		};
		var initOperations = function() {
			$('[data-op-type=remove]').click(function() {
				remove($(this).attr('data-op-key'));
			});
			$('[data-op-type=update]').click(function() {
				update($(this).attr('data-op-key'));
			});
		};
		var remove = function(id) {
			var got = prompt(App.text('prompt.input', ['delete']), '');
			if (!got || got!='delete') {
				return;
			}
			App.api('/'+hardwareType+'/module_remove', {'id' : id}).then(function() {
				request(0);
			});
		};
		var create = function() {
			App.router.go('/dev/module?_t=' + hardwareType);
		};
		var update = function(id) {
			App.router.go('/dev/module', {'_t':hardwareType, 'id': id});
		};
		var init = function() {
			$('#create-btn').click(create);
			request();
		};
		init();
	});
</script>
<div class="panel panel-default x-panel">
	<div class="panel-heading">
		<a href="javascript:;" id="create-btn" class="btn btn-info" data-l10n-id="create"></a>
	</div>
	<div class="panel-body">
		<table class="table table-hover x-table" id="the-table">
			<tr class="header">
				<th style="width: 20%">模块</th>
				<th style="width: 10%">排序</th>
				<th style="width: 10%">最小版本</th>
				<th style="width: 20%">Log等级</th>
				<th style="width: 20%">升级时间(秒)</th>
				<th style="width: 20%" data-l10n-id="operations"></th>
			</tr>
			<tr class="template">
				<td>{{id}}</td>
				<td>{{priority}}</td>
				<td>{{minVersion}}</td>
				<td>{{logLevel}}</td>
				<td>{{upgradeTime}}</td>
				<td class="operations">
					<div class="dropdown">
						<a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
							<span data-l10n-id="operations"></span>
							<b class="caret"></b>
						</a>
						<ul class="dropdown-menu dropdown-menu-right">
							<li><a href="javascript:;" data-op-type="update" data-op-key="{{id}}" data-l10n-id="edit"></a></li>
							<li><a href="javascript:;" data-op-type="remove" data-op-key="{{id}}" data-l10n-id="delete"></a></li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
<div class="clear h10"></div>