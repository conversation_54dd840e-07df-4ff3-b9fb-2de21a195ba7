<style type="text/css">
  #strings-container li span {
    padding-left: 10px;
  }
  #strings-container li a {
    padding-left: 5px;
  }
</style>
<script>
  App.ready(function() {
    //menu
    App.menu.select("/singapore/system/localebundles");
    var form = $("#save-form");
    var stringsContainer = $("#strings-container");

    var serializeItem = function(item) {
      item = $(item);
      var k = item.attr("data-key");
      var v = item.find("span").text();
      return { key: k, value: v };
    };
    var save = function() {
      var data = form.serializeObject();
      var table = {};
      stringsContainer.find("li").each(function(index, li) {
        var item = serializeItem(li);
        table[item["key"]] = item["value"];
      });
      data.table = JSON.stringify(table);
      App.api("/singapore/system/localebundle_save", data, {
        success: function(model) {
          App.router.go("/singapore/system/localebundles", {
            bundle: data.bundle
          });
        }
      });
    };
    var appendString = function(item) {
      var template =
        '<li data-key="{{key}}"><label>{{key}}</label>:<span>{{value}}</span>' +
        '<a href="javascript:;" data-op="edit">编辑</a>' +
        '<a href="javascript:;" data-op="del">删除</a></li>';
      var li = $(U.template(template, item));
      li.find("[data-op=edit]").click(function() {
        var item = serializeItem($(this).parent());
        saveString(item);
      });
      li.find("[data-op=del]").click(function() {
        $(this)
          .parent()
          .remove();
      });
      stringsContainer.append(li);
    };
    var locales = [];
    var loadLocales = function() {
      return App.api("/app/locales").then(function(result) {
        locales = result;
      });
    };
    var saveString = function(item) {
      App.openDialog({
        url: App.router.getRealPath("/singapore/system/localebundle_spec.html"),
        context: item,
        noHeader: true,
        primaryClick: function() {
          App.closeDialog();
          var dialog = $("#dialog-form");
          var item = dialog.serializeObject();
          if (!item["key"] || !item["value"]) {
            return;
          }
          var exists = stringsContainer.find(
            '[data-key="' + item["key"] + '"]'
          );
          if (exists[0]) {
            exists.remove();
          }
          appendString(item);
        },
        beforeRender: function(body) {
          var select = body.find('[name="key"]');
          $.each(locales, function(index, item) {
            select.append(
              '<option value="' + item.key + '">' + item.name + "</option>"
            );
          });
        }
      });
    };
    var renderModel = function(model) {
      form.renderModel(model);
      var table = model.table || {};
      for (var k in table) {
        var item = { key: k, value: table[k] };
        appendString(item);
      }
    };
    var findModel = function() {
      var key = App.router.getParameter("key");
      if (key) {
        var bundle = App.router.getParameter("bundle");
        return App.api(
          "/singapore/system/localebundle",
          { key: key, bundle: bundle },
          {
            success: function(model) {
              renderModel(model);
            }
          }
        );
      } else {
        return $.when(function() {
          renderModel({});
        });
      }
    };
    var init = function() {
      loadLocales()
        .then(findModel)
        .then(function() {
          $("#save-btn").click(save);
          $("#add-string-btn").click(function() {
            saveString();
          });
        });
    };
    init();
  });
</script>

<div class="panel panel-default x-panel">
  <div class="panel-body">
    <form id="save-form" role="form" onsubmit="return false;">
      <input type="hidden" name="bundle" ng-model="REQUEST.bundle" />
      <div class="form-group">
        <label>Key：</label>
        <div class="controls">
          <input name="key" ng-model="key" type="text" class="form-control" />
        </div>
      </div>
      <div class="form-group">
        <label
          >字串：<a href="javascript:;" id="add-string-btn"
            ><span>添加</span></a
          ></label
        >
        <div class="controls">
          <ul id="strings-container">
            <!-- <li><label>{{key}}</label>:<span>{{value}}</span></li> -->
          </ul>
        </div>
      </div>
      <div class="form-group">
        <label></label>
        <div class="controls">
          <a
            href="javascript:;"
            id="save-btn"
            class="btn btn-primary"
            data-l10n-id="save"
          ></a>
        </div>
      </div>
    </form>
  </div>
</div>
