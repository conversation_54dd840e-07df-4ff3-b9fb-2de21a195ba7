<script>
  App.ready(function() {
    var form = $("#form1");
    var requestParams = $.extend(
      { limit: 10, bundle: "defaults" },
      App.router.getParameters()
    );
    var bundle = requestParams["bundle"];
    var request = function(offset) {
      App.scrollToTop();
      if (!mars.isNull(offset)) {
        requestParams.offset = offset;
      }
      App.api("/singapore/system/localebundles", requestParams, {
        success: function(ps) {
          var items = ps.items || [];
          var table = $("#the-table");
          var template = table.attr("data-template");
          if (!template) {
            template = "<tr>" + table.find("tr.template").html() + "</tr>";
            table.attr("data-template", template);
          }
          table.find("tr:not(.header)").remove();
          for (var i = 0; i < items.length; i++) {
            var context = items[i];
            var strings = "";
            $.each(context.table, function(key, value) {
              strings += key + ": " + value + "<br/>";
            });
            context._table = strings;
            var row = U.template(template, context);
            table.append(row);
          }
          table.find("[data-op-type]").click(function() {
            var key = $(this).attr("data-op-key");
            App.router.go("/singapore/system/localebundle_save", {
              key: key,
              bundle: bundle
            });
          });
          App.pagination(ps, request);
        }
      });
    };
    var submitIt = function() {
      App.router.go(App.router.getCurrentPath(), form.serializeObject());
    };
    var init = function() {
      $("#search-btn").click(submitIt);
      form.find("[name=bundle]").change(submitIt);
      form.find("[name=key]").keydown(function(e) {
        if (e.keyCode == 13) {
          submitIt();
        }
      });
      $("#import-btn").click(function() {
        var bundleText = form
          .find("[name=bundle]")
          .find("option:selected")
          .text();
        if (!confirm("将导入到" + bundleText + "?")) {
          return;
        }
        App.router.go("/singapore/system/localebundle_import", {
          bundle: bundle
        });
      });
      $("#export-btn").attr(
        "href",
        App.apiPath(
          "/singapore/system/localebundle_export",
          $.extend({ bundle: bundle }, App.specialApiParams())
        )
      );
      $("#export-btn").attr("target", "_blank");
      request();
    };
    init();
  });
</script>
<div class="panel panel-default">
  <div class="panel-heading">
    <form
      id="form1"
      class="form-inline"
      action="#"
      method="get"
      onsubmit="return false;"
    >
      <select name="bundle" ng-model="REQUEST.bundle" class="form-control">
        <option value="defaults">默认表</option
        ><option value="page">页面表</option>
      </select>
      <input
        type="text"
        class="form-control"
        name="key"
        placeholder="Key"
        ng-model="REQUEST.key"
      />
      <a href="javascript:;" id="search-btn" class="btn btn-primary">搜索</a>
      <a href="javascript:;" id="export-btn" class="btn btn-info">导出</a>
      <a href="javascript:;" id="import-btn" class="btn btn-warning">导入</a>
      <a href="#/singapore/system/localebundles_region" class="btn btn-danger"
        >Region字串</a
      >
    </form>
  </div>
  <div class="panel-body">
    <table class="table table-hover x-table" id="the-table">
      <tr class="header">
        <th style="width: 20%">Key</th>
        <th style="width: 65%">字串</th>
        <th style="width: 15%" data-l10n-id="operations"></th>
      </tr>
      <tr class="template">
        <td>{{key}}</td>
        <td>{{_table}}</td>
        <td class="operations">
          <div class="dropdown">
            <a
              class="dropdown-toggle"
              data-toggle="dropdown"
              href="javascript:;"
            >
              <span data-l10n-id="operations"></span>
              <b class="caret"></b>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
              <li>
                <a href="javascript:;" data-op-type="edit" data-op-key="{{key}}"
                  >修改</a
                >
              </li>
            </ul>
          </div>
        </td>
      </tr>
    </table>
  </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>
