<link rel="stylesheet" type="text/css" href="vendor/select2/select2.css" />
<script src="vendor/select2/select2.min.js"></script>
<script src="js/region-select.js"></script>
<script src="js/miscuploader.js"></script>
<script>
  App.ready(function() {
    App.menu.select("/singapore/system/localebundles");
    var select;
    var openImportDialog = function() {
      App.openDialog({
        url: App.router.getRealPath(
          "singapore/system/localebundles_region_upload.html"
        ),
        noHeader: true,
        primaryClick: doImport
      });
    };
    var getBundleName = function() {
      var region = select.getValue();
      if (mars.isEmpty(region)) {
        region = "0";
      }
      return "region_" + region;
    };
    var doImport = function() {
      var form = $("#dialog-form");
      var data = { bundle: getBundleName() };
      App.uploadFile({
        fileElement: form.find('[name="file"]')[0],
        data: data,
        url: "/singapore/system/localebundle_import",
        success: function(result) {
          alert("导入成功");
          App.closeDialog();
        }
      });
    };
    var doExport = function() {
      window.location.target = "_blank";
      window.location.href = App.apiPath(
        "/singapore/system/localebundle_export",
        $.extend({ bundle: getBundleName() }, App.specialApiParams())
      );
    };
    var init = function() {
      select = new RegionSelect("regionselect");
      select.init().then(function() {
        $("#export-btn").click(doExport);
        $("#import-btn").click(openImportDialog);
      });
    };
    init();
  });
</script>
<div class="panel panel-default x-panel">
  <div class="panel-body">
    <form id="save-form" class="form">
      <div class="form-group">
        <label>地区</label>
        <select
          class="form-control"
          name="region"
          id="regionselect-s1"
        ></select>
      </div>
      <a class="btn btn-primary" href="javascript:;" id="export-btn">导出</a>
      <a class="btn btn-warning" href="javascript:;" id="import-btn">导入</a>
      <a class="btn btn-info" href="javascript:window.history.back();">返回</a>
    </form>
  </div>
</div>
