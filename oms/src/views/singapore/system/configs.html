<script src="js/miscuploader.js"></script>
<script>
  App.ready(function() {
    var form = $("#search-form");
    var params = form.serializeObject();
    var configMap;
    var configTypes = null;
    var loadTypes = function() {
      if (configTypes) {
        return;
      } else {
        return App.api("/singapore/system/configtypes", null, {
          success: function(result) {
            configTypes = result;
          }
        });
      }
    };
    var update = function(key) {
      var config = null;
      if (key) {
        config = configMap[key];
      } else {
        config = {};
      }
      $.when(loadTypes()).then(function() {
        var reloadDialog = function(body) {
          var select = body.find('[name="type"]');
          if ($(select).val() == "blob") {
            body.find("#value-wrap").addClass("none");
            body.find("#file-wrap").removeClass("none");
          } else {
            body.find("#value-wrap").removeClass("none");
            body.find("#file-wrap").addClass("none");
          }
        };
        App.openDialog({
          url: App.router.getRealPath("/singapore/system/config.html"),
          context: config,
          noHeader: true,
          primaryClick: save,
          beforeShow: function(body) {
            var select = body.find('[name="type"]');
            $.each(configTypes, function(index, item) {
              select.append(
                '<option value="' + item + '">' + item + "</option>"
              );
            });
            if (!$.isEmptyObject(config)) {
              // update mode
              select.val(config.type);
              select.change(function() {
                this.selectedIndex = this.defaultIndex;
              });
              body.find('[name="key"]').attr("readonly", "readonly");
            } else {
              // create mode
              select.val("string");
              select.change(function() {
                reloadDialog(body);
              });
            }
            reloadDialog(body);
          }
        });
      });
    };
    var save = function() {
      var form = $("#dialog-form");
      var data = $.extend(form.serializeObject(), {
        tableName: params["tableName"]
      });
      if (data["type"] == "blob") {
        App.uploadFile({
          fileElement: form.find('[name="file"]')[0],
          data: { cfgAsString: JSON.stringify(data) },
          url: "/singapore/system/saveblobconfig",
          success: reload
        });
      } else {
        delete data["file"];
        App.api("/singapore/system/saveconfig", data, { success: reload });
      }
    };
    var reload = function() {
      App.closeDialog();
      request(0);
    };
    var remove = function(key) {
      if (!confirm("确定要删除：" + key)) {
        return;
      }
      App.api(
        "/singapore/system/removeconfig",
        {
          tableName: params["tableName"],
          key: key
        },
        { success: reload }
      );
    };
    var request = function(offset) {
      App.scrollToTop();
      var requestParams = $.extend({ limit: 20 }, params, {
        offset: U.isNull(offset) ? 0 : offset
      });
      App.api("/singapore/system/configs", requestParams, {
        success: function(ps) {
          var items = ps.items || [];
          var table = $("#the-table");
          var template = table.attr("data-template");
          if (!template) {
            template = "<tr>" + table.find("tr.template").html() + "</tr>";
            table.attr("data-template", template);
          }
          table.find("tr:not(.header)").remove();
          configMap = {};
          for (var i = 0; i < items.length; i++) {
            var context = items[i];
            configMap[context.key] = context;
            var row = U.template(template, context);
            table.append(row);
          }
          table.find('[data-op-type="update"]').click(function() {
            var key = $(this).attr("data-op-key");
            update(key);
          });
          table.find('[data-op-type="remove"]').click(function() {
            remove($(this).attr("data-op-key"));
          });
          App.pagination(ps, request);
        }
      });
    };
    var submitIt = function() {
      App.router.go(App.router.getCurrentPath(), form.serializeObject());
    };
    var init = function() {
      $("#create-btn").click(function() {
        update();
      });
      $("#select-table").change(submitIt);
      $("#search-btn").click(submitIt);
      $("#search-key").keydown(function(e) {
        if (e.keyCode == 13) {
          submitIt();
        }
      });
      request();
    };
    init();
  });
</script>
<div class="panel panel-default x-panel">
  <div class="panel-heading">
    <form
      id="search-form"
      class="form-inline"
      action="#"
      method="get"
      onsubmit="return false;"
    >
      <input
        id="search-key"
        type="text"
        class="form-control"
        name="key"
        placeholder="key"
        ng-model="REQUEST.key"
      />
      <a
        href="javascript:;"
        id="search-btn"
        class="btn btn-primary"
        data-l10n-id="search"
      ></a>
      <a
        href="javascript:;"
        id="create-btn"
        class="btn btn-success"
        style="margin-left: 5px;"
        data-l10n-id="create"
      ></a>
      <select
        id="select-table"
        name="tableName"
        class="form-control"
        style="float: right;"
        ng-model="REQUEST.tableName"
      >
        <option value="api_config">api_config</option>
        <option value="api_misc_config">api_misc_config</option>
        <option value="pim_node_cfg">pim_node_cfg</option>
        <option value="pim_node_region_cfg">pim_node_region_cfg</option>
        <option value="pim_master_cfg">pim_master_cfg</option>
      </select>
    </form>
  </div>
  <div class="panel-body">
    <table class="table table-hover x-table" id="the-table">
      <tr class="header">
        <th style="width: 20%">Key</th>
        <th style="width: 15%">类型</th>
        <th style="width: 35%">值</th>
        <th style="width: 15%">备注</th>
        <th style="width: 15%" data-l10n-id="operations"></th>
      </tr>
      <tr class="template">
        <td>{{key}}</td>
        <td>{{type}}</td>
        <td>{{value}}</td>
        <td>{{name}}</td>
        <td class="operations">
          <div class="dropdown">
            <a
              class="dropdown-toggle"
              data-toggle="dropdown"
              href="javascript:;"
            >
              <span data-l10n-id="operations"></span>
              <b class="caret"></b>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
              <li>
                <a
                  href="javascript:;"
                  data-op-type="update"
                  data-op-key="{{key}}"
                  >修改</a
                >
              </li>
              <li>
                <a
                  href="javascript:;"
                  data-op-type="remove"
                  data-op-key="{{key}}"
                  >删除</a
                >
              </li>
            </ul>
          </div>
        </td>
      </tr>
    </table>
  </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>
