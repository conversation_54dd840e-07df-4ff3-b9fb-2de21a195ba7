<script src="js/miscuploader.js"></script>
<script>
    App.ready(function () {
        App.menu.select("/singapore/system/localebundles");
        var params = App.router.getParameters();
        params['oauthSign'] = "petkit";
        var submit = function () {
            App.uploadFile({
                fileElement: $("#file")[0],
                url: window.location.host.includes("sandbox") ? "http://api-sandbox.petktasia.com/latest/adm/system/localebundle_import" : "http://api.petktasia.com/latest/adm/system/localebundle_import",
                data: params,
                success: function (result) {
                    alert("导入成功");
                    App.router.go("/singapore/system/localebundles", params);
                }
            });
        };
        $("#submit-btn").click(submit);
    });
</script>

<form id="form1" role="form" onsubmit="return false;">
    <div class="form-group">
        <label>选择文件：</label>
        <input type="file" id="file" name="file" class="form-control"/>
    </div>
    <a id="submit-btn" class="btn btn-primary">导入</a>
    <a href="javascript:window.history.back()" class="btn btn-info btn-back"
    >返回</a
    >
</form>
