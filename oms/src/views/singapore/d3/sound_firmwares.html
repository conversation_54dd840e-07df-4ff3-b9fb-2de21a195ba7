<script>
    App.ready(function() {
        App.menu.select('/singapore/d3/sound_firmwares');
        var request = function(offset) {
            App.scrollToTop();
            if (!U.isNull(offset)) {
                requestParams.offset = offset;
            }
            App.api('/singapore/d3/sound_firmwares', requestParams).then(function(ps) {
                var list = ps.items || [];
                var table = $('#the-table');
                var template = table.attr('data-template');
                if (!template) {
                    template = '<tr>' + table.find('tr.template').html() + '</tr>';
                    table.attr('data-template', template);
                }
                table.find('tr:not(.header)').remove();
                for (var i = 0; i < list.length; i++) {
                    var context = list[i];
                    var row = U.template(template, context);
                    table.append(row);
                }
                initOperations();
                App.pagination(ps, request);
            });
        };
        var initOperations = function() {
            $('[data-op-type=remove]').click(function() {
                var id = $(this).attr('data-op-key');
                remove(id);
            });
            $('[data-op-type=update]').click(function() {
                var id = $(this).attr('data-op-key');
                App.router.go('/singapore/d3/sound_firmware', {'id': id});
            });
        };
        var remove = function(id) {
            var got = prompt(App.text('prompt.input', ['delete']), '');
            if (!got || got!='delete') {
                return;
            }
            App.api('/singapore/d3/sound_firmware_delete', {'id' : id}).then(function() {
                request(0);
            });
        };
        var submitIt = function() {
            requestParams = $('#search-form').serializeObject();
            request();
        };
        var init = function() {
            $('#search-btn').click(submitIt);
            $('#create-btn').click(function() {
                App.router.go('/singapore/d3/sound_firmware');
            });
            submitIt();
        };
        init();
    });
</script>
<div class="panel panel-default x-panel">
    <div class="panel-heading">
        <form id="search-form" class="form-inline" action="#" method="get" onsubmit="return false;">
            <a href="javascript:;" class="btn btn-info" id="create-btn" data-l10n-id="add"></a>
        </form>
    </div>
    <div class="panel-body">
        <table class="table table-hover x-table" id="the-table">
            <tr class="header">
                <th style="width: 20%">音频版本</th>
                <th style="width: 20%">MD5</th>
                <th style="width: 20%">下载包</th>
                <th style="width: 20%">备注</th>
                <th style="width: 20%" data-l10n-id="operations"></th>
            </tr>
            <tr class="template">
                <td>{{version}}</td>
                <td>{{digest}}</td>
                <td><a href="{{url}}" target="_blank">{{size}} 字节</a></td>
                <td>{{note}}</td>
                <td class="operations">
                    <div class="dropdown">
                        <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                            <span data-l10n-id="operations"></span>
                            <b class="caret"></b>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="javascript:;" data-op-type="update" data-op-key="{{id}}" data-l10n-id="edit"></a>
                            </li>
                            <li>
                                <a href="javascript:;" data-op-type="remove" data-op-key="{{id}}" data-l10n-id="delete"></a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear h10"></div>
<div id="pagination-wrap"></div>