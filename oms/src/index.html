<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title></title>
    <link rel="stylesheet" href="vendor/bootstrap/css/bootstrap.min.css" />
    <link
      rel="stylesheet"
      href="vendor/metis/font-awesome/css/font-awesome.css"
    />
    <link rel="stylesheet" href="vendor/metis/css/sb-admin.css" />
    <link rel="stylesheet" href="style/common.css" />
    <link rel="stylesheet" href="vendor/pagination/pagination.css" />
    <style>
      html,
      body {
        width: 100%;
        height: 100%;
      }
      body {
        background-color: #293038;
      }
    </style>
    <script src="vendor/jquery/jquery-1.9.1.min.js"></script>
    <script src="vendor/jquery/jquery.mobile.event.min.js"></script>
    <script src="vendor/jquery/jquery.cookie.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>
    <script src="vendor/pagination/jquery.pagination.js"></script>
    <script src="js/mars.js"></script>
    <script src="js/config.js"></script>
    <script src="js/menu.js"></script>
    <script src="js/metis-menu.js"></script>
  </head>
  <body>
    <div class="header">
      <div class="brand-wrapper">
        <div class="btn-group">
          <a
            href="javascript:;"
            data-toggle="tooltip"
            data-placement="bottom"
            class="btn btn-default btn-sm btn-toggle-menu"
          >
            <i class="fa fa-bars"></i
            ><!-- data-original-title="打开/关闭左侧菜单" -->
          </a>
        </div>
        <a class="brand">
          <span data-l10n-id="index.title" class="span-name"></span>
          <span id="header-version"></span>
        </a>
      </div>
      <ul class="nav navbar-top-links navbar-right">
        <span id="header-user"></span>
        <li class="dropdown">
          <a
            class="dropdown-toggle"
            data-toggle="dropdown"
            href="#"
            aria-expanded="true"
          >
            <i class="fa fa-user fa-fw"></i> <i class="fa fa-caret-down"></i>
          </a>
          <ul class="dropdown-menu dropdown-user">
            <li>
              <a href="#/admin/updatepassword"
                ><i class="fa fa-gear fa-fw"></i
                ><span data-l10n-id="password.update"></span
              ></a>
            </li>
            <li class="divider"></li>
            <li>
              <a href="#/admin/locale_select"
                ><i class="fa fa-wrench fa-fw"></i
                ><span data-l10n-id="locale"></span
              ></a>
            </li>
            <li class="divider"></li>
            <li>
              <a href="#/admin/logout"
                ><i class="fa fa-sign-out fa-fw"></i
                ><span data-l10n-id="logout"></span
              ></a>
            </li>
          </ul>
        </li>
      </ul>
    </div>

    <div class="center">
      <div class="left">
        <div class="left-wrapper">
          <ul class="nav" id="side-menu"></ul>
        </div>
      </div>
      <div id="page-wrapper">
        <div id="page"></div>
      </div>
    </div>

    <script>
      (function ($) {
        var version = "V6.5.20161020";
        $("#header-version").text(version);
        var otherScripts = ["js/app.js", "js/app.misc.js"];
        var loadScriptOptions = {
          cache: true,
          version: version,
          error: function () {
            alert("Bad network, please retry.");
          },
        };
        //enable log
        mars.enableLog(__CONFIG__.enableLog);
        //load locale file
        var preferedLocales = ["en", "zh"];
        var supportedLocales = ["en", "zh", "es"];
        var loadLocales = [];
        $.each(preferedLocales, function (index, item) {
          loadLocales.push(item);
        });
        var userLocale = mars.l10n.getRootLocale(
          $.cookie("locale") || mars.l10n.getBrowserLocale()
        );
        if (
          $.inArray(userLocale, loadLocales) < 0 &&
          $.inArray(userLocale, supportedLocales) >= 0
        ) {
          loadLocales.push(userLocale);
        }
        var localeUrls = [];
        for (var i = 0; i < loadLocales.length; i++) {
          localeUrls.push("locale/" + loadLocales[i] + ".js");
        }
        mars.l10n.setPreferredLocales(preferedLocales);
        mars.l10n.setLocale(userLocale);
        //load app scripts
        mars.loadResources(localeUrls, loadScriptOptions).then(function () {
          mars.loadResources(otherScripts, loadScriptOptions);
        });
      })(jQuery);
    </script>
  </body>
</html>
