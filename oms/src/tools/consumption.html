<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title></title>
    <link rel="stylesheet" href="../vendor/bootstrap/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="../style/common.css"/>
    <style type="text/css">
    	body {
    		padding: 10px;
    	}
    </style>
    <script src="../vendor/jquery/jquery-1.9.1.min.js"></script>
    <script src="../js/mars.js"></script>
    <script>var U = mars;</script>
    <script>
    	$(document).ready(function(){
			var dom = $('#results');
    		var template = dom.html();
    		var calc = function(weight, idealWeight) {
    			var p = (weight / idealWeight);
        		return 105 * Math.pow(weight, 0.75) * 3.2 * (Math.pow(2.718, -0.87 * p) - 0.1);
    		};
    		var render = function(context) {
    			var html = U.template(template, context);
    			dom.html(html).css({'display': ''});
    		};
    		var parseRequest = function() {
        		var req = $('#save-form').serializeObject();
        		for (var key in req) {
        			req[key] = parseFloat(req[key]);
        		}
        		req.energy = req.energy * 0.001;
        		return req;
    		};
    		$('#calc-new-btn').click(function(){
    			var req = parseRequest();
    			var idealWeight = (req.w1 + req.w2)/4 + req.weight/2;
    			if (idealWeight < req.w1) {
    				idealWeight = req.w1;
    			} else if (idealWeight > req.w2) {
    				idealWeight = req.w2;
    			}
    			var consumption = calc(req.weight, idealWeight) / req.energy;
    			var context = {'consumption': {'1': consumption, '2':consumption, '3':consumption}, 'idealWeight': idealWeight};
    			render(context);
    		});
    		$('#calc-old-btn').click(function(){
    			var req = parseRequest();
    			var idealWeight1 = (req.w1 + req.w2)/2;
    			var idealWeight2 = (req.weight < req.w1) ? req.w1 : (req.weight > req.w2 ? req.w2 : req.weight);
    			var c1 = calc(req.weight, idealWeight1) / req.energy;
    			var c2 = calc(req.weight, idealWeight2) / req.energy;
    			var context = {'consumption': {'1': c1, '2':c2, '3':c1}};
    			render(context);
    		});
    		$('#clear-btn').click(function(){
    			dom.html('');
    		});
    	});
    </script>
</head>
<body>
	<form id="save-form" role="form" onsubmit="return false;">
	     <div class="form-group">
	        <label>体重(KG)</label>
	        <div class="controls">
	            <input type="text" name="weight" class="form-control" value="9"/>
	        </div>
	     </div>
	     <div class="form-group">
	        <label>理想体重(下限)</label>
	        <div class="controls">
	            <input type="text" name="w1" class="form-control" value="9"/>
	        </div>
	     </div>
	     <div class="form-group">
	        <label>理想体重(上限)</label>
	        <div class="controls">
	            <input type="text" name="w2" class="form-control" value="11"/>
	        </div>
	     </div>
	     <div class="form-group">
	        <label>单位狗粮卡路里</label>
	        <div class="controls">
	            <input type="text" name="energy" class="form-control" value="3800"/>
	        </div>
	     </div>
	     <div class="form-group">
	        <label></label>
	        <div class="controls">
	           	<a href="javascript:;" id="calc-old-btn" class="btn btn-info">狗粮消耗(旧理想体重算法)</a>
	           	<a href="javascript:;" id="calc-new-btn" class="btn btn-primary">狗粮消耗(新理想体重算法)</a>
	           	<a href="javascript:;" id="clear-btn" class="btn btn-warning">清空结果</a>
	        </div>
	     </div>
	     <div class="form-group">
	        <label></label>
	        <div class="controls" id="results" style="display:none;">
	          	未成年：{{consumption.1}}g
	          	成年：{{consumption.2}}g
	          	老年：{{consumption.3}}g
	          	理想体重：{{idealWeight}}
	        </div>
	     </div>
    </form>
</body>
</html>