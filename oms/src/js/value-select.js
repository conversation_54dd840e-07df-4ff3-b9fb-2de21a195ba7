function ValueSelect(prefix, list) {
    var selector = '#' + prefix ;
    var depth = 0;
    var listeners = [];


    var checkInit = function () {
        if (!initialized) {
            throw 'Could not do anything else before init completed';
        }
    };

    var getSelect = function () {
        return  $(selector);
    };

    var renderSelect = function (list, initValue) {
        var select = getSelect();
        select.html('');
        for (var i = 0; i < list.length; i++) {
            var item = list[i];
            select.append('<option value="' + item.key + '">' + item.value
                + '</option>');
        }
        if (!mars.isNull(initValue)) {
            select.val(initValue).trigger("change");
        }
    };

    var onChanged = function (select) {
        select = $(select);
        var code = select.val();
        for (var i = 0; i < listeners.length; i++) {
            listeners[i](select, code);
        }
    };

    this.setValue = function (v) {
        getSelect().val(v).change();
    };


    this.setItem = function (v) {
        renderSelect(v, null);
    };


    this.getValue = function () {
        checkInit();
        for (var i = depth; i >= 1; i--) {
            var select = getSelect(i);
            var code = select.val();
            if (code) {
                return code;
            }
        }
        return null;
    };

    this.init = function (options) {
        if (!options) {
            options = {};
        }
        var select = getSelect();
        select.change(function () {
            onChanged(this);
        });
        renderSelect(list, options.value);

    };

    this.addChangeListener = function (listener) {
        // checkInit();
        listeners.push(listener);
    };
}