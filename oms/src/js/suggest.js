function Suggest(textBoxID,url,options) {
	var _this=this;
	this.resultArray = null;
	this.suggestResultMenu = null;
	this.resultFocusIndex = -1;
	this.defaultResultArray=null;
	this.suggestResultID="suggest_result_id_unique";
	this.suggestResultMenuItemID="suggest_result_menuitem_id_unique";
	
	var getPosition=function(element) {
		element=element[0];
		var result = { x: element.offsetLeft, y: element.offsetTop };
		element = element.offsetParent;
		while (element) {
			result.x += element.offsetLeft;
			result.y += element.offsetTop;
			element = element.offsetParent;
		}
		return result;
	};
	
	if(typeof options=='undefined'){options={};}
	this.textBox =$(textBoxID);
	this.url=url;
	this.handleClick=options.handleClick||function(o){};
	this.handleFocus=options.handleFocus||function(item){};
	this.handleAppend=options.handleAppend||function(item,data){item.innerHTML=data;};
	this.handleEnter=options.handleEnter||function(o){};
	this.allowEmpty=options.allowEmpty||0;
	this.handleLayout=options.handleLayout||function(control){
		var tb = _this.textBox;
		var pos=getPosition(tb);
		control.style.width = tb[0].clientWidth+ "px";
		control.style.left = pos.x + "px";     
		control.style.top = (pos.y + tb[0].clientHeight + 2) + "px";
	}
	
	this.doSuggest = function (code) {
		if (code == 38) {// up
			this.handleUpKey();
		} else {
			if (code == 40) {// down
				this.handleDownKey();
			} else {
				if (code == 13) {// enter
					this.handleEnterKey();
				} else {
					this.handleMainWork();
				}
			}
		}
	};
	
	// clear suggest results
	this.clearSuggest = function () {
		if (this.suggestResultMenu != null) {
			$('#' + this.suggestResultID).remove();
			this.suggestResultMenu = null;
		}
		this.resultArray = null;
		this.resultFocusIndex = -1;
	};
		
	// click specified suggest menuItem
	this.clickMenuItem=function(index){;
		if(this.resultArray==null)return;
		this.handleClick(this.resultArray[index]);
		this.clearSuggest();
	};
	
	// focus specified suggest menuItem
	this.focusMenuItem=function(index){
		if(this.resultArray==null)return;
		for (var i = 0; i < this.resultArray.length; i++) {
			var item = $('#' + this.suggestResultMenuItemID + i);
			if (i == index) {
				item.addClass('suggest-item-on');
			} else {
				item.removeClass('suggest-item-on');
			}
		}
		this.handleFocus(this.resultArray[index]);
	};
	
	this.handleMainWork=function (){
		this.clearSuggest();
		var tip = this.textBox.val().trim();
		if ((tip =='')&&(this.allowEmpty==0)) {
			return;
		}
		App.api(url, {'s': tip}, {loading: false}).then(function (list) {
			  _this.resultArray=list;
			  _this.buildSuggest();
		});
	};
	this.buildSuggest=function (){
		if(this.resultArray == null || this.resultArray.length == 0) {
			this.clearSuggest();return;
		}
		if (this.suggestResultMenu == null) {
			this.suggestResultMenu = this.createSuggestMenu();
		}else{
		     this.suggestResultMenu.innerHTML='';	
		}
		for (var i = 0; i < this.resultArray.length; i++) {
			var item=document.createElement('div');
			item.i=i;
			item.id=this.suggestResultMenuItemID+ i;
			item.className='suggest-item';
			item.onclick=function(){
				_this.clickMenuItem(this.i);
			};
			item.onmouseover=function(){
				_this.focusMenuItem(this.i);
			};
			this.handleAppend(item,this.resultArray[i]);
			this.suggestResultMenu.appendChild(item);
		}
		this.resultFocusIndex = -1;
		// this.focusMenuItem(0);
	}
	this.createSuggestMenu=function (){
		var newControl = document.createElement("div");
		newControl.id = this.suggestResultID;
		newControl.className='suggest-container';
		this.handleLayout(newControl);
		document.body.appendChild(newControl);
		return newControl;
	}
	this.handleUpKey=function (){
		if (this.resultArray == null) {
			return;
		}
		if (this.resultFocusIndex < 1) {
			this.resultFocusIndex = this.resultArray.length;
		}
		this.resultFocusIndex--;
		this.focusMenuItem(this.resultFocusIndex);
	}
	this.handleDownKey=function (){
		if (this.resultArray == null) {
			return;
		}
		if (this.resultFocusIndex >= this.resultArray.length - 1) {
			this.resultFocusIndex = -1;
		}
		this.resultFocusIndex++;
		this.focusMenuItem(this.resultFocusIndex);
	}
	this.handleEnterKey=function (){
		var index=this.resultFocusIndex;
		this.handleEnter(index>-1?this.resultArray[index]:null);
		this.clearSuggest();
	}
}