// alias mars
window.U = mars;
// define app
var App = (function ($) {
  var _loadingCount = 0;

  var getText = function (key, args) {
    var text = mars.l10n.get(key, args);
    if (mars.isNull(text)) {
      return key;
    }
    return text;
  };

  var preloader = function () {
    _loadingCount++;
    // mars.log('preloader: ' + _loadingCount);
    // mars.log(arguments.callee.caller);
    var mask = document.getElementById("loading-indicator-mask");
    if (mask) {
      $(mask).css({
        display: "block",
      });
    } else {
      try {
        $('<div id="loading-indicator-mask"/>')
          .appendTo(document.body)
          .append('<div id="loading-indicator"/>');
      } catch (e) {}
    }
  };
  var closePreloader = function () {
    _loadingCount--;
    // mars.log('closePreloader: ' + _loadingCount);
    // mars.log(arguments.callee.caller);
    if (_loadingCount > 0) {
      return;
    }
    var mask = document.getElementById("loading-indicator-mask");
    if (mask) {
      $(mask).css({
        display: "none",
      });
    }
  };

  var openDialog = function (options) {
    preloader();
    options = options || {};
    if (!options.url) {
      throw "Bad url for dialog";
    }
    var dialogId = "dialog";
    var dialog = document.getElementById(dialogId);
    if (dialog) {
      dialog = $(dialog);
    } else {
      dialog = $(
        '<div id="' +
          dialogId +
          '" class="modal fade" role="dialog" aria-hidden="true" data-keyboard="false" data-backdrop="static"><div class="modal-dialog"><div class="modal-content"></div></div></div>'
      );
    }
    dialog.attr("data-closable", options.notClosable ? "0" : "1");
    var container = dialog.find(".modal-content");
    var header;
    if (!options.showXBtn) {
      header = $(
        '<div class="modal-header"><h4 class="modal-title"></h4></div>'
      );
    } else {
      header = $(
        '<div class="modal-header"><button type="button" class="close"><span>&times;</span><span class="sr-only">Close</span></button><h4 class="modal-title"></h4></div>'
      );
    }
    var footer = $('<div class="modal-footer"></div>');
    var type = options.type || "default";

    var buttons;
    if (type == "default") {
      buttons = [
        {
          cssClass: "btn-default",
          textKey: options.cancelTextKey || "cancel",
          dismiss: true,
        },
        {
          cssClass: "btn-primary",
          textKey: options.primaryTextKey || "ok",
          click: options.primaryClick,
        },
      ];
    } else if (type == "alert") {
      buttons = [
        {
          cssClass: "btn-default",
          textKey: options.primaryTextKey || "ok",
          dismiss: true,
        },
      ];
    } else if (type == "single") {
      buttons = [
        {
          cssClass: "btn-primary",
          textKey: options.primaryTextKey || "ok",
          click: options.primaryClick,
          enter: options.primaryEnter,
        },
      ];
    } else if (type == "custom") {
      buttons = options.buttons;
    } else {
      throw "Unknown dialog type: " + type;
    }
    for (var i = 0; i < buttons.length; i++) {
      var btnCfg = buttons[i];
      var cssClass = btnCfg.cssClass ? btnCfg.cssClass : "";
      if (cssClass) {
        cssClass = "btn " + cssClass;
      } else {
        cssClass = "btn";
      }
      var button = $(
        '<button type="button" class="' +
          cssClass +
          '">' +
          getText(btnCfg.textKey) +
          "</button>"
      );
      if (btnCfg.enter) {
        $(document).keypress(function (e) {
          if (e.keyCode == 13) {
            btnCfg.enter();
          }
        });
      }
      if (btnCfg.dismiss) {
        button.click(closeDialog);
      } else if (btnCfg.click) {
        button.click(btnCfg.click);
      }
      footer.append(button);
    }
    container.empty();
    container.load(options.url, function () {
      var bodyInnerHTML = container.html();

      container.empty();
      if (!options.noHeader) {
        header
          .find(".modal-title")
          .html(getText(options.titleTextKey) || "&nbsp;");
        header.find("button").click(closeDialog);
        container.append(header);
      }

      var body = $('<div class="modal-body"></div>');
      body.html(bodyInnerHTML);
      container.append(body);
      if (options.beforeRender) {
        options.beforeRender(body, container);
      }
      mars.l10n.translate(body);
      body.renderModel(options.context);

      container.append(footer);
      closePreloader();
      if (options.beforeShow) {
        options.beforeShow(body, container);
      }
      dialog.modal("show");
      options.afterRender && options.afterRender();
    });
  };

  var closeDialog = function () {
    var dialog = $("#dialog");
    if (!dialog[0]) {
      return;
    }
    if (dialog.attr("data-closable") === "1") {
      dialog.modal("hide");
    }
  };

  var pagination = function (ps, callback, options) {
    var offset = ps.offset;
    var limit = ps.limit;
    var total = ps.total;
    options = options || {};

    $(options.container || "#pagination-wrap").pagination(total, {
      items_per_page: limit,
      current_page: offset / limit,
      num_edge_entries: 5,
      num_display_entries: 8,
      prev_text: getText("pagination.lastpage"),
      next_text: getText("pagination.nextpage"),
      callback: function (pageIndex) {
        callback && callback(pageIndex * limit);
        return false;
      },
    });
  };

  var scrollToTop = function () {
    $(document.body).animate({
      scrollTop: 0,
    });
  };

  var promise = function (cb) {
    var defer = $.Deferred();
    setTimeout(function () {
      var ret = cb();
      if (ret === false) {
        defer.reject();
      } else {
        defer.resolve();
      }
    }, 0);
    return defer.promise();
  };

  var getSessionId = function () {
    return $.cookie("sess");
  };

  var login = function () {
    var data = $("#login-form").serializeObject();
    api("/admin/login", data, {
      success: function (session) {
        var cookiePath = mars.router.getBasePath();
        if (!mars.string.startsWith(cookiePath, "/")) {
          cookiePath = "/" + cookiePath;
        }
        if (!window.localStorage) {
          alert("Your browser is out of date");
          return false;
        }
        window.localStorage.permissions = session.permissions;
        $.cookie("sess", session.id, {
          path: __CONFIG__.appPath,
        });
        var expiresDate = new Date();
        expiresDate = new Date(
          expiresDate.getTime() + 30 * 24 * 60 * 60 * 1000
        );
        $.cookie("username", data["username"], {
          path: cookiePath,
          expires: expiresDate,
        });
        mars.router.go("/welcome");
      },
    });
  };

  var showLoginDialog = function () {
    // location.href = 'views/login1.html';
    openDialog({
      url: mars.router.getRealPath("/admin/login.html"),
      notClosable: true,
      type: "custom",
      buttons: [
        {
          cssClass: "btn-info",
          textKey: "password.recover",
          click: function () {
            App.router.go("/admin/recoverpassword");
          },
        },
        {
          cssClass: "btn-primary",
          textKey: "login",
          click: login,
          enter: login,
        },
      ],
      titleTextKey: "login",
      showXBtn: false,
      context: {
        username: $.cookie("username"),
      },
    });
  };

  var specialApiParams = function () {
    var params = {};
    var session = getSessionId();
    if (session) {
      params["X-Admin-Session"] = session;
    }
    var locale = mars.router.getParameter("X-Locale") || $.cookie("locale");
    if (locale) {
      params["X-Locale"] = locale;
    }
    return params;
  };

  var defaultError = {
    code: 0,
    msg: getText("network.error"),
  };

  var api = function (path, data, options) {
    var defer = $.Deferred();
    options = options ? $.extend({}, options) : {};

    var server = options.server || "api";
    if (path.charAt(0) === "/") {
      path = path.substring(1);
    }
    var url = null;
    // console.log(__CONFIG__);
    if (server === "api") {
      url = __CONFIG__.api + "adm/" + path;
    } else if (server === "new-api") {
      url = __CONFIG__.api + path;
    } else {
      url = __CONFIG__.api + "proxy/" + server;
    }

    var loading = options.loading;
    if (typeof loading == "undefined") {
      loading = true;
    }
    if (loading) {
      preloader();
    }
    var success = options.success || function () {};
    var userError =
      options.error ||
      function (err) {
        if (mars.isNull(err) || mars.isNull(err.msg)) {
          err = defaultError;
        }
        alert(err.msg);
      };
    var error = function (err) {
      if (!err) {
        err = defaultError;
      }
      if (err.code === 5 || err.code === 6) {
        showLoginDialog();
      } else {
        userError(err);
      }
    };
    if (mars.isNull(data)) {
      data = {};
    } else if (typeof data === "string") {
      var dataArray = data.split("&");
      var dataAsMap = {};
      for (var i = 0; i < dataArray.length; i++) {
        var dataItem = dataArray[i].split("=");
        var value = mars.isNull(dataItem[1]) ? dataItem[1] : "";
        dataAsMap[dataItem[0]] = decodeURIComponent(value);
      }
      data = dataAsMap;
    }
    data = $.extend(data, specialApiParams());
    if (server !== "api") {
      data["__uri__"] = path;
    }
    var trimmedData = {};
    for (var key in data) {
      var value = data[key];
      if (mars.isNull(value)) {
        value = "";
      }
      trimmedData[key] = value;
    }
    data = trimmedData;
    var dataAsStr = null;
    if (!mars.isNull(data)) {
      var s = "";
      for (var key in data) {
        if (s.length > 0) {
          s += "&";
        }
        var v = data[key];
        if ($.isPlainObject(v) || $.isArray(v)) {
          v = JSON.stringify(v);
        }
        s += key + "=" + encodeURIComponent(v);
      }
      dataAsStr = s;
    }
    var ajaxOptions = {
      url: url,
      type: options.type || "post",
      data: dataAsStr,
      // beforeSend: function(request) {
      // 	request.setRequestHeader('x-api-version', '9.0.0');
      // },
      error: function (xhr) {
        if (loading) {
          closePreloader();
        }
        var err = null;
        try {
          err = mars.isNull(xhr.responseText)
            ? null
            : eval("(" + xhr.responseText + ")");
        } catch (e) {}
        error(err);
        defer.reject();
      },
      success: function (obj) {
        if (loading) {
          closePreloader();
        }
        var err = obj.error;
        if (err) {
          error(err);
          return;
        }
        var ret = success(obj.result);
        if (ret === false) {
          defer.reject();
        } else {
          defer.resolve(obj.result);
        }
      },
    };
    $.ajax($.extend(options, ajaxOptions));
    return defer.promise();
  };

  var getResourcePath = function (file) {
    var s = window.__CONFIG__.appPath;
    if (s.endsWith("/")) {
      s = s.substring(0, s.length - 1);
    }
    if (!file.startsWith("/")) {
      s += "/";
    }
    s += file;
    return s;
  };

  var apiPath = function (path, params) {
    if (path.charAt(0) === "/") {
      path = path.substring(1);
    }
    var url = __CONFIG__.api + "adm/" + path;
    var i = path.indexOf("?") >= 0 ? 1 : 0;
    if (params) {
      for (var key in params) {
        var value = params[key];
        if (value === undefined) {
          continue;
        }
        if (value === null) {
          value = "";
        }
        if (i === 0) {
          url += "?";
        } else {
          url += "&";
        }
        url += key + "=" + encodeURIComponent(value);
        i++;
      }
    }
    return url;
  };

  var renderPage = function (context) {
    $(mars.router.getContainer()).renderModel(context);
  };

  var menu;
  var initMenu = function () {
    var menuBox = $(".left");
    $(".btn-toggle-menu").click(function () {
      if (menuBox.hasClass("hide")) {
        menuBox.show();
        $("#page-wrapper").css(
          "margin-left",
          $(window).width() < 768 ? 215 : 250
        );
        menuBox.removeClass("hide");
      } else {
        menuBox.hide();
        $("#page-wrapper").css("margin-left", 15);
        menuBox.addClass("hide");
      }
    });
    if ($(window).width() < 768) {
      menuBox.hide();
      // $('#page-wrapper').css('margin-left',0);
      menuBox.addClass("hide");
      menuBox.width(200);
      $(".left .left-wrapper").width(215);
      $(".left .nav").width(200);
      $("#page-wrapper").css("margin-left", 15);
      $("body").on("swiperight", function () {
        if (menuBox.hasClass("hide")) {
          menuBox.show();
          $("#page-wrapper").css(
            "margin-left",
            $(window).width() < 768 ? 215 : 235
          );
          menuBox.removeClass("hide");
        }
      });
      $("body").on("swipeleft", function () {
        if (!menuBox.hasClass("hide")) {
          menuBox.hide();
          $("#page-wrapper").css("margin-left", 15);
          menuBox.addClass("hide");
        }
      });
    }
    $(window).resize(function () {
      if ($(window).width() < 768) {
        menuBox.hide();
        menuBox.width(200);
        $(".left .left-wrapper").width(215);
        $(".left .nav").width(200);
        $("#page-wrapper").css("margin-left", 15);
        menuBox.addClass("hide");
      } else {
        menuBox.show();
        menuBox.width(235);
        $(".left .left-wrapper").width(250);
        $(".left .nav").width(235);
        $("#page-wrapper").css("margin-left", 250);
        menuBox.removeClass("hide");
      }
    });

    menu = new MetisMenu({
      container: "#side-menu",
      menus: __MENU__,
    });
  };

  var initPostMessageListener = function () {
    window.addEventListener("message", function (event) {
      if (!event.data) return;
      var type = event.data.type;
      var content = event.data.content;
      console.log(content);
      switch (type) {
        case "redirect":
          // window.location.href = '#' + content.redirectUrl;
          mars.router.go(content.redirectUrl);
          break;
        case "jump":
          var contentId = event.data.contentId;
          window.open(location.href + "?contentId=" + contentId);
          break;
        default:
          break;
      }
    });
  };

  var _pageLoaded = false;
  var ready = function (func) {
    if (_pageLoaded) {
      func();
      return;
    }
    $(mars.router.getContainer()).on("pageready", func);
  };
  var init = function () {
    // translate
    mars.l10n.translate(document.body);
    // tooltip & header & menu
    $('[data-toggle="tooltip"]').tooltip();
    document.title = getText("index.title");
    $("#header-user").html($.cookie("username"));
    initMenu();
    // init router
    mars.router.init({
      forceRedirect: false,
      loadScriptOptions: {
        cache: true,
        version: __CONFIG__.version,
      },
      handler: function (path) {
        if (path === "/") {
          mars.router.go("/welcome");
          return false;
        }
        if (path === "/admin/recoverpassword") {
          return true;
        }
        var session = getSessionId();
        if (!session) {
          showLoginDialog();
          return false;
        }
        menu.select(path);
        return true;
      },
      pageBeforeLoad: function () {
        preloader();
        $(mars.router.getContainer()).off("pageready");
        _pageLoaded = false;
      },
      pageLoadError: function (xhr, status, err) {
        if (err) {
          mars.log(err);
        } else if (xhr) {
          mars.log(xhr);
        }
        closePreloader();
        $(mars.router.getContainer()).html("");
        alert(getText("network.error"));
      },
      pageBeforeShow: function () {
        var context = {
          REQUEST: mars.router.getParameters(),
        };
        var container = mars.router.getContainer();
        mars.l10n.translate(container);
        $(container).renderModel(context);
      },
      pageLoadSuccess: function () {
        closePreloader();
        scrollToTop();
        $(mars.router.getContainer()).trigger("pageready");
        _pageLoaded = true;
      },
    });
    // 初始化postmessage监听器
    initPostMessageListener();
  };
  init();

  return {
    ready: ready,
    text: getText,
    preloader: preloader,
    closePreloader: closePreloader,
    openDialog: openDialog,
    closeDialog: closeDialog,
    pagination: pagination,
    router: mars.router,
    generator: mars.generator,
    renderPage: renderPage,
    menu: menu,
    promise: promise,
    api: api,
    apiPath: apiPath,
    resourcePath: getResourcePath,
    specialApiParams: specialApiParams,
    scrollToTop: scrollToTop,
  };
})(jQuery);
