var MetisMenu = function (options) {
  var ALL_PERMISSIONS = '.*';
  var $this = $(options.container);
  var myPermissions = null;

  var checkPermission = function (destPermission) {
    if (myPermissions === ALL_PERMISSIONS) {
      return true;
    }
    var destPermissions = destPermission.split('|');
    for (var i = 0; i < destPermissions.length; i++) {
      var dest = destPermissions[i];
      if (dest.charAt(0) == '/') {
        dest = dest.substring(1);
      }
      for (var k = 0; k < myPermissions.length; k++) {
        var p = myPermissions[k];
        if (p.indexOf(dest) === 0) {
          return true;
        }
      }
    }
    return false;
  };

  var getAuthorizedMenus = function (menus) {
    var authorizedMenus = [];
    for (var i = 0; i < menus.length; i++) {
      var item = menus[i];
      var destPermission = item.permission || item.href;
      if (!checkPermission(destPermission)) {
        continue;
      }
      if (item.children) {
        var subItems = getAuthorizedMenus(item.children);
        if (subItems.length == 0) {
          continue;
        }
        item.children = subItems;
      }
      authorizedMenus.push(item);
    }
    return authorizedMenus;
  };

  var checkMenu = function (menus) {
    for (var i = 0; i < menus.length; i++) {
      var item = menus[i];
      if (!item.title) {
        throw 'No title specified for menu: ' + JSON.stringify(item);
      }
      if (!item.href) {
        throw 'No href specified for menu: ' + item.title;
      }
      if (item.children) {
        checkMenu(item.children);
      }
    }
    return menus;
  };

  var buildMenu = function (options) {
    var menus = options.menus;
    checkMenu(menus);
    myPermissions = window.localStorage.permissions;
    //permissions = $.cookie('permissions');
    //permissions = permissions ? decodeURIComponent(permissions) : null;
    if (mars.isNull(myPermissions)) {
      return;
    }
    var myMenu = null;
    if (myPermissions === '.*') {
      myMenu = menus;
    } else {
      myPermissions = myPermissions.split('|');
      console.log(myPermissions);
      myMenu = getAuthorizedMenus(menus);
    }
    if (!myMenu) {
      return;
    }
    var noChildTemplate =
      '<a href="#{{href}}" ng-router><i class="fa{{cssClass}} fa-fw" style="margin-right:5px;"></i>{{title}}</a>';
    var withChildrenTemplate =
      '<a href="javascript:;"><i class="fa{{cssClass}} fa-fw" style="margin-right:5px;"></i>{{title}}<span class="fa arrow"></span></a>';
    var withChildrenNoIconTemplate =
      '<a href="javascript:;">{{title}}<span class="fa arrow"></span></a>';
    var childTemplate = '<li><a href="#{{href}}" ng-router>{{title}}</a></li>';

    for (var i = 0; i < myMenu.length; i++) {
      var item = myMenu[i];
      var template = null;
      var html = '<li>';
      if (!item.children) {
        html += mars.template(noChildTemplate, {
          href: item.href,
          title: mars.l10n.get(item.title),
          cssClass: item.cssClass ? ' ' + item.cssClass : '',
        });
      } else {
        html += mars.template(withChildrenTemplate, {
          title: mars.l10n.get(item.title),
          cssClass: item.cssClass ? ' ' + item.cssClass : '',
        });
        html += '<ul class="nav nav-second-level">';
        for (var j = 0; j < item.children.length; j++) {
          var subItem = item.children[j];
          if (subItem.children && subItem.children.length) {
            html +=
              '<li>' +
              mars.template(withChildrenNoIconTemplate, {
                title: mars.l10n.get(subItem.title),
                cssClass: subItem.cssClass ? ' ' + subItem.cssClass : '',
              });
            html += '<ul class="nav nav-third-level">';
            for (var k = 0; k < subItem.children.length; k++) {
              var subSubItem = subItem.children[k];
              //   console.log("subSubItem", subSubItem);
              html += mars.template(childTemplate, {
                href: subSubItem.href,
                title: mars.l10n.get(subSubItem.title),
              });
            }
            html += '</ul></li>';
          } else {
            html += mars.template(childTemplate, {
              href: subItem.href,
              title: mars.l10n.get(subItem.title),
            });
          }
        }
        html += '</ul>';
      }
      html += '</li>';
      $this.append(html);
    }
    // const dealWithLoop = function (menuList) {
    // }

    // dealWithLoop(myMenu);

    $this
      .find('li')
      .has('ul')
      .children('a')
      .on('click', function (e) {
        e.preventDefault();
        $(this)
          .parent('li')
          .toggleClass('active')
          .children('ul')
          .collapse('toggle');
        $(this)
          .parent('li')
          .siblings()
          .removeClass('active')
          .children('ul.in')
          .collapse('hide');
      });
    $this.find('li').has('ul').children('ul').addClass('collapse');
  };

  var selectMenu = function (currentPath) {
    var currentMenuItem = null;
    $this.find('a[ng-router]').each(function () {
      var menuItem = $(this);
      //var path = mars.router.getPathName(menuItem.attr('href'));
      var path = menuItem.attr('href').substring(1);
      if (path == currentPath) {
        menuItem.parent().addClass('open');
        menuItem.css({
          'background-color': '#0099cc',
          color: '#fff',
        });
        currentMenuItem = menuItem;
      } else {
        menuItem.parent().removeClass('open');
        menuItem.css({
          'background-color': '#293038',
          color: '#fff',
        });
        menuItem
          .parentsUntil($this, 'li')
          .removeClass('active')
          .has('ul')
          .children('ul')
          .removeClass('in');
      }
    });
    if (currentMenuItem) {
      currentMenuItem
        .parentsUntil($this, 'li')
        .addClass('active')
        .has('ul')
        .children('ul')
        .addClass('in');
    }
  };

  buildMenu(options);

  this.select = selectMenu;
  this.checkPermssion = checkPermission;
};

$(window).bind('load resize', function () {
  width =
    this.window.innerWidth > 0 ? this.window.innerWidth : this.screen.width;
  if (width < 768) {
    $('div.sidebar-collapse').addClass('collapse');
  } else {
    $('div.sidebar-collapse').removeClass('collapse');
  }
});
// jQuery.metisMenu = (function($){
// var init = function(options) {
// // $this.find('li.active').has('ul').children('ul').addClass('collapse in');
// //
// $this.find('li').not('.active').has('ul').children('ul').addClass('collapse');
// //
// // $this.find('li').has('ul').children('a').on('click', function (e) {
// // e.preventDefault();
// //
// $(this).parent('li').toggleClass('active').children('ul').collapse('toggle');
// //
// $(this).parent('li').siblings().removeClass('active').children('ul.in').collapse('hide');
// // });
// };
//
// return {
// init: init,
// selectMenu: selectMenu
// };
//
// })(jQuery);
