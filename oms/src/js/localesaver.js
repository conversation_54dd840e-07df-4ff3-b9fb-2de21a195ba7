function LocaleSaver(options) {
	var localeList = [];
	var localeMap = {};
	var specs = {};
	var container = $('#locale-specs');
	var save = function() {
		App.closeDialog();
		var dialog = $('#dialog-form');
		var data = dialog.serializeObject();
		var locale = data.locale;
		if (!locale) {
			return;
		}
		var spec = data.spec;
		specs[locale] = spec;
		show(locale, spec);
	};
	var update = function() {
		var locale = $(this).attr('data-op-key');
		openDialog(locale);
	};
	var remove = function() {
		var locale = $(this).attr('data-op-key');
		delete specs[locale];
		container.find('[data-locale="' + locale + '"]').remove();
	};
	var show = function(localeKey, spec) {
		var template = $('#locale-spec-template').html();
		var localeObj = localeMap[localeKey];
		var oldElement = container.find('[data-locale="' + localeKey + '"]');
		var html = U.template(template, {
			spec : spec,
			locale : localeObj
		});
		var wrap = $('<div data-locale="'+localeKey+'"></div>');
		wrap.html(html);
		wrap.find('[data-op-type="update"]').click(update);
		wrap.find('[data-op-type="remove"]').click(remove);
		if (oldElement[0]) {
			oldElement.after(wrap);
			oldElement.remove();
		} else {
			container.append(wrap);
		}
	};
	var loadLocales = function() {
		return App.api('/app/locales').then(function(result) {
			localeList = result;
			$.each(localeList, function(index, item) {
				localeMap[item['key']] = item;
			});
		});
	};
	var openDialog = function(locale) {
		var spec = specs[locale];
		App.openDialog({
			url : App.router.getRealPath(options.dialogUrl),
			context : {
				spec : spec,
				locale: locale ? locale : 'zh_CN',
			},
			noHeader : true,
			primaryClick : save,
			beforeRender : function(body) {
				var select = body.find('[name="locale"]');
				$.each(localeList, function(index, item) {
					select.append('<option value="' + item.key + '">'
							+ item.name + '</option>');
				});
			}
		});
	};
	this.init = function() {
		return loadLocales().then(function() {
			$('#local-add-btn').click(function() {
				openDialog();
			});
		});
	};
	this.setData = function(data) {
		specs = data;
		for (var locale in specs) {
			show(locale, specs[locale]);
		}
	};
	this.setDataByModel = function(model, keys) {
		var specMap = {};
		$.each(keys, function(i, key) {
        	var prop = model[key];
            for (var locale in prop) {
            	var spec = specMap[locale];
            	if (!spec) {
            		spec = {};
            		specMap[locale] = spec;
            	}
            	spec[key] = prop[locale];
            }
        });
        this.setData(specMap);
	};
	this.getData = function() {
		return specs;
	};
	this.getDataAsModel = function() {
		var model = {};
		for (var locale in specs) {
			var spec = specs[locale];
			for (var key in spec) {
				var prop = model[key];
				if (!prop) {
					prop = {};
					model[key] = prop;
				}
				prop[locale] = spec[key];
			}
		}
		return model;
	};
	this.getLocales = function() {
		return localeList;
	};
};