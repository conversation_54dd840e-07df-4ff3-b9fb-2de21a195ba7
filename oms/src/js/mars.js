if (!JSON || !JSON.stringify) {
  if (!JSON) {
    var JSON = {};
  }
  JSON.stringify = function (obj) {
    var t = typeof obj;
    if (t != 'object' || obj === null) {
      // simple data type
      if (t == 'string') obj = '"' + obj + '"';
      return String(obj);
    } else {
      // recurse array or object
      var n,
        v,
        json = [],
        arr = obj && obj.constructor == Array;
      for (n in obj) {
        v = obj[n];
        t = typeof v;
        if (t == 'string') v = '"' + v + '"';
        else if (t == 'object' && v !== null) v = JSON.stringify(v);
        json.push((arr ? '' : '"' + n + '":') + String(v));
      }
      return (arr ? '[' : '{') + String(json) + (arr ? ']' : '}');
    }
  };
}

var mars = (function ($) {
  var _logEnabled = false;
  var log = function (s) {
    if (!_logEnabled || !console || !console.log) {
      return;
    }
    console.log(s);
  };
  var enableLog = function (enabled) {
    _logEnabled = enabled;
  };

  var isNull = function (s) {
    return typeof s === 'undefined' || s === null;
  };

  var isEmpty = function (s) {
    if (isNull(s)) {
      return true;
    }
    if (typeof s != 'string') {
      return false;
    }
    //		return s.replace(/(^\s*)|(\s*$)/g, "").length == 0;
    return s.length == 0;
  };

  var isNumber = function (s) {
    if (isEmpty(s)) {
      return false;
    }
    return /^[0-9]*$/.test(s);
  };

  var string = (function () {
    var startsWith = function (s, prefix) {
      return s.indexOf(prefix) == 0;
    };

    var endsWith = function (str, suffix) {
      return str.indexOf(suffix, str.length - suffix.length) !== -1;
    };

    var replaceAll = function (s, s1, s2) {
      return s.replace(new RegExp(s1, 'gm'), s2);
    };

    return {
      startsWith: startsWith,
      endsWith: endsWith,
      replaceAll: replaceAll,
    };
  })();

  var date = (function () {
    var pad = function (n) {
      return n < 10 ? '0' + n : n;
    };

    var format = function (date, fmt) {
      if (!date || !fmt) {
        return null;
      }
      var o = {
        'M+': date.getMonth() + 1, // 月份
        'd+': date.getDate(), // 日
        'h+': date.getHours() % 12 == 0 ? 12 : date.getHours() % 12, // 小时
        'H+': date.getHours(), // 小时
        'm+': date.getMinutes(), // 分
        's+': date.getSeconds(), // 秒
        'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
        S: date.getMilliseconds(),
      };
      var week = {
        0: '\u65e5',
        1: '\u4e00',
        2: '\u4e8c',
        3: '\u4e09',
        4: '\u56db',
        5: '\u4e94',
        6: '\u516d',
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          (date.getFullYear() + '').substr(4 - RegExp.$1.length)
        );
      }
      if (/(E+)/.test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          (RegExp.$1.length > 1
            ? RegExp.$1.length > 2
              ? '\u661f\u671f'
              : '\u5468'
            : '') + week[date.getDay() + '']
        );
      }
      for (var k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length == 1
              ? o[k]
              : ('00' + o[k]).substr(('' + o[k]).length)
          );
        }
      }
      return fmt;
    };

    var formatISO8601 = function (d) {
      if (!d) {
        return null;
      }
      return (
        d.getUTCFullYear() +
        '-' +
        pad(d.getUTCMonth() + 1) +
        '-' +
        pad(d.getUTCDate()) +
        'T' +
        pad(d.getUTCHours()) +
        ':' +
        pad(d.getUTCMinutes()) +
        ':' +
        pad(d.getUTCSeconds()) +
        'Z'
      );
    };
    var getInt = function (s) {
      var offset = 0;
      for (var i = 0; i < s.length; i++) {
        if (s.charAt(i) == '0') {
          continue;
        }
        offset = i;
        break;
      }
      if (offset == 0) {
        return parseInt(s);
      }
      return parseInt(s.substr(offset));
    };
    var parse = function (v, timezoneOffset) {
      if (!v) {
        return null;
      }
      // yyyy-MM-ddTHH:mm:ssZ
      // yyyy-MM-ddTHH:mm:ss.SSSZ
      // yyyy-MM-dd HH:mm:ss.SSS
      var index = 0;
      var year = getInt(v.substr(index, 4));
      index += 5;
      var month = getInt(v.substr(index, 2)) - 1;
      index += 3;
      var day = getInt(v.substr(index, 2));
      index += 3;
      var hour = index >= v.length ? 0 : getInt(v.substr(index, 2));
      index += 3;
      var minute = index >= v.length ? 0 : getInt(v.substr(index, 2));
      index += 3;
      var second = index >= v.length ? 0 : getInt(v.substr(index, 2));
      // TODO more format
      if (v.charAt(v.length - 1) == 'Z') {
        var millSecond =
          v.indexOf('.') > 0
            ? getInt(v.substring(v.indexOf('.') + 1, v.length - 1))
            : 0;
        var d = new Date();
        d.setUTCFullYear(year);
        d.setUTCMonth(month);
        d.setUTCDate(day);
        d.setUTCHours(hour);
        d.setUTCMinutes(minute);
        d.setUTCSeconds(second);
        d.setUTCMilliseconds(millSecond);
        return d;
      } else {
        var millSecond =
          v.indexOf('.') > 0 ? getInt(v.substring(v.indexOf('.') + 1)) : 0;
        var date = new Date(year, month, day, hour, minute, second, millSecond);
        if (!isNull(timezoneOffset)) {
          var diff = timezoneOffset - date.getTimezoneOffset();
          date.setTime(date.getTime() - diff * 60 * 1000);
        }
        return date;
      }
    };

    return {
      parse: parse,
      format: format,
      formatISO8601: formatISO8601,
      getDayOfYear: function (date) {
        var start = new Date(date.getFullYear(), 0, 0);
        var diff = date.getTime() - start.getTime();
        var oneDay = 1000 * 60 * 60 * 24;
        return Math.floor(diff / oneDay);
      },
    };
  })();

  var getParameters = function () {
    var loc = window.location;
    var search = loc.search;
    var pairs = {};
    if (search.length <= 1) {
      return pairs;
    }
    var params = search.substring(1).split('&');
    for (var i = 0; i < params.length; i++) {
      if (params[i].length == 0) {
        continue;
      }
      var kv = params[i].split('=');
      pairs[kv[0]] = kv.length > 1 ? kv[1] : null;
    }
    return pairs;
  };

  var getParameter = function (key) {
    var params = getParameters(key);
    return isNull(params[key]) ? null : params[key];
  };

  var getContext = function (key, context) {
    if (isNull(context) || isNull(key)) {
      return null;
    }
    if (typeof key != 'string') {
      key = '' + key;
    }
    if (key.length == 0) {
      return null;
    }
    var keys = key.split('.');
    for (var i = 0; i < keys.length; i++) {
      var k = keys[i];
      if (!$.isPlainObject(context)) {
        return null;
      }
      context = context[k];
      if (isNull(context)) {
        return null;
      }
    }
    return context;
  };

  var template = function (s, context, options) {
    if (isNull(s)) {
      return null;
    }
    if (typeof s !== 'string') {
      s = '' + s;
    }
    context = context || {};
    options = options || {};
    var pattern = options.pattern || {
      start: '{{',
      end: '}}',
    };
    var startChars = pattern.start;
    var endChars = pattern.end;
    var forceNotNull = options.forceNotNull;
    var retainKeyIfNull = options.retainKeyIfNull;
    var buf = '';
    var fromIndex = 0;
    var dollarIndex = 0;
    var len = s.length;
    while ((dollarIndex = s.indexOf(startChars, fromIndex)) >= 0) {
      var endIndex = s.indexOf(endChars, fromIndex);
      if (endIndex <= 0) {
        throw 'Bad template: ' + s;
      }
      var key = s.substring(dollarIndex + startChars.length, endIndex);
      var value = getContext(key, context);
      if (isNull(value)) {
        if (forceNotNull) {
          throw 'No value defined for key: ' + key;
        }
        if (retainKeyIfNull) {
          value = startChars + key + endChars;
        } else {
          value = '';
        }
      }
      if (dollarIndex > fromIndex) {
        buf += s.substring(fromIndex, dollarIndex);
      }
      buf += value;
      fromIndex = endIndex + endChars.length;
    }
    if (fromIndex < len) {
      if (fromIndex == 0) {
        return s;
      }
      buf += s.substring(fromIndex);
    }
    return buf;
  };

  var addURLParam = function (url, key, value) {
    return url + (url.indexOf('?') > 0 ? '&' : '?') + key + '=' + value;
  };

  var wrapScriptURL = function (url, options) {
    if (!options.cache) {
      return addURLParam(url, '__PREVENT_FROM_CACHE__', new Date().getTime());
    }
    if (options.version) {
      return addURLParam(url, '__VERSION__', options.version);
    }
    return url;
  };

  var _resourceCaches = {};

  var loadCssOrScript = function (type, url, options) {
    var success = options.success;
    var done = false;
    if (_resourceCaches[url]) {
      if (success) {
        success();
      }
      return;
    }
    var res = null;
    if (type == 'script') {
      res = document.createElement('script');
    } else if (type == 'css') {
      res = document.createElement('link');
      res.setAttribute('rel', 'stylesheet');
      res.setAttribute('type', 'text/css');
    }
    res.onload = res.onreadystatechange = function () {
      if (
        !done &&
        (!this.readyState ||
          this.readyState == 'loaded' ||
          this.readyState == 'complete')
      ) {
        done = true;
        _resourceCaches[url] = true;
        if (success) {
          success();
        }
      }
    };
    res.onerror = function () {
      options.error && options.error('Failed to load: ' + url);
    };
    var loadURL = wrapScriptURL(url, options);
    if (type == 'script') {
      res.setAttribute('src', loadURL);
    } else {
      res.setAttribute('href', loadURL);
    }
    log('Start to load: ' + url);
    document.getElementsByTagName('head')[0].appendChild(res);
  };
  var loadCss = function (url, options) {
    loadCssOrScript('css', url, options);
  };
  var loadScript = function (url, options) {
    loadCssOrScript('script', url, options);
  };
  var getResourceType = function (url) {
    var searchOffset = url.indexOf('?');
    if (searchOffset > 0) {
      url = url.substring(0, searchOffset);
    }
    var fileNameOffset = url.lastIndexOf('/');
    if (fileNameOffset > 0) {
      url = url.substring(fileNameOffset + 1);
    }
    var typeOffset = url.lastIndexOf('.');
    if (typeOffset <= 0) {
      return null;
    }
    return url.substring(typeOffset + 1).toLowerCase();
  };

  var loadResource = function (res, options) {
    var defer = $.Deferred();
    options = $.extend(
      {
        cache: true,
      },
      options || {}
    );
    var userSuccess = options.success;
    var userError = options.error;
    options.success = function () {
      var ok = true;
      if (userSuccess) {
        ok = userSuccess();
      }
      if (ok === false) {
        defer.reject();
      } else {
        defer.resolve();
      }
    };
    options.error = function (e) {
      userError && userError(e);
      defer.reject();
    };
    var url = null;
    var dataType = null;
    if (typeof res === 'string') {
      url = res;
      dataType = getResourceType(res);
    } else {
      dataType = res.type;
      if (!dataType) {
        dataType = res.dataType;
      }
      url = res.url;
    }
    if (!dataType) {
      dataType = options.dataType;
    }
    if (!dataType || dataType == 'js') {
      dataType = 'script';
    }
    if (dataType == 'script') {
      loadScript(url, options);
    } else if (dataType == 'css') {
      loadCss(url, options);
    } else if (dataType == 'localscript') {
      setTimeout(function () {
        var data = res.data;
        var err = null;
        try {
          if (window.eval) {
            window.eval(data);
          } else {
            window.execScript(data);
          }
        } catch (e) {
          err = 'Failed to execute script: ' + data;
          log(e);
          log(err);
        }
        if (err) {
          options.error(err);
        } else {
          options.success();
        }
      }, 0);
    } else {
      setTimeout(function () {
        options.error('Bad dataType: ' + dataType);
      });
    }
    return defer.promise();
  };

  var loadResources = function (urls, options) {
    options = options || {};
    var defer = $.Deferred();
    var userSuccess = options.success;
    var i = -1;
    var success = function () {
      i++;
      if (i < urls.length) {
        loadResource(urls[i], options);
      } else {
        var ok = true;
        if (userSuccess) {
          ok = userSuccess();
        }
        if (ok === false) {
          defer.reject();
        } else {
          defer.resolve();
        }
      }
    };
    options.success = success;
    setTimeout(success, 0);
    return defer.promise();
  };

  var getScripts = function (data, options) {
    if (isNull(data) || data.length == 0) {
      return {
        html: '',
      };
    }
    var lines = data.split('\n');
    var scripts = [];
    var inScript = false;
    var scriptBuf = null;
    var p1 = new RegExp('<script.*?src=[\'"](.*?)[\'"].*?>.*</script>', 'i');
    var p2 = new RegExp('<script.*?>', 'i');
    var p3 = new RegExp('</script>', 'i');
    var mts = null;
    var html = '';
    for (var i = 0; i < lines.length; i++) {
      var line = lines[i];
      var found = false;
      mts = p1.exec(line);
      if (mts) {
        scripts.push({
          type: 'script',
          url: mts[1],
        });
        found = true;
      }
      if (found) {
        continue;
      }
      if (!inScript) {
        mts = p2.exec(line);
        if (mts) {
          inScript = true;
          scriptBuf = '';
        } else {
          html += '\n';
          html += line;
        }
        continue;
      }
      mts = p3.exec(line);
      if (mts) {
        scripts.push({
          type: 'localscript',
          data: scriptBuf,
        });
        inScript = false;
        scriptBuf = null;
      } else {
        scriptBuf += '\n';
        scriptBuf += line;
      }
    }
    return {
      html: html,
      scripts: scripts,
    };
  };

  var loadPage = function (url, options) {
    if (!options || isNull(options.container)) {
      throw 'Bad options';
    }
    options = $.extend(
      {
        type: 'GET',
        dataType: 'html',
        timeout: 30000,
      },
      options || {}
    );
    var cb = options.success;
    options.success = function (data) {
      var container = $(options.container);
      if (options.beforeShow) {
        container.css({
          display: 'none',
        });
      }
      var scriptsAndHtml = getScripts(data);
      container.html(scriptsAndHtml.html);
      if (options.beforeShow) {
        options.beforeShow();
        container.css({
          display: 'block',
        });
      }
      var scripts = scriptsAndHtml.scripts;
      if (scripts && scripts.length > 0) {
        var loadScriptOptions = options.loadScriptOptions || {};
        loadScriptOptions.success = cb;
        loadScriptOptions.error = options.error;
        loadResources(scripts, loadScriptOptions);
      } else {
        cb && cb();
      }
    };
    return $.ajax(url, options);
  };

  var escapeHtml = function (s) {
    return s
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  };

  return {
    log: log,
    enableLog: enableLog,
    isNull: isNull,
    string: string,
    isEmpty: isEmpty,
    isNumber: isNumber,
    date: date,
    getParameters: getParameters,
    getParameter: getParameter,
    getContext: getContext,
    template: template,
    loadResource: loadResource,
    loadResources: loadResources,
    loadPage: loadPage,
    escapeHtml: escapeHtml,
  };
})(jQuery);

mars.l10n = (function ($, mars) {
  var gData = {};
  var gDefaultKey = '__default__';
  var prefLocales = null;
  var displayLocale = null;
  var displayRootLocale = null;

  var findParentLocale = function (lang) {
    var index = lang.lastIndexOf('_');
    if (index <= 0) {
      return null;
    }
    return lang.substring(0, index);
  };

  var getRootLocale = function (lang) {
    var index = lang.indexOf('_');
    if (index <= 0) {
      return lang;
    }
    return lang.substring(0, index);
  };

  var getBrowserLocale = function () {
    var lang = navigator.language || navigator.browserLanguage;
    lang = mars.string.replaceAll(lang, '-', '_');
    return lang;
  };

  var setLocale = function (locale) {
    displayLocale = locale;
    displayRootLocale = getRootLocale(locale);
  };

  var isParentLocaleOf = function (parent, child) {
    if (mars.isEmpty(parent) || mars.isEmpty(child)) {
      return false;
    }
    var c = child;
    while (true) {
      var p = findParentLocale(c);
      if (!p) {
        return false;
      }
      if (p == parent) {
        return true;
      }
      c = p;
    }
    return false;
  };

  var error = function (e) {
    if (console && console.error) {
      console.error(e);
    }
  };

  var getRawInMap = function (values) {
    if (mars.isNull(values)) {
      return null;
    }
    var value = values[displayLocale];
    if (!mars.isNull(value)) {
      return value;
    }
    for (var locale in values) {
      if (getRootLocale(locale) == displayRootLocale) {
        return values[locale];
      }
    }
    // falls to preferred
    if (prefLocales) {
      for (var i = 0; i < prefLocales.length; i++) {
        var locale = prefLocales[i];
        value = values[locale];
        if (!mars.isNull(value)) {
          return value;
        }
      }
    }
    // falls to default
    for (var key in values) {
      return values[key];
    }
    error('should not happend - localized is empty but not null');
    return null;
  };

  var getRaw = function (key) {
    if (mars.isNull(key) || typeof key !== 'string') {
      return null;
    }
    var values = gData[key];
    if (mars.isNull(values)) {
      error('No localized text of "' + key + '"');
      return null;
    }
    return getRawInMap(values);
  };

  var parseArrayContext = function (kv) {
    if (mars.isNull(kv)) {
      return {};
    }
    if ($.isArray(kv)) {
      var context = {};
      for (var i = 0; i < kv.length; i++) {
        context['' + i] = kv[i];
      }
      kv = context;
    }
    return kv;
  };

  var _translate = function (tag, key, attr, context) {
    var s = getRaw(key);
    if (mars.isNull(s)) {
      return;
    }
    if (typeof s !== 'string') {
      throw 'bad value for key: ' + key;
    }
    s = mars.template(s, context);
    if (!attr) {
      var tagName = tag.prop('tagName').toLowerCase();
      if (tagName == 'option') {
        attr = 'text';
      } else if (tagName == 'input' || tagName == 'textarea') {
        attr = 'value';
      } else if (tagName == 'img') {
        attr = 'title';
      } else {
        attr = 'html';
      }
    }
    if (attr == 'html') {
      tag.html(s);
    } else if (attr == 'text') {
      tag.text(s);
    } else if (attr == 'value') {
      tag.val(s);
    } else {
      tag.attr(attr, s);
    }
  };

  var doTranslate = function (tag) {
    tag = $(tag);
    var key = tag.attr('data-l10n-id');
    var attr = tag.attr('data-l10n-for');
    var args = tag.attr('data-l10n-args');
    if (mars.isNull(key)) {
      throw 'empty key';
    }
    if (!mars.isNull(args) && args.length > 0) {
      args = eval('(' + args + ')');
    } else {
      args = null;
    }
    var keys = key.split('|');
    var attrs = mars.isNull(attr) ? null : attr.split('|');
    var context = parseArrayContext(args);
    for (var i = 0; i < keys.length; i++) {
      _translate(tag, keys[i], mars.isNull(attrs) ? null : attrs[i], context);
    }
  };

  var translate = function (container) {
    $(container)
      .find('[data-l10n-id]')
      .each(function () {
        doTranslate(this);
      });
  };

  // init
  setLocale(getBrowserLocale());

  return {
    findParentLocale: findParentLocale,
    isParentLocaleOf: isParentLocaleOf,
    getRootLocale: getRootLocale,
    getBrowserLocale: getBrowserLocale,
    setPreferredLocales: function (locales) {
      if (!$.isArray(locales)) {
        throw 'locales should be array';
      }
      prefLocales = locales;
    },
    setLocale: setLocale,
    put: function (lang, kv) {
      for (var key in kv) {
        var value = kv[key];
        var values = gData[key];
        if (!values) {
          values = {};
          gData[key] = values;
        }
        values[lang] = value;
      }
    },
    getRaw: getRaw,
    get: function (key, context) {
      var text = getRaw(key);
      if (text === null) {
        return null;
      }
      text = mars.template(text, parseArrayContext(context));
      return text;
    },
    getBy: function (localizedStrings, context) {
      var text = getRawInMap(localizedStrings);
      if (text === null) {
        return null;
      }
      text = mars.template(text, parseArrayContext(context));
      return text;
    },
    doTranslate: doTranslate,
    translate: translate,
  };
})(jQuery, mars);

mars.router = (function ($, mars) {
  var cfg = {};
  var _lastPath = null;

  var addSlash = function (path) {
    if (path.indexOf('/') === 0) {
      return path;
    }
    return '/' + path;
  };

  var getAbsolutePath = function (path, data) {
    var loc = window.location.pathname;
    loc += '?_pfc=' + new Date().getTime() + '#' + addSlash(path);
    if (!mars.isNull(data)) {
      var i = 0;
      for (var key in data) {
        if (i > 0) {
          loc += '&';
        } else {
          loc += '?';
        }
        loc += key + '=' + encodeURIComponent(data[key]);
        i++;
      }
    }
    return loc;
  };

  var getBasePath = function () {
    var basePath = window.location.pathname;
    var lastSlash = basePath.lastIndexOf('/');
    if (lastSlash > 0) {
      basePath = basePath.substring(0, lastSlash);
    }
    return basePath;
  };

  var getCurrentPath = function () {
    var path = window.location.hash;
    var paramIndex = path.indexOf('?');
    if (paramIndex > 0) {
      path = path.substring(1, paramIndex);
    } else {
      path = path.substring(1);
    }
    // lastPath: #/xx/yy
    // currentPath: zz
    // turn to  /xx/zz
    if (_lastPath && !mars.string.startsWith(path, '/')) {
      var prefix = _lastPath.substring(0, _lastPath.lastIndexOf('/') + 1);
      path = prefix + path;
    } else {
      path = addSlash(path);
    }
    return path;
  };

  var getHashSearch = function () {
    var path = window.location.hash;
    var paramIndex = path.indexOf('?');
    if (paramIndex <= 0) {
      return '';
    }
    var search = path.substring(paramIndex + 1);
    return search;
  };

  var getHashParams = function () {
    // var path = window.location.hash;
    // var paramIndex = path.indexOf('?');
    // if (paramIndex <= 0) {
    // 	return pairs;
    // }
    // var search = path.substring(paramIndex + 1);
    var pairs = {};
    var search = getHashSearch();
    if (!search) {
      return pairs;
    }
    var params = search.split('&');
    for (var i = 0; i < params.length; i++) {
      if (params[i].length == 0) {
        continue;
      }
      var kv = params[i].split('=');
      pairs[kv[0]] = mars.isNull(kv[1]) ? null : decodeURIComponent(kv[1]);
    }
    return pairs;
  };

  var getAllParameters = function () {
    if (cfg.forceRedirect) {
      var originalParams = mars.getParameters();
      var hashParams = getHashParams();
      var params = {};
      for (var key in originalParams) {
        params[key] = originalParams[key];
      }
      for (var key in hashParams) {
        params[key] = hashParams[key];
      }
      return params;
    } else {
      return getHashParams();
    }
  };

  var getJSONParameters = function () {
    var params = getAllParameters();
    for (var key in params) {
      if (key.indexOf('.') < 0) {
        continue;
      }
      var v = params[key];
      delete params[key];
      var keys = key.split('.');
      var context = params;
      for (var i = 0; i < keys.length; i++) {
        var k = keys[i];
        if (i == keys.length - 1) {
          context[k] = v;
        } else {
          var p = context[k];
          if (!p) {
            p = {};
            context[k] = p;
          } else if (!$.isPlainObject(p)) {
            throw (
              'not a plain object of ' + k + ' in ' + JSON.stringify(context)
            );
          }
          context = p;
        }
      }
    }
    return params;
  };

  var onHashChanged = function (redirect) {
    var path = getCurrentPath();
    if (redirect) {
      go(path, getHashParams());
      return;
    }
    _lastPath = path;
    if (cfg.handler(path)) {
      if (mars.string.endsWith(path, '/')) {
        path = path + 'index';
      }
      var htmlPage = cfg.viewsPath + path + '.html';
      mars.loadPage(htmlPage, {
        container: cfg.container,
        beforeSend: cfg.pageBeforeLoad,
        beforeShow: cfg.pageBeforeShow,
        success: cfg.pageLoadSuccess,
        error: cfg.pageLoadError,
        loadScriptOptions: cfg.loadScriptOptions,
      });
    }
  };

  var getPathName = function (path) {
    // ?a=b#/path/to/name?xx=yy
    var hashIndex = path.indexOf('#');
    if (hashIndex >= 0) {
      path = path.substring(hashIndex + 1);
    }
    var qIndex = path.indexOf('?');
    if (qIndex >= 0) {
      path = path.substring(0, qIndex);
    }
    path = addSlash(path);
    return path;
  };

  var getRealPath = function (path) {
    return cfg.viewsPath + addSlash(path);
  };

  var go = function (path, data) {
    window.location.href = getAbsolutePath(path, data);
  };

  return {
    init: function (varCfg) {
      cfg = $.extend(
        {
          viewsPath: 'views',
          container: '#page',
          forceRedirect: false,
        },
        varCfg || {}
      );
      $(window).on('hashchange', function () {
        onHashChanged(cfg.forceRedirect);
      });
      onHashChanged();
    },
    getContainer: function () {
      return $(cfg.container)[0];
    },
    go: go,
    getBasePath: getBasePath,
    getCurrentPath: getCurrentPath,
    getAbsolutePath: getAbsolutePath,
    getPathName: getPathName,
    getRealPath: getRealPath,
    getParameters: getAllParameters,
    getJSONParameters: getJSONParameters,
    getParameter: function (key) {
      var params = getAllParameters();
      return mars.isNull(params[key]) ? null : params[key];
    },
    getHashSearch: getHashSearch,
  };
})(jQuery, mars);

mars.generator = (function ($, mars) {
  var generatePage = function (url, menu) {
    App.menu.select(menu);
    var hashSearch = mars.router.getHashSearch();
    var token = $.cookie('sess');
    localStorage.setItem('sessionToken', token);
    var ifrSrc = url;
    var url = hashSearch ? ifrSrc + '?' + hashSearch : ifrSrc;
    console.log(ifrSrc, url);
    var ifr = document.getElementById('iframe');
    ifr.src = url;
  };

  return {
    generatePage: generatePage,
  };
})(jQuery, mars);

(function ($) {
  $.fn.serializeObject = function () {
    var o = {};
    var a = this.serializeArray();
    $.each(a, function () {
      var key = this.name;
      var value = this.value || '';
      var obj = null;
      if (key.indexOf('.') > 0) {
        var keys = key.split('.');
        obj = o[keys[0]];
        if (!obj) {
          obj = {};
          o[keys[0]] = obj;
        }
        key = keys[1];
      } else {
        obj = o;
      }
      if (typeof obj[key] != 'undefined') {
        if (!obj[key].push) {
          obj[key] = [obj[key]];
        }
        obj[key].push(value);
      } else {
        obj[key] = value;
      }
    });
    return o;
  };
  $.fn.renderModel = function (context) {
    if ($.isEmptyObject(context)) {
      return;
    }
    $(this)
      .find('[ng-model]')
      .each(function () {
        var tag = $(this);
        var object = mars.getContext(tag.attr('ng-model'), context);
        if (mars.isNull(object)) {
          return;
        }
        // if ($.isPlainObject(object)) {
        // for ( var attr in object) {
        // tag.attr(attr, object[attr]);
        // }
        // return;
        // }
        var tagName = tag.prop('tagName').toLowerCase();
        if (
          tagName == 'input' ||
          tagName == 'textarea' ||
          tagName == 'select'
        ) {
          if (tag.is(':checkbox')) {
            object = '' + object;
            tag.attr('checked', object === '1' || object === 'true');
          } else {
            tag.val(object);
          }
        } else if (tagName == 'img' || tagName == 'video') {
          tag.attr('src', object);
        } else {
          tag.html(object);
        }
      });
  };

  $.fn.renderTemplate = function (context) {
    var html = mars.template(this.html(), context);
    this.html(html);
  };

  $.fn.getCommentTemplate = function (options) {
    options = $.extend(
      {
        cache: true,
        cacheKey: 'data-commenttemplate',
      },
      options || {}
    );
    var container = $(this);
    var template = null;
    if (options.cache) {
      template = container.attr(options.cacheKey);
      if (template) {
        return template;
      }
    }
    var node = container.contents().filter(function () {
      return this.nodeType === 8;
    });
    if (node[0]) {
      template = node[0].nodeValue;
    } else {
      var children = container.children();
      for (var i = 0; i < children.length; i++) {
        var subNode = children[i];
        template = $(subNode).getCommentTemplate({
          cache: false,
        });
        if (template) {
          break;
        }
      }
    }
    if (!template) {
      return null;
    }
    if (options.cache) {
      container.attr(options.cacheKey, template);
    }
    return template;
  };
})(jQuery);
