/**
 * /** 展示和编辑json string 的插件 使用方法： <div id="panel"></div>
 * 先初始化：App.jsonPanel.init({context:$('#panel')}) init参数为jquery对象
 * 传入json对象，展示：App.jsonPanel.val(jsonObject) 获得组件的json对象：App.jsonPanel.val()
 *
 */
App.jsonPanel = (function ($) {
  var _data = {};
  var _init = function (options) {
    var panel =
      '<div class="panel panel-default">' +
      '<div class="panel-body panel-pro">' +
      "<div>" +
      '<a href="javascript:;" class="btn btn-info btn-add-pro">添加属性</a>' +
      "</div>" +
      "</div>" +
      "</div>";
    options.context.append(panel);
    $(".btn-add-pro").click(function () {
      if ($(".row-undone").length > 0) {
        return;
      }
      var div =
        '<div class="row row-undone" style="padding:10px;">' +
        '<div class="col-md-1">' +
        "名称:" +
        "</div>" +
        '<div class="col-md-4">';
      if (options.module == "discovery") {
        div =
          div +
          '<input type="text" class="form-control" name="proName" value="url" disabled>';
      } else {
        div = div + '<input type="text" class="form-control" name="proName">';
      }
      div =
        div +
        "</div>" +
        '<div class="col-md-1">' +
        "值:" +
        "</div>" +
        '<div class="col-md-4">' +
        '<input type="text" class="form-control" name="proValue">' +
        "</div>" +
        '<div class="col-md-2">' +
        '<a href="javascript:;" class="btn btn-success btn-submit">确定</a>' +
        "</div>" +
        "</div>";
      $(".panel-pro").append(div);
    });
    $(".panel-pro").on("click", ".glyphicon-remove", function () {
      var box = $(this).parent().parent().parent();
      var proName = box.find(".span-name").html();
      delete _data[proName];
      box.remove();
    });
    $(".panel-pro").on("click", ".btn-submit", function () {
      var proName = $('input[name="proName"]').val();
      var proValue = $('input[name="proValue"]').val();
      if (mars.isEmpty(proName)) {
        alert("属性名称不能为空");
        return;
      }
      if (_data.hasOwnProperty(proName)) {
        alert("该属性已经存在");
        return;
      }
      _data[proName] = proValue;
      if (mars.isEmpty(proValue)) {
        proValue = "[空]";
      }
      $(this).closest(".row-undone").detach();
      var newBox =
        '<div style="padding:10px;">' +
        '<button class="btn btn-primary btn-label" type="button">' +
        '名称：<span class="span-name">' +
        proName +
        "</span>" +
        '<span style="margin-left:15px;">值：</span><span class="span-value">' +
        proValue +
        "</span>" +
        '<span class="badge" style="margin-left:5px;">' +
        '<span class="glyphicon glyphicon-remove"></span>' +
        "</span>" +
        "</button>" +
        "</div>";
      $(".panel-pro").append(newBox);
    });
  };
  var _val = function (data) {
    if (arguments.length == 0) {
      return _data;
    }
    _data = data;
    for (var o in data) {
      var newBox =
        '<div style="padding:10px;">' +
        '<button class="btn btn-primary btn-label" type="button">' +
        '名称：<span class="span-name">' +
        o +
        "</span>" +
        '<span style="margin-left:15px;">值：</span><span class="span-value">' +
        data[o] +
        "</span>" +
        '<span class="badge" style="margin-left:5px;">' +
        '<span class="glyphicon glyphicon-remove"></span></span>' +
        "</button>" +
        "</div>";
      $(".panel-pro").append(newBox);
    }
  };
  return {
    init: _init,
    val: _val,
  };
})(jQuery);

/**
 * 文件上传组件 方法init(options) options { url:请求token的url id:按钮的id
 * fileUploaded:function(info) 文件上传完后触发的事件 error:function(errTip) 文件上传失败后触发的事件 }
 *
 * @type {{init}}
 */
App.upload = (function ($) {
  var _init = function (options) {
    if (options.type == "file") {
      App.api("/app/upload_file_token", {
        namespace: options.namespace,
      }).then(function (info) {
        options.info = info;
        _initUploader(options);
      });
    } else if (options.type == "image") {
      App.api("/app/upload_image_token", {
        namespace: options.namespace,
      }).then(function (info) {
        options.info = info;
        _initUploader(options);
      });
    } else if (options.type == "video") {
      App.api("/app/upload_video_token", {
        namespace: options.namespace,
      }).then(function (info) {
        console.log("info");
        options.info = info;
        _initUploader(options);
      });
    } else if (options.type == "firmware") {
      App.api("/app/upload_firmware_token", {
        namespace: options.namespace,
        bucketName: options.bucketName,
      }).then(function (info) {
        console.log("info");
        options.info = info;
        _initUploader(options);
      });
    } else {
      // 其他特殊情况
      App.api(options.url, options.params).then(function (info) {
        options.info = info;
        _initUploader(options);
      });
    }
  };

  var _initUploader = function (options) {
    let Qiniu = new QiniuJsSDK();
    Qiniu.uploader({
      runtimes: "html5,flash", // 上传模式,依次退化
      browse_button: options.id, // 上传选择的点选按钮，**必需**
      domain: "http://qiniu-plupload.qiniudn.com/", // bucket
      // 域名，下载资源时用到，**必需**
      // container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
      uptoken: options.info.token,
      max_file_size: "300mb", // 最大文件体积限制
      flash_swf_url: "vendor/js/plupload/Moxie.swf", // 引入flash,相对路径
      max_retries: 1, // 上传失败最大重试次数
      dragdrop: false, // 开启可拖曳上传
      drop_element: "container", // 拖曳上传区域元素的ID，拖曳文件或文件夹后可触发上传
      chunk_size: "30mb", // 分块上传时，每片的体积
      auto_start: true, // 选择文件后自动上传，若关闭需要自己绑定事件触发上传
      init: {
        FilesAdded: function (up, files) {
          options.fileAdded && options.fileAdded(files);
          console.log("files");
          App.preloader();
        },
        BeforeUpload: function (up, file) {
          // 每个文件上传前,处理相关的事情
          options.beforeUpload && options.beforeUpload(file);
        },
        UploadProgress: function (up, file) {
          // 每个文件上传时,处理相关的事情
        },
        FileUploaded: function (up, file, info) {
          // 每个文件上传成功后,处理相关的事情
          // 其中 info 是文件上传成功后，服务端返回的json，形式如
          App.closePreloader();
          if (typeof info == "string") {
            info = JSON.parse(info);
          }
          options.fileUploaded && options.fileUploaded(info);
        },
        Error: function (up, err, errTip) {
          console.log(err);
          // 上传出错时,处理相关的事情
          alert("Upload failed");
          up.destroy();
          _init(options);
          //options.error && options.error(errTip);
        },
        UploadComplete: function (up) {
          // 队列文件处理完毕后,处理相关的事情
          up.destroy();
          _init(options);
        },
        Key: function (up, file) {
          return options.info.key;
        },
      },
    });
  };

  return {
    init: _init,
  };
})(jQuery);

/**
 * 文件解析组件 方法init(options) options { id:按钮的id
 * fileChange:function(content: string) 文件上传完后触发的事件 error:function(errTip) 文件上传失败后触发的事件 }
 *
 * @type {{init}}
 */
App.resolveFileContent = (function ($) {
  var _init = function (options) {
    var uploadMd5FileBtn = document.querySelector(options.id);

    uploadMd5FileBtn.addEventListener(
      "change",
      () => {
        try {
          if (!uploadMd5FileBtn.files || !uploadMd5FileBtn.files.length) return;
          const reader = new FileReader();
          reader.readAsText(uploadMd5FileBtn.files[0], "utf8"); // input.files[0]为第一个文件
          reader.onload = () => {
            options.fileChange && options.fileChange(reader.result.trim());
          };
        } catch (e) {
          console.log(e);
          options.error && options.error(e);
        }
      },
      false
    );
  };

  return {
    init: _init,
  };
})(jQuery);

App.previewImgUrl = function (img, w, h) {
  if (!img) {
    return null;
  }
  if (img.indexOf("img2.petkit.cn") >= 0) {
    if (w < 60) {
      w = 60;
    }
    if (h < 60) {
      h = 60;
    }
    return img + "@!" + w + "-" + h;
  } else if (
    img.indexOf("video5.petkit.cn") >= 0 ||
    img.indexOf("video5-us.petkit.cn") >= 0
  ) {
    return img + "?vframe/jpg/offset/0/rotate/auto/q/90/w/" + w + "/h/" + h;
  } else {
    return img + "?imageView2/1/w/" + w + "/h/" + h;
  }
};
