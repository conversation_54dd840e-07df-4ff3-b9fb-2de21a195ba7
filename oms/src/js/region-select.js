var Regions = function () {
    var initialized = false;
    var regionMap = {};
    var countries = null;
    var doLoad = function (pcode) {
        return App.api('/app/localities', {
            'parent': pcode
        }).done(function (regions) {
            for (var i = 0; i < regions.length; i++) {
                var region = regions[i];
                regionMap[region.code] = region;
            }
            if (pcode) {
                var parent = regionMap[pcode];
                if (parent) {
                    parent.children = regions;
                }
            } else {
                countries = regions;
            }
            return regions;
        });
    };
    var load = function (pcode) {
        if (!pcode) {
            if (countries) {
                return $.when(countries);
            } else {
                return doLoad(null);
            }
        } else {
            var parent = regionMap[pcode];
            if (!parent) {
                return $.when([]);
            }
            var children = parent.children;
            if (!children) {
                return doLoad(pcode);
            }
            return $.when(children);
        }
    };
    var find = function (code) {
        return regionMap[code];
    };

    var get = function () {
        return regionMap;
    };
    return {
        load: load,
        find: find,
        get: get
    };
}();

function RegionSelect(prefix) {
    var selector = '#' + prefix + '-s';
    var depth = 0;
    var listeners = [];
    var autoLoad = true;

    var checkInit = function () {
        if (!initialized) {
            throw 'Could not do anything else before init completed';
        }
    };

    var getSelect = function (index) {
        var select = $(selector + index);
        return select[0] ? select : null;
    };

    var renderSelect = function (index, list, initValue) {
        if (index > depth) {
            return;
        }
        for (var i = index; i <= depth; i++) {
            var select = getSelect(i);
            select.html('');
            select.append('<option value="">---</option>');
        }
        if (!list) {
            return;
        }
        var select = getSelect(index);
        for (var i = 0; i < list.length; i++) {
            var item = list[i];
            select.append('<option value="' + item.code + '">' + item.name
                + '</option>');
        }
        if (!mars.isNull(initValue)) {
            if (autoLoad) {
                select.val(initValue).trigger("change");
            } else {
                select.val(initValue);
            }
        }
    };

    var onChanged = function (select) {
        select = $(select);
        var code = select.val();
        for (var i = 0; i < listeners.length; i++) {
            listeners[i](select, code);
        }
        var index = parseInt(select.attr('data-region-index'));
        var nextIndex = index + 1;
        var next = getSelect(nextIndex);
        if (!next) {
            return;
        }
        renderSelect(nextIndex, null);
        if (!code) {
            return;
        }
        Regions.load(code).done(function (children) {
            renderSelect(nextIndex, children);
        });
    };


    this.setValue = function (v) {
        checkInit();
        if (!Regions.find(v)) {
            return;
        }
        getSelect(1).val(v).change();
    };

    this.setChinaAllValue = function (v) {
        checkInit();
        var country = v.substr(0, 2);
        var code = v.substr(3, 6);
        var city = country + "_" + parseInt(code / 10000) * 10000;
        var qu = country + "_" + parseInt(code / 100) * 100;
        var i = 0, j = 0;
        renderSelect(1, Regions.get()[country], country);
        Regions.load(country)
            .then(function () {
                    autoLoad = false;
                    renderSelect(2, Regions.get()[country].children, city);
                    return Regions.load(city);
                }
            )
            .then(function () {
                for (; i < Regions.get()[country].children.length; i++) {
                    if (Regions.get()[country].children[i]["code"] === city) {
                        renderSelect(3, Regions.get()[country].children[i].children, qu);
                        break;
                    }
                }
                return Regions.load(qu);
            })
            .then(function () {
                const parent = Regions.get()[country].children[i];
                for (; j < parent.children.length; j++) {
                    if (parent.children[j]["code"] === qu) {
                        renderSelect(4, parent.children[j].children, v);
                        break;
                    }
                }
                autoLoad = true;
            })
    };


    this.getValue = function () {
        checkInit();
        for (var i = depth; i >= 1; i--) {
            var select = getSelect(i);
            var code = select.val();
            if (code) {
                return code;
            }
        }
        return null;
    };

    this.init = function (options) {
        if (!options) {
            options = {};
        }
        for (var i = 1; i <= 10; i++) {
            var select = getSelect(i);
            if (!select) {
                break;
            }
            select.attr('data-region-index', i);
            select.change(function () {
                onChanged(this);
            });
            depth++;
        }
        if (depth == 0) {
            throw 'no select defined in page';
        }
        return Regions.load(null).then(function (regions) {
            renderSelect(1, regions, options.value);
            initialized = true;
        });
    };

    this.addChangeListener = function (listener) {
        checkInit();
        listeners.push(listener);
    };
}