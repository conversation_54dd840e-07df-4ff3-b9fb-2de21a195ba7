(function (window) {
  var menu = null;
  var push = function (v, list) {
    if (!list) {
      list = menu;
    }
    if ($.isArray(v)) {
      for (var i = 0; i < v.length; i++) {
        if (!__CONFIG__.oversea) {
          if (v[i]['href'].includes('singapore')) {
            continue;
          } else {
            for (let j = v[i]['children'].length - 1; j >= 0; j--) {
              if (v[i]['children'][j]['href'].includes('singapore')) {
                v[i]['children'].splice(j, 1);
              }
            }
          }
        }
        list.push(v[i]);
      }
    } else {
      if (!__CONFIG__.oversea) {
        for (let j = v['children'].length - 1; j >= 0; j--) {
          if (v['children'][j]['href'].includes('singapore')) {
            v['children'].splice(j, 1);
          }
        }
      }
      list.push(v);
    }
  };

  menu = [
    {
      href: '/mall',
      title: 'menu.mall',
      cssClass: 'fa-user',
      children: [
        {
          href: '/mall/content',
          title: 'menu.mall.marketingContent',
          permission: '/mall/',
        },
        {
          href: '/mall/standard',
          title: 'menu.mall.standardEntry',
        },
        {
          href: '/mall/product',
          title: 'menu.mall.productManagement',
        },
        {
          href: '/mall/virtualDevice',
          title: 'menu.mall.virtualDevice',
        },
        {
          href: '/mall/purchase',
          title: 'menu.mall.purchaseEntry',
        },
        {
          href: '/community/banners',
          title: 'menu.community.banner',
          name: 'Banner',
          // title: "menu.community.banner",
          permission: '/community/banners',
        },
        {
          href: '/community/appbanner_list',
          title: 'menu.community.appbanner',
          name: 'AppBanner',
          // title: "menu.community.appbanner",
          permission: '/community/appbanners',
        },
        {
          href: '/community/old_appbanner_list',
          title: 'menu.community.appbanner.old',
          name: 'AppBanner(老)',
          // title: "menu.community.appbanner",
          permission: '/community/appbanners',
        },
        {
          href: '/community/splash_list',
          title: 'menu.community.splash',
          name: '启动页',
          // title: "menu.community.splash",
          permission: '/community/splashes',
        },
        {
          href: '/user/group',
          title: 'menu.user.group',
          name: '用户分群',
          // title: "menu.user.group",
          permission: '/user/users',
        },
      ],
    },
    {
      href: '/business',
      title: 'menu.business',
      cssClass: 'fa-user',
      children: [
        {
          href: '/business/cloudServiceSku',
          title: 'menu.business.cloudServiceSku',
        },
        {
          href: '/business/skuCapacity',
          title: 'menu.business.skuCapacity',
        },
        {
          href: '/business/skuBenefit',
          title: 'menu.business.skuBenefit',
        },
        {
          href: '/business/order',
          title: 'menu.business.order',
        },
        {
          href: '/business/subscription',
          title: 'menu.business.subscription',
        },
        // {
        //   href: '/business/cloudService',
        //   title: 'menu.business.cloudService',
        //   permission: '/business/cloudService',
        // },
        {
          href: '/business/cloudServiceV2',
          title: 'menu.business.cloudServiceV2',
          permission: '/business/cloudServiceV2',
        },
        {
          href: '/business/warnEvents',
          title: 'menu.business.warnEvents',
        },
        // {
        //   href: "/business/benefitCompare",
        //   title: "menu.business.benefitCompare",
        // },
        //
        {
          href: '/business/currencyList',
          title: 'menu.business.currencyList',
        },
        {
          href: '/business/operationPackage',
          title: 'menu.business.operationPackage',
        },
        {
          href: '/business/aiVideoFeedback',
          title: 'menu.business.aiVideoFeedback',
        },
        {
          href: '/business/dressUp',
          title: 'menu.business.dressUp',
        },
        {
          href: '/business/virtualCard',
          title: 'menu.business.virtualCard',
          children: [
            {
              href: '/business/virtualCard/list',
              title: 'menu.business.virtualCard',
            },
            {
              href: '/business/virtualCard/codeList',
              title: 'menu.business.virtualCardCode',
            },
          ],
        },
        {
          href: '/business/toolbox',
          title: 'menu.business.toolbox',
        },
        {
          href: '/business/historyRecord',
          title: 'menu.business.history',
          children: [
            {
              href: '/business/historyRecord/refund',
              title: 'menu.business.history.refund',
            },
            {
              href: '/business/historyRecord/changeExpiration',
              title: 'menu.business.history.changeExpiration',
            },
            {
              href: '/business/historyRecord/distribution',
              title: 'menu.business.history.distribution',
            },
          ],
        },
      ],
    },
    {
      href: '/protocols/',
      title: 'menu.protocols',
      cssClass: 'fa-user',
      children: [
        {
          href: '/protocols/private',
          title: 'menu.protocols.private',
        },
      ],
    },
    {
      href: '/activity/',
      title: 'menu.activity',
      cssClass: 'fa-user',
      children: [
        {
          href: '/activity/aiCollaboration',
          title: 'menu.activity.ai.collaboration',
          children: [
            {
              href: '/activity/aiCollaboration/list',
              title: 'menu.activity.ai.collaboration.management',
            },
            {
              href: '/activity/aiCollaboration/reviewList',
              title: 'menu.activity.ai.collaboration.review',
            },
          ],
        },
        {
          href: '/activity/content',
          title: 'menu.activity.content',
          children: [
            {
              href: '/activity/content/upload',
              title: 'menu.activity.content.upload',
            },
            // {
            //   href: '/activity/content/award',
            //   title: 'menu.activity.content.award',
            // },
          ],
        },
      ],
    },
    {
      href: '/user/',
      title: 'menu.user',
      cssClass: 'fa-user',
      children: [
        {
          href: '/user/users',
          title: 'menu.user',
          permission: '/user/users',
        },
        {
          href: '/user/family',
          title: 'menu.user.family',
        },
        {
          href: '/user/activate',
          title: 'menu.user.activate',
          permission: '/user/signupcodehistory',
        },
        {
          href: '/user/cs',
          title: 'menu.user.cs',
          permission: '/user/cs_roster',
        },
        {
          href: '/user/doctors',
          title: 'menu.user.doctor',
          permission: '/user/doctor_roster',
        },
        {
          href: '/user/blacklist',
          title: 'menu.user.blacklist',
          permission: '/user/blacklist_roster',
        },
        {
          href: '/user/whitelist',
          title: 'menu.user.whitelist',
          permission: '/user/whitelist_roster',
        },
      ],
    },
    {
      href: '/pet/',
      title: 'menu.pet',
      cssClass: 'fa-github-square',
      permission: '/pet/',
      children: [
        {
          href: '/pet/pets',
          title: 'menu.pet',
        },
        {
          href: '/pet/breeds',
          title: 'menu.pet.breed',
        },
        {
          href: '/pet/foodbrands',
          title: 'menu.pet.food',
          permission: '/pet/foodbrandsnocache',
        },
        {
          href: '/pet/privatefoods',
          title: 'menu.pet.privatefood',
          permission: '/pet/privatefoods',
        },
        {
          href: '/pet/food_brand',
          title: 'menu.pet.foodBrand',
          permission: '/pet/listBrands',
        },
        {
          href: '/pet/photoIdentify',
          title: 'menu.pet.photoIdentify',
        },
      ],
    },
    {
      href: '/community/',
      title: 'menu.community',
      cssClass: 'fa-home',
      permission: '/community/',
      children: [
        {
          href: '/community/posts',
          title: 'menu.community.post',
          permission: '/community/posts',
        },
        {
          href: '/community/topics',
          title: 'menu.community.topic',
          permission: '/community/topics',
        },
        {
          href: '/community/buylinks',
          title: 'menu.community.buylink',
          permission: '/community/buylinks',
        },
        // {
        //     href: "/community/copy_writing_list",
        //     title: "menu.community.copyWriting",
        //     permission: "/community/appCopyWritings"
        // },
        {
          href: '/community/fwords',
          title: 'menu.community.fword',
          permission: '/community/fwords',
        },
        // {
        // 	'href': '/community/plinks',
        // 	'title': 'menu.community.pushlink',
        // 	'permission': '/community/plinks'
        // },
        // {
        //    'href': '/community/pushes',
        //    'title': 'menu.community.push',
        //    'permission': '/community/pushes'
        // },
        {
          href: '/community/appeals',
          title: 'menu.community.appeal',
          permission: '/community/appeal_list',
        },
        {
          href: '/community/reports',
          title: 'menu.community.report',
          permission: '/community/report_list',
        },
        {
          href: '/community/tags',
          title: 'menu.community.tag',
        },
      ],
    },
    {
      href: '/push/',
      title: 'menu.push',
      cssClass: 'fa-home',
      permission: '/community/',
      children: [
        {
          href: '/community/pushes',
          title: 'menu.push',
          permission: '/community/pushes',
        },
        {
          href: '/push/smart_pushes',
          title: 'menu.push.content',
          permission: '/community/pushes',
        },
        {
          href: '/push/smart_push_feeder',
          title: 'menu.feeder',
          permission: '/community/pushes',
        },
        {
          href: '/push/smart_push_feedermini',
          title: 'menu.feedermini',
          permission: '/community/pushes',
        },
        {
          href: '/push/smart_push_cozy',
          title: 'menu.cozy',
          permission: '/community/pushes',
        },
      ],
    },
    {
      href: '/lock/',
      title: 'menu.lock',
      cssClass: 'fa-laptop',
      permission: '/lock/',
      children: [
        {
          href: '/lock/lockareas',
          title: 'menu.lock.lockareas',
          permission: '/lock/types',
        },
        {
          href: '/lock/material',
          title: 'menu.lock.material',
          permission: '/lock/material',
        },
        {
          href: '/lock/device',
          title: 'menu.lock.device',
          permission: '/lock/device',
        },
      ],
    },
  ];

  push({
    href: '/fit/',
    title: 'menu.fit',
    cssClass: 'fa-mobile-phone',
    permission: '/fit',
    children: [
      {
        href: '/fit/devices',
        title: 'menu.fit.device',
        permission: '/fit/devices',
      },
      {
        href: '/dev/firmwares?_t=fit',
        title: 'menu.fit.firmware',
        permission: '/fit/firmwares',
      },
      {
        href: '/fit/data',
        title: 'menu.fit.data',
        permission: '/fit/data',
      },
      {
        href: '/fit/issues',
        title: 'menu.fit.issue',
        permission: '/fit/issues',
      },
      {
        href: '/fit/warmtips',
        title: 'menu.fit.warmtips',
        permission: '/fit/findwarmtips',
      },
    ],
  });

  push({
    href: '/mate/',
    title: 'menu.mate',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/mate/devices',
        title: 'menu.mate',
        permission: '/mate/devices',
      },
      {
        href: '/mate/logs',
        title: 'menu.mate.log',
        permission: '/mate/logs',
      },
      {
        href: '/mate/firmwares',
        title: 'menu.mate.firmware',
        permission: '/mate/firmwares',
      },
      {
        href: '/mate/firmwarereleases',
        title: 'menu.mate.release',
        permission: '/mate/firmwarereleases',
      },
      {
        href: '/mate/capgroups',
        title: 'menu.mate.sn',
        permission: '/mate/capgroups',
      },
      {
        href: '/mate/modules',
        title: 'menu.mate.module',
        permission: '/mate/modules',
      },
      {
        href: '/mate/logmodules',
        title: 'menu.mate.logmodule',
        permission: '/mate/logmodules',
      },
      {
        href: '/mate/configs',
        title: 'menu.mate.areaconfig',
        permission: '/mate/configs',
      },
      {
        href: '/mate/sipservers',
        title: 'menu.mate.sipserver',
        permission: '/mate/sipservers',
      },
      {
        href: '/mate/relayservers',
        title: 'menu.mate.relayserver',
        permission: '/mate/relayservers',
      },
    ],
  });

  push({
    href: '/go/',
    title: 'menu.go',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/go/devices',
        title: 'menu.go.device',
        permission: '/go/devices',
      },
      {
        href: '/dev/firmwares?_t=go',
        title: 'menu.go.firmware',
        permission: '/go/firmwares',
      },
    ],
  });

  push({
    href: '/feeder/',
    title: 'menu.feeder',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/feeder/devices',
        title: 'menu.feeder.device',
        permission: '/feeder/devices',
      },
      {
        href: '/feeder/sn',
        title: 'menu.feeder.sn',
        permission: '/feeder/sn_list',
      },
      {
        href: '/dev/modules?_t=feeder',
        title: 'menu.feeder.module',
        permission: '/feeder/modules',
      },
      {
        href: '/dev/firmware_version_list?_t=feeder',
        title: 'menu.feeder.firmware',
        permission: '/feeder/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=feeder",
      //     title: "menu.feeder.firmware",
      //     permission: "/feeder/firmwares"
      // },
      {
        href: '/dev/modularized_firmware_details?_t=feeder',
        title: 'menu.feeder.firmwaredetail',
        permission: '/feeder/firmware_details',
      },
      {
        href: '/feeder/logs',
        title: 'menu.feeder.log',
        permission: '/feeder/logs',
      },
    ],
  });

  push({
    href: '/hg/',
    title: 'menu.hg',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/hg/devices',
        title: 'menu.hg.device',
        permission: '/hg/devices',
      },
      {
        href: '/hg/sn',
        title: 'menu.hg.sn',
        permission: '/hg/sn_list',
      },
      {
        href: '/dev/firmware_list?_t=hg',
        title: 'menu.hg.firmware',
        permission: '/hg/firmwares',
      },
      // {
      //     href: "/dev/firmwares?_t=hg",
      //     title: "menu.hg.firmware",
      //     permission: "/hg/firmwares"
      // },
    ],
  });

  push({
    href: '/cozy/',
    title: 'menu.cozy',
    cssClass: 'fa-paw',
    children: [
      {
        href: '/cozy/devices',
        title: 'menu.cozy.device',
        permission: '/cozy/devices',
      },
      {
        href: '/cozy/sn',
        title: 'menu.cozy.sn',
        permission: '/cozy/sn_list',
      },
      {
        href: '/cozy/module',
        title: 'menu.cozy.module',
        permission: '/cozy/module',
      },
      {
        href: '/dev/firmware_version_list?_t=cozy',
        title: 'menu.cozy.firmware',
        permission: '/cozy/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=cozy",
      //     title: "menu.cozy.firmware",
      //     permission: "/cozy/firmwares"
      // },
      {
        href: '/dev/modularized_firmware_details?_t=cozy',
        title: 'menu.cozy.firmwaredetail',
        permission: '/cozy/firmware_details',
      },
      {
        href: '/cozy/log',
        title: 'menu.cozy.log',
        permission: '/cozy/log',
      },
    ],
  });

  push({
    href: '/feedermini/',
    title: 'menu.feedermini',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/feedermini/devices',
        title: 'menu.feedermini.device',
        permission: '/feedermini/devices',
      },
      {
        href: '/feedermini/sn',
        title: 'menu.feedermini.sn',
        permission: '/feedermini/sn_list',
      },
      {
        href: '/feedermini/module',
        title: 'menu.feedermini.module',
        permission: '/feedermini/module',
      },
      {
        href: '/dev/firmware_version_list?_t=feedermini',
        title: 'menu.feedermini.firmware',
        permission: '/feedermini/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=feedermini",
      //     title: "menu.feedermini.firmware",
      //     permission: "/feedermini/firmwares"
      // },
      {
        href: '/singapore/dev/modularized_firmwares?_t=feedermini',
        title: 'menu.feedermini.firmware.singapore',
        permission: '/feedermini/firmwares',
      },

      {
        href: '/dev/modularized_firmware_details?_t=feedermini',
        title: 'menu.feedermini.firmwaredetail',
        permission: '/feedermini/firmware_details',
      },
      {
        href: '/feedermini/log',
        title: 'menu.feedermini.log',
        permission: '/feedermini/log',
      },
    ],
  });
  push({
    href: '/t3/',
    title: 'menu.t3',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/t3/devices',
        title: 'menu.t3.device',
        permission: '/t3/devices',
      },
      {
        href: '/t3/sn',
        title: 'menu.t3.sn',
        permission: '/t3/sn_list',
      },
      {
        href: '/t3/module',
        title: 'menu.t3.module',
        permission: '/t3/module',
      },
      {
        href: '/dev/firmware_version_list?_t=t3',
        title: 'menu.t3.firmware',
        permission: '/t3/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=t3",
      //     title: "menu.t3.firmware",
      //     permission: "/t3/firmwares"
      // },
      {
        href: '/singapore/dev/modularized_firmwares?_t=t3',
        title: 'menu.t3.firmware.singapore',
        permission: '/t3/firmwares',
      },

      {
        href: '/dev/modularized_firmware_details?_t=t3',
        title: 'menu.t3.firmwaredetail',
        permission: '/t3/firmware_details',
      },
      {
        href: '/t3/log',
        title: 'menu.t3.log',
        permission: '/t3/log',
      },
    ],
  });

  push({
    href: '/aqh1/',
    title: 'menu.aqh1',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/aqh1/devices',
        title: 'menu.aqh1.device',
        permission: '/aqh1/devices',
      },
      {
        href: '/aqh1/sn',
        title: 'menu.aqh1.sn',
        permission: '/aqh1/sn_list',
      },
      {
        href: '/aqh1/module',
        title: 'menu.aqh1.module',
        permission: '/aqh1/module',
      },
      {
        href: '/dev/firmware_version_list?_t=aqh1',
        title: 'menu.aqh1.firmware',
        permission: '/aqh1/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=aqh1",
      //     title: "menu.aqh1.firmware",
      //     permission: "/aqh1/firmwares"
      // },
      {
        href: '/singapore/dev/modularized_firmwares?_t=aqh1',
        title: 'menu.aqh1.firmware.singapore',
        permission: '/aqh1/firmwares',
      },

      {
        href: '/dev/modularized_firmware_details?_t=aqh1',
        title: 'menu.aqh1.firmwaredetail',
        permission: '/aqh1/firmware_details',
      },
    ],
  });

  push({
    href: '/k2/',
    title: 'menu.k2',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/k2/devices',
        title: 'menu.k2.device',
        permission: '/k2/devices',
      },
      {
        href: '/k2/sn',
        title: 'menu.k2.sn',
        permission: '/k2/sn_list',
      },
      {
        href: '/k2/module',
        title: 'menu.k2.module',
        permission: '/k2/module',
      },
      {
        href: '/dev/firmware_version_list?_t=k2',
        title: 'menu.k2.firmware',
        permission: '/k2/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=k2",
      //     title: "menu.k2.firmware",
      //     permission: "/k2/firmwares"
      // },
      {
        href: '/singapore/dev/modularized_firmwares?_t=k2',
        title: 'menu.k2.firmware.singapore',
        permission: '/k2/firmwares',
      },

      {
        href: '/dev/modularized_firmware_details?_t=k2',
        title: 'menu.k2.firmwaredetail',
        permission: '/k2/firmware_details',
      },
      {
        href: '/k2/log',
        title: 'menu.k2.log',
        permission: '/k2/log',
      },
    ],
  });
  push({
    href: '/k3/',
    title: 'menu.k3',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/k3/devices',
        title: 'menu.k3.device',
        permission: '/k3/devices',
      },
      {
        href: '/k3/sn',
        title: 'menu.k3.sn',
        permission: '/k3/sn_list',
      },
      {
        href: '/dev/firmware_list?_t=k3',
        title: 'menu.k3.firmware',
        permission: '/k3/firmwares',
      },
      // {
      //     href: "/dev/firmwares?_t=k3",
      //     title: "menu.k3.firmware",
      //     permission: "/k3/firmwares"
      // },
    ],
  });
  push({
    href: '/aq/',
    title: 'menu.aq',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/aq/devices',
        title: 'menu.aq.device',
        permission: '/aq/devices',
      },
      {
        href: '/aq/sn',
        title: 'menu.aq.sn',
        permission: '/aq/sn_list',
      },
      {
        href: '/dev/firmware_list?_t=aq',
        title: 'menu.aq.firmware',
        permission: '/aq/firmwares',
      },
      // {
      //     href: "/dev/firmwares?_t=aq",
      //     title: "menu.aq.firmware",
      //     permission: "/aq/firmwares"
      // },
    ],
  });
  push({
    href: '/aqr/',
    title: 'menu.aqr',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/aqr/devices',
        title: 'menu.aqr.device',
        permission: '/aqr/devices',
      },
      {
        href: '/aqr/sn',
        title: 'menu.aqr.sn',
        permission: '/aqr/sn_list',
      },
      {
        href: '/dev/firmware_list?_t=aqr',
        title: 'menu.aqr.firmware',
        permission: '/aqr/firmwares',
      },
      // {
      //     href: "/dev/firmwares?_t=aqr",
      //     title: "menu.aqr.firmware",
      //     permission: "/aqr/firmwares"
      // },
    ],
  });

  // push({
  //     href: "/h3/",
  //     title: "menu.h3",
  //     cssClass: "fa-mobile-phone",
  //     children: [
  //         {
  //             href: "/h3/devices",
  //             title: "menu.h3.device",
  //             permission: "/h3/devices"
  //         },
  //         {
  //             href: "/h3/sn",
  //             title: "menu.h3.sn",
  //             permission: "/h3/sn_list"
  //         },
  //         {
  //             href: "/h3/module",
  //             title: "menu.h3.module",
  //             permission: "/h3/module"
  //         },
  //         {
  //             href: "/dev/firmware_version_list?_t=h3",
  //             title: "menu.h3.firmware",
  //             permission: "/h3/firmwares"
  //         },
  //         {
  //             href: "/dev/modularized_firmware_details?_t=h3",
  //             title: "menu.h3.firmwaredetail",
  //             permission: "/h3/firmware_details"
  //         }
  //     ]
  // });

  push({
    href: '/t4/',
    title: 'menu.t4',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/t4/devices',
        title: 'menu.t4.device',
        permission: '/t4/devices',
      },
      {
        href: '/t4/sn',
        title: 'menu.t4.sn',
        permission: '/t4/sn_list',
      },
      {
        href: '/t4/module',
        title: 'menu.t4.module',
        permission: '/t4/module',
      },
      {
        href: '/dev/firmware_version_list?_t=t4',
        title: 'menu.t4.firmware',
        permission: '/t4/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=t4",
      //     title: "menu.t4.firmware",
      //     permission: "/t4/firmwares"
      // },
      // {
      //     href: "/singapore/dev/modularized_firmwares?_t=t4",
      //     title: "menu.t4.firmware.singapore",
      //     permission: "/t4/firmwares"
      // },
      {
        href: '/dev/modularized_firmware_details?_t=t4',
        title: 'menu.t4.firmwaredetail',
        permission: '/t4/firmware_details',
      },
    ],
  });

  push({
    href: '/t5/',
    title: 'menu.t5',
    name: 'T5',
    // title: "menu.t5",
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/t5/devices',
        title: 'menu.t5.device',
        permission: '/t5/devices',
      },
      {
        href: '/t5/sn',
        title: 'menu.t5.sn',
        permission: '/t5/sn_list',
      },
      {
        href: '/t5/module',
        title: 'menu.t5.module',
        permission: '/t5/module',
      },
      {
        href: '/dev/firmware_version_list?_t=t5',
        title: 'menu.t5.firmware',
        permission: '/t5/firmwares',
      },
      // {
      //   href: '/singapore/dev/modularized_firmwares?_t=t5',
      //   title: 'menu.t5.firmware.singapore',
      //   permission: '/t5/firmwares',
      // },
      {
        href: '/dev/modularized_firmware_details?_t=t5',
        title: 'menu.t5.firmwaredetail',
        permission: '/t5/firmware_details',
      },
    ],
  });

  push({
    href: '/t6/',
    title: 'menu.t6',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/t6/devices',
        title: 'menu.t6.device',
        permission: '/t6/devices',
      },
      {
        href: '/t6/sn',
        title: 'menu.t6.sn',
        permission: '/t6/sn_list',
      },
      {
        href: '/t6/module',
        title: 'menu.t6.module',
        permission: '/t6/module',
      },
      {
        href: '/dev/firmware_version_list?_t=t6',
        title: 'menu.t6.firmware',
        permission: '/t6/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=t6",
      //     title: "menu.t6.firmware",
      //     permission: "/t6/firmwares"
      // },
      // {
      //     href: "/singapore/dev/modularized_firmwares?_t=t6",
      //     title: "menu.t6.firmware.singapore",
      //     permission: "/t6/firmwares"
      // },
      {
        href: '/dev/modularized_firmware_details?_t=t6',
        title: 'menu.t6.firmwaredetail',
        permission: '/t6/firmware_details',
      },
      {
        href: '/t6/rubbish_box_nfc_list',
        title: 'menu.t6.rubbishBoxNFCList',
        permission: '/t6/rubbish_box_nfc_list',
      },
      {
        href: '/t6/nfc_account_list',
        title: 'menu.t6.NFCAccountList',
        permission: '/t6/nfc_account_list',
      },
      {
        href: '/t6/rubbish_box_list',
        title: 'menu.t6.rubbishBoxList',
        permission: '/t6/rubbish_box_list',
      },
    ],
  });

  push({
    href: '/t7/',
    title: 'menu.t7',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/t7/devices',
        title: 'menu.t7.device',
        permission: '/t7/devices',
      },
      {
        href: '/t7/sn',
        title: 'menu.t7.sn',
        permission: '/t7/sn_list',
      },
      {
        href: '/t7/module',
        title: 'menu.t7.module',
        permission: '/t7/module',
      },
      {
        href: '/dev/firmware_version_list?_t=t7',
        title: 'menu.t7.firmware',
        permission: '/t7/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=t6",
      //     title: "menu.t6.firmware",
      //     permission: "/t6/firmwares"
      // },
      // {
      //     href: "/singapore/dev/modularized_firmwares?_t=t6",
      //     title: "menu.t6.firmware.singapore",
      //     permission: "/t6/firmwares"
      // },
      {
        href: '/dev/modularized_firmware_details?_t=t7',
        title: 'menu.t7.firmwaredetail',
        permission: '/t7/firmware_details',
      },
      {
        href: '/t7/crystal_tray_cat_litter',
        title: 'menu.t7.crystalTrayCatLitter',
        permission: '/t7/crystal_tray_cat_litter',
      },
      {
        href: '/t7/crystal_tray_cat_litter_management_list',
        title: 'menu.t7.crystalTrayCatLitterManagementList',
        permission: '/t7/crystal_tray_cat_litter_management_list',
      },
      {
        href: '/t7/crystal_tray_cat_litter_nfc_account_list',
        title: 'menu.t7.crystalTrayCatLitterNFCAccountList',
        permission: '/t7/crystal_tray_cat_litter_nfc_account_list',
      },
    ],
  });

  push({
    href: '/d3/',
    title: 'menu.d3',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/d3/devices',
        title: 'menu.d3.device',
        permission: '/d3/devices',
      },
      {
        href: '/d3/sn',
        title: 'menu.d3.sn',
        permission: '/d3/sn_list',
      },
      {
        href: '/d3/module',
        title: 'menu.d3.module',
        permission: '/d3/module',
      },

      {
        href: '/dev/firmware_version_list?_t=d3',
        title: 'menu.d3.firmware',
        permission: '/d3/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=d3",
      //     title: "menu.d3.firmware",
      //     permission: "/d3/firmwares"
      // },
      {
        href: '/dev/modularized_firmware_details?_t=d3',
        title: 'menu.d3.firmwaredetail',
        permission: '/d3/firmware_details',
      },
      {
        href: '/d3/log',
        title: 'menu.d3.log',
        permission: '/d3/log',
      },
      {
        href: '/d3/sound_firmwares',
        title: 'menu.d3.sound_firmwares',
        permission: '/d3/sound_firmwares',
      },
      {
        href: '/singapore/d3/sound_firmwares',
        title: 'menu.d3.sound_firmwares.singapore',
        permission: '/d3/sound_firmwares',
      },
    ],
  });

  push({
    href: '/d4/',
    title: 'menu.d4',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/d4/devices',
        title: 'menu.d4.device',
        permission: '/d4/devices',
      },
      {
        href: '/d4/sn',
        title: 'menu.d4.sn',
        permission: '/d4/sn_list',
      },
      {
        href: '/d4/module',
        title: 'menu.d4.module',
        permission: '/d4/module',
      },

      {
        href: '/dev/firmware_version_list?_t=d4',
        title: 'menu.d4.firmware',
        permission: '/d4/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=d4",
      //     title: "menu.d4.firmware",
      //     permission: "/d4/firmwares"
      // },

      {
        href: '/dev/modularized_firmware_details?_t=d4',
        title: 'menu.d4.firmwaredetail',
        permission: '/d4/firmware_details',
      },
      {
        href: '/d4/log',
        title: 'menu.d4.log',
        permission: '/d4/log',
      },
    ],
  });

  push({
    href: '/d4s/',
    title: 'menu.d4s',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/d4s/devices',
        title: 'menu.d4s.device',
        permission: '/d4s/devices',
      },
      {
        href: '/d4s/sn',
        title: 'menu.d4s.sn',
        permission: '/d4s/sn_list',
      },
      {
        href: '/d4s/module',
        title: 'menu.d4s.module',
        permission: '/d4s/module',
      },

      {
        href: '/dev/firmware_version_list?_t=d4s',
        title: 'menu.d4s.firmware',
        permission: '/d4s/firmwares',
      },
      // {
      //     href: "/dev/modularized_firmwares?_t=d4s",
      //     title: "menu.d4s.firmware",
      //     permission: "/d4s/firmwares"
      // },

      // {
      //   href: "/singapore/dev/modularized_firmwares?_t=d4s",
      //   title: "menu.d4s.firmware.singapore",
      //   permission: "/d4s/firmwares",
      // },

      {
        href: '/dev/modularized_firmware_details?_t=d4s',
        title: 'menu.d4s.firmwaredetail',
        permission: '/d4s/firmware_details',
      },
    ],
  });

  push({
    href: '/d4h/',
    title: 'menu.d4h',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/d4h/devices',
        title: 'menu.d4h.device',
        permission: '/d4h/devices',
      },
      {
        href: '/d4h/sn',
        title: 'menu.d4h.sn',
        permission: '/d4h/sn_list',
      },
      {
        href: '/d4h/module',
        title: 'menu.d4h.module',
        permission: '/d4h/module',
      },
      {
        href: '/dev/firmware_version_list?_t=d4h&hardware=1',
        title: 'menu.d4h.firmware',
        permission: '/d4h/firmwares',
      },
      {
        href: '/dev/tc_acoustic_firmware_version_list?_t=d4h&hardware=1',
        title: 'menu.d4h.tencent.firmware',
        permission: '/d4h/tencent/firmwares',
      },
      {
        href: '/dev/modularized_firmware_details?_t=d4h&hardware=1',
        title: 'menu.d4h.firmwaredetail',
        permission: '/d4h/firmware_details',
      },
      {
        href: '/new-dev/logs-with-file?deviceType=d4h',
        title: 'menu.d4h.logs',
        permission: '/d4h/logs',
      },
    ],
  });

  push({
    href: '/d4h-2/',
    title: 'menu.d4h.2',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/d4h-2/module',
        title: 'menu.d4h.2.module',
        permission: '/d4h-2/module',
      },
      {
        href: '/dev/firmware_version_list?_t=d4h&hardware=2',
        title: 'menu.d4h.2.firmware',
        permission: '/d4h-2/firmwares',
      },
      {
        href: '/dev/tc_acoustic_firmware_version_list?_t=d4h&hardware=2',
        title: 'menu.d4h.2.tencent.firmware',
        permission: '/d4h-2/tencent/firmwares',
      },
      {
        href: '/dev/modularized_firmware_details?_t=d4h&hardware=2',
        title: 'menu.d4h.2.firmwaredetail',
        permission: '/d4h-2/firmware_details',
      },
    ],
  });

  push({
    href: '/d4sh/',
    title: 'menu.d4sh',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/d4sh/devices',
        title: 'menu.d4sh.device',
        permission: '/d4sh/devices',
      },
      {
        href: '/d4sh/sn',
        title: 'menu.d4sh.sn',
        permission: '/d4sh/sn_list',
      },
      {
        href: '/d4sh/module',
        title: 'menu.d4sh.module',
        permission: '/d4sh/module',
      },
      {
        href: '/dev/firmware_version_list?_t=d4sh&hardware=1',
        title: 'menu.d4sh.firmware',
        permission: '/d4sh/firmwares',
      },
      {
        href: '/dev/tc_acoustic_firmware_version_list?_t=d4sh&hardware=1',
        title: 'menu.d4sh.tencent.firmware',
        permission: '/d4sh/tencent/firmwares',
      },
      {
        href: '/dev/modularized_firmware_details?_t=d4sh&hardware=1',
        title: 'menu.d4sh.firmwaredetail',
        permission: '/d4sh/firmware_details',
      },
      {
        href: '/new-dev/logs-with-file?deviceType=d4sh',
        title: 'menu.d4sh.logs',
        permission: '/d4sh/logs',
      },
    ],
  });

  push({
    href: '/d4sh-2/',
    title: 'menu.d4sh.2',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/d4sh-2/module',
        title: 'menu.d4sh.2.module',
        permission: '/d4sh-2/module',
      },
      {
        href: '/dev/firmware_version_list?_t=d4sh&hardware=2',
        title: 'menu.d4sh.2.firmware',
        permission: '/d4sh-2/firmwares',
      },
      {
        href: '/dev/tc_acoustic_firmware_version_list?_t=d4sh&hardware=2',
        title: 'menu.d4sh.2.tencent.firmware',
        permission: '/d4sh-2/tencent/firmwares',
      },
      {
        href: '/dev/modularized_firmware_details?_t=d4sh&hardware=2',
        title: 'menu.d4sh.2.firmwaredetail',
        permission: '/d4sh-2/firmware_details',
      },
    ],
  });

  push({
    href: '/p3/',
    title: 'menu.p3',
    cssClass: 'fa-mobile-phone',
    permission: '/p3',
    children: [
      {
        href: '/p3/devices',
        title: 'menu.p3.device',
        permission: '/p3/devices',
      },
      {
        href: '/p3/sn',
        title: 'menu.p3.sn',
        permission: '/p3/sn_list',
      },
      {
        href: '/dev/firmware_list?_t=p3',
        title: 'menu.p3.firmware',
        permission: '/p3/firmwares',
      },
      // {
      //     href: "/dev/firmwares?_t=p3",
      //     title: "menu.p3.firmware",
      //     permission: "/p3/firmwares"
      // },
      {
        href: '/p3/data',
        title: 'menu.p3.data',
        permission: '/p3/data',
      },
      {
        href: '/p3/issues',
        title: 'menu.p3.issue',
        permission: '/p3/issues',
      },
      {
        href: '/p3/warmtips',
        title: 'menu.p3.warmtips',
        permission: '/p3/findwarmtips',
      },
    ],
  });

  push({
    href: '/h2/',
    title: 'menu.h2',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/h2/devices',
        title: 'menu.h2.device',
        permission: '/h2/devices',
      },
    ],
  });
  push({
    href: '/w5/',
    title: 'menu.w5',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/w5/devices',
        title: 'menu.w5.device',
        permission: '/w5/devices',
      },
      {
        href: '/w5/sn',
        title: 'menu.w5.sn',
        permission: '/w5/sn_list',
      },
      {
        href: '/dev/firmware_list?_t=w5',
        title: 'menu.w5.firmware',
        permission: '/w5/firmwares',
      },
      // {
      //     href: "/dev/firmwares?_t=w5",
      //     title: "menu.w5.firmware",
      //     permission: "/w5/firmwares"
      // },
    ],
  });
  push({
    href: '/ctw3/',
    title: 'menu.ctw3',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/ctw3/devices',
        title: 'menu.ctw3.device',
      },
      {
        href: '/ctw3/sn',
        title: 'menu.ctw3.sn',
      },
      {
        href: '/dev/firmware_list?_t=ctw3',
        title: 'menu.ctw3.firmware',
      },
    ],
  });
  push({
    href: '/r2/',
    title: 'menu.r2',
    cssClass: 'fa-mobile-phone',
    children: [
      {
        href: '/r2/devices',
        title: 'menu.r2.device',
        permission: '/r2/devices',
      },
      {
        href: '/r2/sn',
        title: 'menu.r2.sn',
        permission: '/r2/sn_list',
      },
      {
        href: '/dev/firmware_list?_t=r2',
        title: 'menu.r2.firmware',
        permission: '/r2/firmwares',
      },
      // {
      //     href: "/dev/firmwares?_t=r2",
      //     title: "menu.r2.firmware",
      //     permission: "/r2/firmwares"
      // },
    ],
  });
  push([
    {
      href: '/app/',
      title: 'menu.app',
      cssClass: 'fa-laptop',
      permission: '/app/',
      children: [
        {
          href: '/app/logs',
          title: 'menu.app.log',
        },
        {
          href: '/app/apilogs',
          title: 'menu.app.apilog',
        },
        {
          href: '/app/apiusers',
          title: 'menu.app.apiuser',
        },
        {
          href: '/app/links',
          title: 'menu.app.link',
        },
        {
          href: '/app/apps',
          title: 'menu.app.release',
        },
        {
          href: '/app/alert',
          title: 'menu.app.alert',
        },
        {
          href: '/app/modules',
          title: 'menu.app.module',
        },
        {
          href: '/business/appVersionVerify',
          title: 'menu.business.appVersionVerify',
        },
      ],
    },
    {
      href: '/schedule/',
      title: 'menu.schedule',
      cssClass: 'fa-github-square',
      permission: '/schedule/',
      children: [
        {
          href: '/schedule/types',
          title: 'menu.schedule',
        },
      ],
    },
    {
      href: '/coin/',
      title: 'menu.coin',
      cssClass: 'fa-user-md',
      children: [
        {
          href: '/coin/gifts',
          title: 'menu.coin.gift',
        },
        {
          href: '/coin/coupons',
          title: 'menu.coin.coupon',
        },
      ],
    },
    {
      href: '/im/',
      title: 'menu.im',
      cssClass: 'fa-user-md',
      children: [
        {
          href: '/im/emotiongroups',
          title: 'menu.im.emotion',
        },
        {
          href: '/im/nodes',
          title: 'menu.im.node',
        },
        {
          href: '/im/csconfigs',
          title: 'menu.im.csconfig',
        },
      ],
    },
    {
      href: '/system/',
      title: 'menu.system',
      cssClass: 'fa-cogs',
      permission: '/system/|/apikey/',
      children: [
        {
          href: '/system/configs',
          title: 'menu.system.config',
          permission: '/system/configs',
        },
        {
          href: '/apikey/apikeys',
          title: 'menu.system.apikey',
          permission: '/apikey/apikeys',
        },
        {
          href: '/system/localebundles',
          title: 'menu.system.localebundle',
          permission: '/system/localebundles',
        },
        {
          href: '/system/app_device_list',
          title: 'menu.system.appDeviceInfo',
          permission: '/system/app_device_list',
        },
        {
          href: '/system/tip_info_list',
          title: 'menu.system.tipInfo',
          permission: '/system/tip_info_list',
        },
        {
          href: '/system/license_batch',
          title: 'menu.system.licenseBatch',
          permission: '/system/license_batch',
        },
      ],
    },
    {
      href: '/singapore/system/',
      title: 'menu.singapore.system',
      cssClass: 'fa-cogs',
      permission: '/system/|/apikey/',
      children: [
        {
          href: '/singapore/system/configs',
          title: 'menu.system.config',
          permission: '/system/configs',
        },
        {
          href: '/singapore/apikey/apikeys',
          title: 'menu.system.apikey',
          permission: '/apikey/apikeys',
        },
        {
          href: '/singapore/system/localebundles',
          title: 'menu.system.localebundle',
          permission: '/system/localebundles',
        },
        {
          href: '/system/app_device_list',
          title: 'menu.system.appDeviceInfo',
          permission: '/system/app_device_list',
        },
      ],
    },
    {
      href: '#',
      title: 'menu.admin',
      cssClass: 'fa-user',
      permission: '/admin/',
      children: [
        {
          href: '/admin/groups',
          title: 'menu.admin.group',
          permission: '/admin/groups',
        },
        {
          href: '/admin/admins',
          title: 'menu.admin',
          permission: '/admin/admins',
        },
      ],
    },
  ]);

  window.__MENU__ = menu;
  localStorage.menuTree = JSON.stringify(menu);
})(window);
