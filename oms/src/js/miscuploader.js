//TODO refractor
var MiscUploader = (function() {
	var NETWORK_ERROR = '网络错误';
	var testXHR = XMLHttpRequest ? new XMLHttpRequest() : null;
	var html5XHRSupport = (testXHR && testXHR.upload) ? true : false;
	var emptyFunc = function(){};
	
	function html5Upload(files, options){
		var fd = new FormData();
		var fieldName = options.fieldName ? options.fieldName : 'file';
		for (var i=0; i<files.length; i++) {
			fd.append(fieldName, files[i]);
		}
		var data = options.data;
		if (data) {
			for (var key in data) {
				fd.append(key, data[key]);
			}
		}
		var xhr = new XMLHttpRequest();
		var onError = options.error || emptyFunc;
		var onSuccess = options.success || emptyFunc;
		
		xhr.addEventListener("error", onError, false);
		
		if (options.success)
			xhr.onload=function(evt) {
				var s = evt.target.responseText;
				var res = null;
				try {
					res = eval('(' + s + ')');
				} catch(e) {
					onError(NETWORK_ERROR);
					return;
				}
				onSuccess(res);
			};
			
		if (options.progress){
			xhr.onprogress=function(evt){
				if (!evt.lengthComputable) {
					return;
				}
				options.progress(evt.loaded  / evt.total);
			};
		}
		if (options.before)
			xhr.onloadstart=options.before;
		if (options.done)
			xhr.onloadend= options.done;
		if (options.abort)
			xhr.onabort= options.abort;
		if (options.timeout) {
			xhr.timeout=options.timeout;
			xhr.ontimeout = onError;
		}
		
		xhr.open("POST", options.url, true);
		if (options.headers) {
			for (var key in options.headers) {
				xhr.setRequestHeader(key, options.headers[key]);
			}
		}
		xhr.send(fd);
		return xhr;
	}
	
	return {
		upload: function(options) {
			if(!html5XHRSupport) {
				alert('当前浏览器不支持高级上传模式，请使用chrome/firefox/IE10+');
				return;
			}
			var fileElement = $(options.fileElement)[0];
			if (!options.fieldName) {
				options.fieldName = fileElement.getAttribute('name');
			}
			return html5Upload(fileElement.files, options);
		}
	};
})();


App.uploadFile = function(options) {
	options = options || {};
	var path = options.url;
	if (path.charAt(0) == '/') {
		path = path.substring(1);
	}
	var server = options.server || 'api';
	var cb = options.success;
	var error = options.error;
	var data = options.data || {};
	options = $.extend({
		dataType: 'json',
        before: function(){
        	App.preloader();
        },
        done: function () {
        	App.closePreloader();
        }
	}, options);
	data = $.extend(data, App.specialApiParams());
	var url = null;
	if (path.indexOf('http://') === 0 || path.indexOf('https://') === 0) {
		url = path;
	} else {
		if (server == 'api') {
			url = App.apiPath(path);
		} else {
			url = App.apiPath('proxy/' + server);
			data['__uri__'] = path;
		}
	}
	options.url = url;
	options.data = data;
	options.error = function(s) {
		if (U.isNull(s) || typeof s!=='string') {
    		s = App.text('network.error');
    	}
    	alert(s);
    	error && error();
	};
	options.success = function (data){
		if (U.isNull(data) || data.error){
			var msg = (data && data.error) ? data.error.msg : null;
			alert(msg || App.text('network.error'));
			return;
		}
		var result = data.result;
		cb && cb(result);
	};
	MiscUploader.upload(options);
};