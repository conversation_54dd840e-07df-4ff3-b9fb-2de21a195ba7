var postAsText = function(post) {
	var text = post.detail ? post.detail : '';
	if (post.link) {
		text = post.link + "<br/>" + post.summary;
	}
	var imgs = null;
	if (post.video) {
		imgs = [ post.video.thumbnailUrl ];
	} else if (post.images) {
		imgs = [];
		$.each(post.images, function(index, item){
			imgs.push(item.url);
		});
	}
	if (imgs && imgs.length > 0) {
		text += '<br/>';
		for (var i = 0; i < imgs.length; i++) {
			var thumbnail = App.previewImgUrl(imgs[i], 120, 120);
			text += '<a href="' + imgs[i] + '" target="_blank"><img src="'
					+ thumbnail + '" class="postimg"/></a>';
		}
	}
	if (text.startsWith('<br/>')) {
		text = text.substring(5);
	}
	return text;
};