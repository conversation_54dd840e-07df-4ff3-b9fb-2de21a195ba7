var Region = (function() {
	var REGION_KEYS = [ 'country', 'province', 'city', 'district' ];
	var REGIONS = [];
	var REGION_MAP = {};
	var loopRegion = function(items, countryCode, level, parent) {
		if (!items) {
			return;
		}
		for (var i = 0; i < items.length; i++) {
			var item = items[i];
			item.level = level;
			item.parent = parent;
			var code = countryCode === null ? item.code : countryCode + '-'
					+ item.code;
			REGION_MAP[code] = item;
			loopRegion(item.children, countryCode === null ? code
					: countryCode, level + 1, item);
		}
	}
	var init = function(regions) {
		REGIONS = regions;
		loopRegion(regions, null, 1, null);
	};
	var find = function(o) {
		var key = null;
		if ($.type(o) == 'string') {
			key = o;
		} else if ($.isPlainObject(o)) {
			var fKey = REGION_KEYS[0];
			for (var i = REGION_KEYS.length - 1; i > 0; i--) {
				var addrKey = REGION_KEYS[i];
				if (o[addrKey]) {
					key = o[fKey] + '-' + o[addrKey];
					break;
				}
			}
			if (!key) {
				key = o[fKey];
			}
		}
		if (!key) {
			throw 'Bad region key';
		}
		return REGION_MAP[key];
	};
	var findName = function(o) {
		var region = find(o);
		if (!region) {
			return [];
		}
		var names = [region.name];
		while (region.parent) {
			names.unshift(region.parent.name);
			region = region.parent;
		}
		return names;
	};
	var load = function() {
		if (REGIONS.length > 0) {
			var defer = $.Deferred();
			setTimeout(function() {
				defer.resolve();
			});
			return defer.promise();
		} else {
			return App.api('/app/countries').then(
					function(regions) {
						Region.init(regions);
					});
		}
	};

	return {
		init : init,
		load: load,
		keys: REGION_KEYS,
		getAll: function() {
			return REGIONS;
		},
		findName: findName,
		find: find
	};

})();

function RegionUI(prefix) {

	var selector = '#' + prefix + '-';

	function renderSelect(container, list) {
		var select = $(selector + container);
		if (!select[0]) {
			return;
		}
		select.html('');
		select.append('<option value="">---</option>');
		if (!list) {
			return;
		}
		for (var i = 0; i < list.length; i++) {
			var item = list[i];
			select.append('<option value="' + item.code + '">' + item.name
					+ '</option>');
		}
	}

	
	function getAddressCodeLevel() {
		var level = 0;
		var keys = [ 'district', 'city', 'province', 'country' ];
		for (var i = 0; i < keys.length; i++) {
			var s = $(selector + keys[i]).val();
			if (!s) {
				continue;
			}
			return keys.length - i;
		}
		return level;
	}

	function onChanged(_this) {
		var index = parseInt(_this.attr('addressIndex'));
		var thisKey = Region.keys[index];
		var next = Region.keys[index + 1];
		renderSelect(next, null);
		var code = _this.val();
		if (!code) {
			return;
		}
		if (thisKey != 'country') {
			code = $(selector + 'country').val() + '-' + code;
		}
		var address = Region.find(code);
		if (!address) {
			return;
		}
		renderSelect(next, address.children);
	}

	var setAddr = function(level, v) {
		var key = Region.keys[level - 1];
		var container = $(selector + key);
		container.val(v);
		onChanged(container);
	};

	this.setValue = function(v) {
		var addr = v ? Region.find(v) : null;
		if (!addr) {
			return false;
		}
		var addrs = [];
		var top = addr;
		while (true) {
			addrs.unshift(top);
			var pp = top.parent;
			if (!pp) {
				break;
			} else {
				top = pp;
			}
		}
		for (var i = 0; i < addrs.length; i++) {
			setAddr(i + 1, addrs[i].code);
		}
		return true;
	};

	this.getValue = function() {
		var country = $(selector + 'country').val();
		if (!country) {
			return '';
		}
		var keys = [ 'district', 'city', 'province' ];
		for (var i = 0; i < keys.length; i++) {
			var s = $(selector + keys[i]).val();
			if (s) {
				return country + '-' + s;
			}
		}
		return country;
	};
	this.getValueAsObject = function() {
		var o = {};
		for (var i = 0; i < Region.keys.length; i++) {
			var key = Region.keys[i];
			var value = $('#address-' + key).val();
			if (!value) {
				break;
			}
			o[key] = value;
		}
		return o;
	};
	this.getValueLevel = getAddressCodeLevel;
	
	var init = function() {
		for (var i = 0; i < Region.keys.length - 1; i++) {
			var key = Region.keys[i];
			$(selector + key).attr('addressIndex', i);
			$(selector + key).change(function() {
				onChanged($(this));
			});
		}
		renderSelect('country', Region.getAll());
		$(selector + 'country').val('');
	};
	init();
}