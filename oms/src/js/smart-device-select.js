var Devices = function () {
    var initialized = false;
    var devices = [{code: 4,name:"喂食器"}, {code: 5,name:"宠物窝"},{code:6,name:"喂食器Mini"}];  //Fit 1,<PERSON>e 2, <PERSON> 3,Feeder 4,Cozy 5,FeederMini 6
    var load = function () {
        return $.when(devices);
    };

    return {
        load: load
    };
}();

function DeviceSelect(prefix) {
    var selector = '#' + prefix + '-s';
    var depth = 0;
    var listeners = [];

    var checkInit = function () {
        if (!initialized) {
            throw 'Could not do anything else before init completed';
        }
    };

    var getSelect = function (index) {
        var select = $(selector + index);
        return select[0] ? select : null;
    };

    var renderSelect = function (index, list, initValue) {
        var select = getSelect(index);
        for (var i = 0; i < list.length; i++) {
            var item = list[i];
            select.append('<option value="' + item.code + '">' + item.name
                + '</option>');
        }
        if (!mars.isNull(initValue)) {
            select.val(initValue).trigger("change");
        }
    };

    var onChanged = function (select) {
        select = $(select);
        var code = select.val();
        for (var i = 0; i < listeners.length; i++) {
            listeners[i](select, code);
        }
        var index = parseInt(select.attr('data-device-index'));
        var nextIndex = index + 1;
        var next = getSelect(nextIndex);
        if (!next) {
            return;
        }
        renderSelect(nextIndex, null);

    };

    this.setValue = function (v) {
        checkInit();
        getSelect(1).val(v).change();
    };

    this.getValue = function () {
        checkInit();
        for (var i = depth; i >= 1; i--) {
            var select = getSelect(i);
            var code = select.val();
            if (code) {
                return code;
            }
        }
        return null;
    };

    this.init = function (options) {
        if (!options) {
            options = {};
        }
        for (var i = 1; i <= 10; i++) {
            var select = getSelect(i);
            if (!select) {
                break;
            }
            select.attr('data-device-index', i);
            select.change(function () {
                onChanged(this);
            });
            depth++;
        }
        return Devices.load().then(function (devices) {
            renderSelect(1, devices, options.value);
            initialized = true;
        });
    };

    this.addChangeListener = function (listener) {
        checkInit();
        listeners.push(listener);
    };
}