	var LocaleSpec = (function() {
	var _specs = {};
	var _localeList = [];
	var _localeMap = {};

	var loadLocales = function() {
		return App.api('/app/locales').then(function(result) {
			_localeList = result;
			$.each(_localeList, function(index, item) {
				_localeMap[item['key']] = item;
			});
		});
	};

	var showLocaleSpec = function(spec) {
		var container = $('#locale-specs');
		var template = $('#locale-spec-template').html();
		var localeObj = _localeMap[spec['locale']];
		var oldDom = container.find('[data-locale="' + spec['locale'] + '"]')[0];
		if (oldDom) {
			oldDom.remove();
		}
		container.append(U.template(template, {
			spec : spec,
			locale : localeObj
		}));
	};

	var saveLocaleSpec = function() {
		App.closeDialog();
		var dialog = $('#dialog-form');
		var spec = dialog.serializeObject();
		var locale = spec['locale'];
		if (!locale) {
			return;
		}
		_specs[locale] = spec;
		showLocaleSpec(spec);
	};

	var removeLocaleSpec = function(locale) {
		var container = $('#locale-specs');
		container.find('[data-locale="' + locale + '"]').remove();
		delete _specs[locale];
	};

	var setSpecs = function(specs) {
		for ( var locale in specs) {
			var spec = specs[locale];
			if (!spec['locale']) {
				spec['locale'] = locale;
			}
		}
		for ( var locale in specs) {
			showLocaleSpec(specs[locale]);
		}
		_specs = specs;
	};

	var getAllByKeys = function() {
		var kv = {};
		for ( var locale in _specs) {
			var spec = _specs[locale];
			for ( var key in spec) {
				if (key == 'locale') {
					continue;
				}
				var value = spec[key];
				if (!(key in kv)) {
					kv[key] = {};
				}
				kv[key][locale] = value;
			}
		}
		return kv;
	};
	
	var setAllByKeys = function(props) {
		if (!props) {
			return;
		}
		var specs = {};
		for(var propKey in props) {
			var map = props[propKey];
			for (var locale in map) {
				var str = map[locale];
				var spec = specs[locale];
				if (!spec) {
					spec = {};
					specs[locale] = spec;
				}
				spec[propKey] = str;
			}
		}
		setSpecs(specs);
	};

	var getLocaleSpec = function(locale) {
		return _specs[locale];
	};

	var getLocaleList = function() {
		return _localeList;
	};

	return {
		loadLocales : loadLocales,
		getLocaleList : getLocaleList,
		setAll : setSpecs,
		getAll : function() {
			return _specs;
		},
		getAllByKeys : getAllByKeys,
		setAllByKeys : setAllByKeys,
		remove : removeLocaleSpec,
		save : saveLocaleSpec,
		get : getLocaleSpec
	};

})();