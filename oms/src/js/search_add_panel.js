function SearchAddPanel(id, options) {
	options = $.extend({'idKey':'id', 'nameKey': 'name'}, options || {});
	var getControl = function(suffix) {
		return $('#'+ id + '-'+ suffix);
	};
	var selectedItems = [];
	var select = getControl('search-result-select');
	
	var findItem = function(id) {
		for (var i = 0; i < selectedItems.length; i++) {
			var item = selectedItems[i];
			if (item[options.idKey] == id) {
				return i;
			}
		}
		return -1;
	};
	
	var addItem = function(item) {
		selectedItems.push(item);
		var row = $('<div class="alert alert-warning alert-dismissible" role="alert" style="padding:5px 20px;margin-bottom:5px;">'+
				'<button type="button" class="close" data-op-key="'+ item.id +'" aria-label="Close"><span aria-hidden="true">&times;</span></button>' 
				+ item.name + '</div>');
		row.find('button').click(function(){
			var id = $(this).attr('data-op-key');
			$(this).parent().remove();
			removeItem(id);
		});
		getControl('selected-panel').append(row);
	};
	
	var removeItem = function(id) {
		var index = findItem(id);
		if (index >= 0) {
			selectedItems.splice(index, 1);
		}
	};
	
	// init
	var init = function() {
		getControl('add-btn').click(function(){
			select.find('option:selected').each(function(){
				var id = $(this).val();
				var name = $(this).text();
				if (findItem(id) >= 0) {
					return;
				}
				var item = {};
				item[options.idKey] = id;
				item[options.nameKey] = name;
				addItem(item);
			});
		});
		
		getControl('search-btn').click(function(){
			var s = getControl('search-input').val();
			if (s.length == 0) {
				return;
			}
			select.html('');
			var params = {};
			params[options.apiRequestName || 'name'] = s;
			App.api(options.api, params).then(function(result){
				$.each(result, function(i, item){
					select.append('<option value="'+ item[options.idKey] + '">' + item[options.nameKey] + '</option>');
				});
			});
		});
		
		if (options.initApi) {
			App.api(options.initApi, options.initApiParams).then(function(result){
				$.each(result, function(i, item){
					select.append('<option value="'+ item[options.idKey] + '">' + item[options.nameKey] + '</option>');
				});
			});
		}
	};
	

	
	this.getValue = function() {
		return selectedItems;
	};
	
	this.setValue = function(items) {
		if ($.isArray(items)) {
			$.each(items, function(index, item){
				addItem(item);
			});
		} else if (items) {
			addItem(items);
		}
	};
	
	init();
	
};