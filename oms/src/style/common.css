* {
	margin: 0;
	padding: 0;
	border: 0 none;
}
ul,li{
	list-style-type: none; 
	margin:0;
	padding:0;
}
.clear {
	visibility: hidden;
	clear: both;
	width: 1px;
	height: 0;
	font-size: 1px;
	overflow: hidden;
}
.h10{
	height:10px;
}
.h20{
	height:20px;
}
.h30{
	height:30px;
}
.h40{
	height:40px;
}
.none{
	display:none !important;
}
.block {
	display: block !important;
}
.fl {
	float: left;
}
.fr {
	float: right;
}

#loading-indicator-mask {
	position: fixed;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	background: #CCC;
	opacity: 0.5;
	filter: alpha(opacity =  50);
	z-index: 65535;
	_position: absolute;
    _top: expression(eval(document.documentElement.scrollTop));
}

#loading-indicator {
	position: absolute;
	width: 50px;
	height: 50px;
	left: 50%;
	top: 50%;
	margin: -25px 0 0 -25px;
	background: url(../img/loading.gif) no-repeat;
}

.tclose {
	position: absolute;
	top: 0px;
	right: 0px;
	width: 30px;
	height: 30px;
	cursor: pointer;
	background: url(../img/close.png) no-repeat;
}
.tclose:hover {
	background-position: 0 -30px;
}

.x-panel {
}

.x-table {
	word-break: break-all;
	font-size: 95%;
	border:1px solid #ddd;
}
.x-table th{
	background-color:#F5F6FA;
	color:#999;
}
.x-table .vertical-bar{
	color:#ccc;
	margin:0 4px;
}
.x-table td img{
	width: 50px;
	height: 50px;
}
.x-table td.ops a {
	margin-right: 3px;
}
.x-table-none{
	margin:20px 0 0 0;
	text-align:center;
	color:#888;
	font-size:16px;
}
.x-table tr.template {
	display: none;
}

.locale-spec {
	border-top: 1px dashed #CCC;
	margin-top: 10px;
	padding-top: 10px;
}
.locale-spec-item span {
	padding-left: 5px;
}
.locale-spec-item .edit {
	margin-left: 10px;
}
#locale-spec-template {
	display: none;
}

:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color: #f00;  
}

::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: #f00;
}

.region-select-container {
	padding: 5px 0;
}
.region-select-container select.form-control {
	display: inline-block;
	width: 15rem;
}

/** piaohao start */
/** 设置input placeholder */
.form-control::-webkit-input-placeholder { color:#ddd; }
.form-control::-moz-placeholder { color:#ddd; } /* firefox 19+ */
.form-control:-ms-input-placeholder { color:#ddd; } /* ie */
.form-control:-moz-placeholder { color:#ddd; }

/**主页样式*/

.header{height:50px;background-color:#09c;color:#fff;}
.header .brand-wrapper{float:left;margin:10px 10px;}
.header .brand{color:#FFF;font-size:20px;text-decoration:none;}
.header .topnav{float:right;margin:10px 10px;}
.header a.dropdown-toggle{color: #FFF;}

/* 
.header .topnav .btn-group{position:relative;display:inline-block;vertical-align:middle;}
.header .topnav .btn-logout{background-color:#ee465a;border-color:#c11a39;color:#fff;}
 */

.left-wrapper .nav{width:235px;}
.left-wrapper a{background-color:#293038;color:#fff;}
.left-wrapper ul li a:hover{background-color:#37424f;color:#fff;}
.left-wrapper ul li a:focus{background-color:#37424f;color:#fff;}
.left-wrapper li.open{background-color:#0099cc;color:#fff;}
.left-wrapper li.open a{background-color:#0099cc;color:#fff;}
.left{position:fixed;margin-top:0;overflow-x:hidden;height:100%;width:235px;}
.left .left-wrapper{width:250px;height:94%;overflow-y:scroll;background-color:#293038;padding-top:10px;}
.left ul li {border-bottom:0;}

.center{position:absolute;top:50px;width:100%;bottom:0;}
.center #page-wrapper{position:absolute;left:-15px;top:0;right:0;bottom:0;width:auto;min-height:0;overflow-y:auto;}

/* piaohao end */