var zh_CN = {
  /** common **/
  'network.error': '网络错误，请刷新重试',
  ok: '确定',
  cancel: '取消',
  yes: '是',
  no: '否',
  create: '创建',
  add: '添加',
  edit: '编辑',
  save: '保存',
  delete: '删除',
  login: '登录',
  logout: '退出',
  back: '返回',
  done: '操作成功',
  username: '用户名',
  password: '密码',
  'password.update': '修改密码',
  'password.old': '旧密码',
  'password.new': '新密码',
  'password.new.retype': '再输入一次新密码',
  'password.recover': '找回密码',
  'pagination.lastpage': '上一页',
  'pagination.nextpage': '下一页',
  search: '搜索',
  operations: '操作',
  'confirm.operation': '确定要执行这个操作吗？',
  'confirm.delete': '确定要删除吗？',
  'prompt.input': '请输入：{{0}} 命令来执行此操作',
  locale: '语言',
  localize: '本地化内容',
  file: '文件',
  md5File: 'md5文件',
  'file.selector': '+选择上传文件',
  download: '下载',
  id: 'ID',
  name: '名称',
  title: '标题',
  detail: '详细',
  image: '图片',
  type: '类型',
  link: '链接',
  privilege: '私有',
  createdat: '创建于',
  createdby: '创建者',
  priority: '优先级',
  userGroup: '目标人群',
  'priority.tip': '优先级越低排在越前面',
  'index.title': 'Petkit运营管理系统',
  'index.welcome': '欢迎来到Petkit运营管理系统',

  /** menu **/
  'menu.user': '用户',
  'menu.user.family': '家庭',
  'menu.user.activate': '用户激活',
  'menu.user.cs': '客服',
  'menu.user.doctor': '医生',
  'menu.user.blacklist': '黑名单',
  'menu.user.whitelist': '白名单',

  'menu.community': '社区',
  'menu.community.post': '贴子',
  'menu.community.topic': '话题',
  'menu.community.buylink': '购买链接',
  'menu.community.copyWriting': '文案',
  'menu.community.fword': '敏感词',
  'menu.community.pushlink': '创建推送',
  'menu.community.pushsms': '推送短信',
  'menu.community.appeal': '申诉',
  'menu.community.report': '举报',
  'menu.community.tag': '标签',

  'menu.push': '推送',
  'menu.push.content': '内容',

  'menu.pet': '宠物',
  'menu.pet.breed': '品种',
  'menu.pet.food': '宠粮',
  'menu.pet.privatefood': '定制宠粮',
  'menu.pet.foodBrand': 'APP主粮品牌管理页面',
  'menu.pet.foodSpec': 'APP主粮规格管理页面',
  'menu.pet.photoIdentify': '照片识别',

  'menu.schedule': '提醒',

  'menu.fit': 'Fit',
  'menu.fit.device': '设备',
  'menu.fit.firmware': '固件',
  'menu.fit.data': '数据',
  'menu.fit.issue': '问题',
  'menu.fit.warmtips': '温馨提示',

  'menu.mate': 'Mate',
  'menu.mate.firmware': '固件',
  'menu.mate.release': '发布版本',
  'menu.mate.log': 'Log',
  'menu.mate.sn': 'SN',
  'menu.mate.module': '模块',
  'menu.mate.logmodule': 'Log模块',
  'menu.mate.areaconfig': '地区配置',
  'menu.mate.sipserver': 'Sip服务器',
  'menu.mate.relayserver': 'Relay服务器',

  'menu.go': 'Go',
  'menu.go.device': '设备',
  'menu.go.firmware': '固件',

  'menu.feeder': '喂食器',
  'menu.feeder.device': '设备',
  'menu.feeder.sn': '序列号',
  'menu.feeder.module': '模块',
  'menu.feeder.firmware': '固件版本',
  'menu.feeder.firmwaredetail': '固件模块包',
  'menu.feeder.log': 'Log',

  'menu.hg': '烘干箱',
  'menu.hg.device': '设备',
  'menu.hg.sn': '序列号',
  'menu.hg.module': '模块',
  'menu.hg.firmware': '固件',
  'menu.hg.firmwaredetail': '固件模块包',
  'menu.hg.log': 'Log',

  'menu.cozy': '宠物窝',
  'menu.cozy.device': '设备',
  'menu.cozy.sn': '序列号',
  'menu.cozy.module': '模块',
  'menu.cozy.firmware': '固件版本',
  'menu.cozy.firmwaredetail': '固件模块包',
  'menu.cozy.log': 'Log',

  'menu.feedermini': '喂食器Mini',
  'menu.feedermini.device': '设备',
  'menu.feedermini.sn': '序列号',
  'menu.feedermini.module': '模块',
  'menu.feedermini.firmware': '固件版本',
  'menu.feedermini.firmware.singapore': '新加坡固件版本',
  'menu.feedermini.firmwaredetail': '固件模块包',
  'menu.feedermini.log': 'Log',

  'menu.t3': 'T3',
  'menu.t3.device': '设备',
  'menu.t3.sn': '序列号',
  'menu.t3.module': '模块',
  'menu.t3.firmware': '固件版本',
  'menu.t3.firmware.singapore': '新加坡固件版本',
  'menu.t3.firmwaredetail': '固件模块包',
  'menu.t3.log': 'Log',

  //h3模块
  'menu.h3': 'H3',
  'menu.h3.device': '设备',
  'menu.h3.sn': '序列号',
  'menu.h3.module': '模块',
  'menu.h3.firmware': '固件版本',
  'menu.h3.firmwaredetail': '固件模块包',

  // t4模块
  'menu.t4': 'T4',
  'menu.t4.device': '设备',
  'menu.t4.sn': '序列号',
  'menu.t4.module': '模块',
  'menu.t4.firmware': '固件版本',
  'menu.t4.firmware.singapore': '新加坡固件版本',
  'menu.t4.firmwaredetail': '固件模块包',

  // t5模块
  'menu.t5': 'T5',
  'menu.t5.device': '设备',
  'menu.t5.sn': '序列号',
  'menu.t5.module': '模块',
  'menu.t5.firmware': '固件版本',
  'menu.t5.firmware.singapore': '新加坡固件版本',
  'menu.t5.firmwaredetail': '固件模块包',

  // t6模块
  'menu.t6': 'T6',
  'menu.t6.device': '设备',
  'menu.t6.sn': '序列号',
  'menu.t6.module': '模块',
  'menu.t6.firmware': '固件版本',
  'menu.t6.firmware.singapore': '新加坡固件版本',
  'menu.t6.firmwaredetail': '固件模块包',
  'menu.t6.rubbishBoxNFCList': '垃圾袋盒管理',
  'menu.t6.NFCAccountList': '垃圾袋盒查询',
  'menu.t6.rubbishBoxList': '垃圾袋盒',

  // t7模块
  'menu.t7': 'T7',
  'menu.t7.device': '设备',
  'menu.t7.sn': '序列号',
  'menu.t7.module': '模块',
  'menu.t7.firmware': '固件版本',
  'menu.t7.firmware.singapore': '新加坡固件版本',
  'menu.t7.firmwaredetail': '固件模块包',
  'menu.t7.crystalTrayCatLitter': '水晶猫砂盘',
  'menu.t7.crystalTrayCatLitterList': '水晶猫砂盘',
  'menu.t7.crystalTrayCatLitterManagementList': '水晶猫砂盘管理',
  'menu.t7.crystalTrayCatLitterNFCAccountList': '水晶猫砂盘查询',

  'menu.k2': 'K2',
  'menu.k2.device': '设备',
  'menu.k2.sn': '序列号',
  'menu.k2.module': '模块',
  'menu.k2.firmware': '固件版本',
  'menu.k2.firmware.singapore': '新加坡固件版本',
  'menu.k2.firmwaredetail': '固件模块包',
  'menu.k2.log': 'Log',

  'menu.k3': 'K3',
  'menu.k3.sn': '序列号',
  'menu.k3.device': '设备',
  'menu.k3.firmware': '固件',
  'menu.k3.log': 'Log',

  'menu.aq': 'Aq',
  'menu.aq.sn': '序列号',
  'menu.aq.device': '设备',
  'menu.aq.firmware': '固件',
  'menu.aq.log': 'Log',

  'menu.aqr': 'Aqr',
  'menu.aqr.sn': '序列号',
  'menu.aqr.device': '设备',
  'menu.aqr.firmware': '固件',
  'menu.aqr.log': 'Log',

  //aqh1模块
  'menu.aqh1': 'AqH1',
  'menu.aqh1.device': '设备',
  'menu.aqh1.sn': '序列号',
  'menu.aqh1.module': '模块',
  'menu.aqh1.firmware': '固件版本',
  'menu.aqh1.firmware.singapore': '新加坡固件版本',
  'menu.aqh1.firmwaredetail': '固件模块包',

  'menu.d3': 'D3',
  'menu.d3.device': '设备',
  'menu.d3.sn': '序列号',
  'menu.d3.module': '模块',
  'menu.d3.firmware': '固件版本',
  'menu.d3.firmware.singapore': '新加坡固件版本',
  'menu.d3.firmwaredetail': '固件模块包',
  'menu.d3.log': 'Log',
  'menu.d3.sound_firmwares': '音频固件包',
  'menu.d3.sound_firmwares.singapore': '新加坡音频固件包',
  'menu.d3.firmwaredetail': '固件模块包',
  'menu.d3.log': 'Log',
  'menu.d3.sound_firmwares': '音频固件包',

  'menu.d4': 'D4',
  'menu.d4.device': '设备',
  'menu.d4.sn': '序列号',
  'menu.d4.module': '模块',
  'menu.d4.firmware': '固件版本',
  'menu.d4.firmware.singapore': '新加坡固件版本',
  'menu.d4.firmwaredetail': '固件模块包',
  'menu.d4.log': 'Log',

  'menu.d4s': 'D4s',
  'menu.d4s.device': '设备',
  'menu.d4s.sn': '序列号',
  'menu.d4s.module': '模块',
  'menu.d4s.firmware': '固件版本',
  'menu.d4s.firmware.singapore': '新加坡固件版本',
  'menu.d4s.firmwaredetail': '固件模块包',

  'menu.d4h': 'D4h',
  'menu.d4h.device': '设备',
  'menu.d4h.sn': '序列号',
  'menu.d4h.module': '模块',
  'menu.d4h.firmware': '固件版本',
  'menu.d4h.tencent.firmware': '腾讯固件版本',
  'menu.d4h.firmwaredetail': '固件模块包',
  'menu.d4h.logs': 'Log',

  'menu.d4h.2': 'D4h-2',
  'menu.d4h.2.device': '设备',
  'menu.d4h.2.sn': '序列号',
  'menu.d4h.2.module': '模块',
  'menu.d4h.2.firmware': '固件版本',
  'menu.d4h.2.tencent.firmware': '腾讯固件版本',
  'menu.d4h.2.firmwaredetail': '固件模块包',
  'menu.d4h.2.logs': 'Log',

  'menu.d4sh': 'D4sh',
  'menu.d4sh.device': '设备',
  'menu.d4sh.sn': '序列号',
  'menu.d4sh.module': '模块',
  'menu.d4sh.firmware': '固件版本',
  'menu.d4sh.tencent.firmware': '腾讯固件版本',
  'menu.d4sh.firmwaredetail': '固件模块包',
  'menu.d4sh.sound.firmwares': '音频固件包',
  'menu.d4sh.logs': 'Log',

  'menu.d4sh.2': 'D4sh-2',
  'menu.d4sh.2.device': '设备',
  'menu.d4sh.2.sn': '序列号',
  'menu.d4sh.2.module': '模块',
  'menu.d4sh.2.firmware': '固件版本',
  'menu.d4sh.2.tencent.firmware': '腾讯固件版本',
  'menu.d4sh.2.firmwaredetail': '固件模块包',
  'menu.d4sh.2.sound.firmwares': '音频固件包',
  'menu.d4sh.2.logs': 'Log',

  'menu.p3': 'P3',
  'menu.p3.device': '设备',
  'menu.p3.sn': '序列号',
  'menu.p3.firmware': '固件',
  'menu.p3.data': '数据',
  'menu.p3.issue': '问题',
  'menu.p3.warmtips': '温馨提示',

  'menu.h2': 'H2',
  'menu.h2.device': '设备',

  'menu.w5': 'W5',
  'menu.w5.sn': '序列号',
  'menu.w5.device': '设备',
  'menu.w5.firmware': '固件',
  'menu.w5.log': 'Log',

  'menu.ctw3': 'CTW3',
  'menu.ctw3.device': '设备',
  'menu.ctw3.sn': '序列号',
  'menu.ctw3.firmware': '固件',

  'menu.r2': 'R2',
  'menu.r2.sn': '序列号',
  'menu.r2.device': '设备',
  'menu.r2.firmware': '固件',
  'menu.r2.log': 'Log',

  'menu.app': 'App',
  'menu.app.log': 'AppLog',
  'menu.app.apilog': 'ApiLog',
  'menu.app.apiuser': 'ApiLog用户',
  'menu.app.link': '相关链接',
  'menu.app.release': '版本发布',
  'menu.app.alert': '规则提醒',
  'menu.app.module': '运营模块',

  'menu.coin': '积分商城',
  'menu.coin.gift': '礼品',
  'menu.coin.coupon': '优惠券',

  'menu.im': '消息',
  'menu.im.emotion': '表情',
  'menu.im.node': '服务器节点',
  'menu.im.csconfig': '客服配置',

  'menu.admin': '管理员',
  'menu.admin.group': '管理组',

  'menu.system': '系统',
  'menu.singapore.system': '新加坡系统',
  'menu.system.config': '配置',
  'menu.system.apikey': 'ApiKey',
  'menu.system.localebundle': '字串',
  'menu.system.appDeviceInfo': 'App设备',
  'menu.system.tipInfo': '提示',
  'menu.system.licenseBatch': '声网批次管理',

  'menu.lock': '仓库',
  'menu.lock.lockarea': '出库记录上传',
  'menu.lock.lockareas': '出库记录',
  'menu.lock.material': '物料管理',
  'menu.lock.device': '锁机管理',

  // 商城菜单
  'menu.mall': '营销内容管理',
  'menu.mall.marketingContent': '推广标贴',
  'menu.mall.standardEntry': '标准入口',
  'menu.mall.productManagement': '商品管理',
  'menu.mall.virtualDevice': '虚拟设备',
  'menu.mall.purchaseEntry': '购买入口',
  'menu.community.banner': 'Banner',
  'menu.community.appbanner': 'AppBanner',
  'menu.community.appbanner.old': 'AppBanner(老)',
  'menu.community.splash': '启动页',
  'menu.user.group': '用户分群',

  // 商业化服务菜单
  'menu.business': '商业化服务',
  'menu.business.cloudServiceSku': 'SKU管理',
  'menu.business.skuCapacity': '设备能力',
  'menu.business.skuBenefit': '权益管理',
  'menu.business.order': '订单管理',
  'menu.business.subscription': '订阅管理',
  'menu.business.cloudService': '服务管理',
  'menu.business.cloudServiceV2': '服务管理(新)',
  'menu.business.appVersionVerify': '审核版本管理',
  'menu.business.warnEvents': '云存事件',
  'menu.business.benefitCompare': '特权对比',
  'menu.business.operationPackage': '活动套餐管理',
  'menu.business.aiVideoFeedback': 'AI视频反馈',
  'menu.business.dressUp': '个性装扮管理',
  'menu.business.virtualCard': '卡券管理',
  'menu.business.virtualCardCode': '卡号管理',
  'menu.business.currencyList': '货币列表',
  'menu.business.toolbox': '工具箱',
  'menu.business.history': '退改发操作记录',
  'menu.business.history.refund': '订单退款',
  'menu.business.history.changeExpiration': '订单更改效期 ',
  'menu.business.history.distribution': '虚拟卡发放',

  // 隐私协议相关配置
  'menu.protocols': '隐私协议',
  'menu.protocols.private': '隐私协议',

  // 活动相关
  'menu.activity': '活动',
  'menu.activity.ai.collaboration': 'AI共创',
  'menu.activity.ai.collaboration.management': 'AI共创管理',
  'menu.activity.ai.collaboration.review': '审核',
  'menu.activity.content': '素材',
  'menu.activity.content.upload': '上传素材',
  'menu.activity.content.award': '活动奖励',

  /** page spec **/
  'p.user.search.tip': '用户ID/手机号/邮箱/昵称',
  'p.user.id': '用户ID',
  'p.user.avatar': '头像',
  'p.user.nick': '昵称',
  'p.user.gender': '性别',
  'p.user.createdat': '注册时间',
  'p.user.locality': '地区',
  'p.user.birth': '生日',
  'p.user.growth': '成长值',
  'p.user.familyCount': '家庭(个)',
  'p.user.petCount': '宠物(只)',
  'p.user.deviceCount': '设备(台)',
  'p.user.pet': '宠物',
  'p.user.dev': '设备',
  'p.user.dev.history': '设备绑定历史',
  'p.user.applog': 'AppLog',
  'p.user.loginhistory': '登录历史',
  'p.user.coin': '积分',
  'p.user.account': '账号',
  'p.user.update.nick': '修改昵称',
  'p.user.update.nick.prompt': '请输入"{{0}}"的新昵称',
  'p.user.post': '贴子',
  'p.user.gender.1': '男',
  'p.user.gender.2': '女',
  'p.user.gender.3': '自定义',
  'p.user.renewcode': '刷新验证码',
  'p.user.coin.confirm': '刷新验证码',
  'p.user.please.input.username': 'Please input email or mobile of user',

  'p.community.post': '贴子',
  'p.community.topic': '话题',
  'p.community.roster.latest': '最新的',
  'p.community.roster.recommend': '推荐的',
  'p.community.roster.official': '官方的',
  'p.community.make.recommended': '推荐★',
  'p.community.remove.recommended': '取消推荐',
  'p.community.recommend.confirm': '确定要推荐吗？',
  'p.community.remove.recommended.confirm': '确定要取消推荐吗？',
  'p.community.make.hidden': '屏蔽',
  'p.community.remove.hidden': '取消屏蔽',
  'p.community.hide.confirm': '确定要屏蔽贴子吗？',
  'p.community.remove.hidden.confirm': '确定要取消屏蔽贴子吗？',
  'p.community.make.official': '设为官方☆',
  'p.community.remove.official': '取消官方',
  'p.community.update.tag': '更改标签',
  'p.community.remove.from.plaza': '从广场中移除',
  'p.community.stick': '置顶',
  'p.community.topic.name': '话题名称',
  'p.community.topic.visit.and.partake': '访问/参与',
  'p.community.banner.type.1': '贴子',
  'p.community.banner.type.2': '外链',
  'p.community.banner.type.3': '话题',
  'p.community.banner.createlink': '创建链接Banner',
  'p.community.set.as.banner': '设为Banner	',
  'p.community.cover': '封面',
  'p.community.set.as.cover': '设为封面',
  'p.community.recommend.and.hide': '推荐/屏蔽/公开',
  'p.community.short.video': '短视频',
  'p.community.text': '文字',
  'p.community.push': '推送',
  'p.community.push.sms': '推送短信',
  'p.community.push.text': '推送文本',
  'p.community.push.post': '推送贴子',
  'p.community.push.topic': '推送话题',
  'p.community.push.web': '推送链接',
  'p.community.add.region': '+添加地区',
  'p.community.add.device': '+添加设备',
  'p.community.add.mobile': '+添加机型',
  'p.community.schedule.push.at': '预约发送时间',
  'p.community.push.please.select.region': '请选择地区',
  'p.community.push.please.set.time':
    '推送时间应该在7：00 ~ 21：00之内，请设置预约发送时间',
  'p.community.splash.please.set.time': '生效持续时间应不大于7天',

  __: '',
};

mars.l10n.put('zh_CN', zh_CN);
localStorage.menuNameList = JSON.stringify(zh_CN);
